from time import sleep
from multiprocessing import Pool, cpu_count
from typing import List

import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from src.helper_funcs import (
    fast_write_sql,
    fast_read_sql,
    get_sql_array_str,
    get_cloud_logger,
    cloud_log_text,
    cloud_log_struct,
    check_existance,
)
from src.data_collection.wyscout.v2.AdvancedStatsPrepper import (
    prep_advanced_stats,
)


class AdvancedStatsPrepOrchestrator:
    def __init__(self, batch_size, advanced_stats_prepper):
        self.batch_size = batch_size
        self.advanced_stats_prepper = advanced_stats_prepper
        self.cnx_prod = create_engine(postgres_prod_str)
        self.collection_list = None
        self.cpu = cpu_count()
        self.logger = get_cloud_logger()

    def loop(self):
        while True:
            # give a 1 min leeway for the first check in case something fucks up with the other script
            sleep(60)
            # this applies to the first run: if there is no events table yet,
            #  create we skip everything and wait for it

            collection_active = self.check_collection_runs()
            if not check_existance(self.cnx_prod, "events", "wyscout"):
                if not collection_active:
                    break
                continue
            self.get_games_for_deriving()
            # if there is nothing yet for collection, wait:
            if len(self.collection_list) == 0:
                if not collection_active:
                    break
                continue  # run each batch:
            for i in range(0, len(self.collection_list), self.batch_size):
                cloud_log_text(self.logger, f"Start of batch {i}")
                batch = self.collection_list[i : i + self.batch_size]
                matches = self.load_batch(batch)
                pool = Pool(processes=self.cpu)
                res = pool.map(prep_advanced_stats, matches)
                cloud_log_text(self.logger, f"End of batch {i}")
                pool.close()
                pool.join()
                df_list = [
                    x["grouped_df"]
                    for x in filter(lambda x: x is not None, res)
                ]
                if not df_list:
                    fucked_ids = batch
                else:
                    df = pd.concat(df_list)
                    fucked_ids = [
                        x for x in batch if x not in df["matchId"].values
                    ]
                if fucked_ids:
                    cloud_log_struct(
                        self.logger,
                        {
                            "action": (
                                "problematic ids in advanced stats derivation"
                            ),
                            "ids": fucked_ids,
                        },
                    )
                if df_list:
                    fast_write_sql(
                        df,
                        self.advanced_stats_prepper.table_name,
                        self.cnx_prod,
                        if_exists="append",
                        schema="wyscout",
                    )

            # if the collection is no longer running and we have derived everything we need, quit the loop:
            if not collection_active:
                break

    def check_collection_runs(self):
        """Check if there is match objects collection at the moment, 1 means yes, 0 - no

        Returns:
            bool: is the collection active
        """
        collection_active = self.cnx_prod.execute(
            "SELECT status FROM meta.matches_collection_status"
        ).first()[0]
        return False if collection_active == 0 else 1

    def get_games_for_deriving(self):
        """Get games that are present in events table but not in the advanced_stats table"""
        try:
            self.collection_list = fast_read_sql(
                f"""SELECT ev."matchId" 
                                    FROM  wyscout.events ev
                                    WHERE ev."matchId" NOT IN (
                                        SELECT DISTINCT "matchId" 
                                        FROM wyscout.advanced_stats
                                    )""",
                self.cnx_prod,
            )["matchId"].tolist()
        except:
            self.collection_list = fast_read_sql(
                f"""SELECT "matchId" FROM wyscout.events """,
                self.cnx_prod,
            )["matchId"].tolist()

    def load_batch(self, ids_list: List[int]) -> list:
        """Load a batch from events that is to be prepped

        Args:
            ids_list (List[int]): list of match ids to be loaded

        Returns:
            list: list of events objects
        """
        sql_ids = get_sql_array_str(ids_list)
        return fast_read_sql(
            f'SELECT events FROM wyscout.events WHERE "matchId" IN {sql_ids}',
            self.cnx_prod,
        )["events"].tolist()
