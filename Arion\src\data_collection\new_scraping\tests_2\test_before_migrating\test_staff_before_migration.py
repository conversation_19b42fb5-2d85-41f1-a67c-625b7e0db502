import unittest
import pandas as pd
from sqlalchemy.engine import create_engine

from src.helper_funcs import fast_read_sql


class TestPlayerBeforeMigration(unittest.TestCase):
    engine = create_engine(
        "***********************************************************/wyscout_raw_production"
    )
    with engine.begin() as con:
        df = fast_read_sql(
            """Select * from meta_scraping.raw_staff_data""", engine
        )
    migr_rows = len(df.index)

    def test_team_ids(self):
        team_ids = [i for i in self.df["current_team_id"] if pd.isnull(i)]
        self.assertEqual(
            0,
            len(team_ids),
            f"There are {len(team_ids)} teams/s with NULL id/s",
        )
