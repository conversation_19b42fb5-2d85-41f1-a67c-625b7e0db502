import json
import re
import time
from datetime import datetime

import requests

headers = {
  'authority': 'fb.oddsportal.com',
  'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
  'sec-ch-ua-mobile': '?1',
  'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Mobile Safari/537.36',
  'sec-ch-ua-platform': '"Android"',
  'accept': '*/*',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'no-cors',
  'sec-fetch-dest': 'script',
  'referer': 'https://www.oddsportal.com/',
  'accept-language': 'en-US,en;q=0.9',
  'cookie': '_gid=GA1.2.1535347184.1640001592; _ga=GA1.2.471828136.1640001592; _gat_UA-821699-19=1; _ga_5YY4JY41P1=GS1.1.1640086973.5.0.1640086981.0'
}

bookies_codes = {"1XBet": 417, "bet365": 16, "bwin": 2, "GGBet": 550}

def get_response(url: str) -> str:
    return requests.get(url, headers=headers).text

print(get_response)
time_now_s = int(time.time())
time_now_ms = int(round(time.time() * 1000))
odds_data_js = f"https://fb.oddsportal.com/ajax-sport-country-tournament/1/tdkpynmB/X0/1/?_={time_now_ms}"

print(re.findall(r"\s({.*})", get_response(odds_data_js))[0])
odds_data = json.loads(re.findall(r"\s({.*})", get_response(odds_data_js))[0])

bookies_odds = odds_data['d']['oddsData']['OOb0zFK5']['odds']  # current odds for a bookie
print([oxt['avg'] for oxt in bookies_odds])
 # sorted as on the website 1 - X - 2

# odds history for the X column for a given bookie
#for item in history_data:
    #value, _, timestamp = item
    #print(f"{datetime.fromtimestamp(timestamp)} - {value}")