from abc import ABC, abstractmethod
from typing import Union, List

from src.data_collection.wyscout.v2.Updater import Updater
from src.data_collection.wyscout.v2.SeasonsIdHandler import (
    ActiveSeasonsIdHandler,
    AllSeasonsIdHandler,
)


class SeasonsXUpdater(Updater, ABC):
    def __init__(self, table_name, id_name, only_active=True):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/teams"
        self.object_type = self.base_url.split("/")[-1]
        self.id_name = id_name
        self.only_active = only_active
        if self.only_active:
            self.id_handler = ActiveSeasonsIdHandler(
                table_name, id_name, self.cnx_prod
            )
        else:
            self.id_handler = AllSeasonsIdHandler(
                table_name, id_name, self.cnx_prod
            )

    @abstractmethod
    def format_response(self, payload, code):
        pass

    def get_objects_for_collection(self, source_table):
        self.collection_list = self.id_handler.get_objects_for_collection(
            source_table
        )

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[None, List[dict]]:
        payload = Updater.extract_payload_from_resp(self, resp, code)
        if payload is None:
            return None
        return self.format_response(payload, code)

    async def get_prepped_objects(self):
        results = await self.collect()
        if results is None:
            return
        seasons_obj, obj = self.prep(results)
        return seasons_obj, obj

    async def loop(self):
        res = await self.get_prepped_objects()
        if res is None:
            return
        seasons_obj, obj = res
        if self.id_handler.collection_list is None:
            if self.collection_list is None:
                raise Exception("No collection list was generated")
            else:
                # pass on the collection list if this was generated by an orchestrator:
                self.id_handler.collection_list = self.collection_list
        return self.id_handler.handle_output(seasons_obj, obj)
