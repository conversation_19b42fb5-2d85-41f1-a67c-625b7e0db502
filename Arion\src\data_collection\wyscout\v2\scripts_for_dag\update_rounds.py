
from time import sleep 
import pandas as pd

from sqlalchemy import create_engine
from settings import postgres_prod_str
from src.helper_funcs import get_sql_array_str, get_wyscout_response, fast_write_sql


def main():
    engine = create_engine(postgres_prod_str)
    API_URL = "https://apirest.wyscout.com/v3/rounds/"
    competitions = (108, 82, 109, 84, 54, 31, 64, 43318)
    df = pd.read_sql(
        f""" select distinct("roundId") from wyscout.matches 
            where "competitionId" in {get_sql_array_str(competitions)}
            and "date"::timestamp > (NOW() - interval '2 years') """,
        engine,
    )
    dd_rounds = {
        "roundId": [],
        "name": [],
        "type": [],
        "startDate": [],
        "endDate": [],
        "competitionId": [],
        "seasonId": [],
    }
    for round_id in df["roundId"]:
        response = get_wyscout_response(f"{API_URL}{round_id}")
        if response["name"] in [
            "Group Stage",
            "16th Finals",
            "Quarter-Finals",
            "Semi-Finals",
            "Final",
            "8th Finals",
            "Finals",
            "Quarter-Finals ",
            "Semi-Finals ",
        ]:
            for column in dd_rounds:
                if column == "roundId":
                    dd_rounds[column].append(response["wyId"])
                elif column == "name":
                    dd_rounds[column].append(response[column].strip())
                else:
                    dd_rounds[column].append(response[column])
            sleep(0.1)
    df_rounds = pd.DataFrame(dd_rounds)
    fast_write_sql(
        df_rounds,
        "rounds",
        cnx=engine,
        schema="wyscout",
        if_exists="replace",
    )


if __name__ == "__main__":
    main()