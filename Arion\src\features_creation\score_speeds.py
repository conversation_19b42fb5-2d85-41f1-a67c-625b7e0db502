import pandas as pd
import numpy as np
import joblib
import pickle
import math
from sqlalchemy import create_engine
from datetime import datetime
from settings import postgres_prod_str, postgres_dev_str


class SpeedScorer:
    def __init__(
        self,
        model_path,
        score_cols_path,
        cnx_prod,
        cnx_dev,
        raw_speeds_table,
        residual_speeds_table,
    ):
        self.model_path = model_path
        self.score_cols_path = score_cols_path
        self.df = None
        self.X = None
        self.y = None
        self.y_pred = None
        self.model = None
        self.score_cols = None
        self.cnx_dev = cnx_dev
        self.cnx_prod = cnx_prod
        self.raw_speeds_table = raw_speeds_table
        self.residual_speeds_table = residual_speeds_table
        self.export_cols = ["playerId", "model_speed", "date"]

    def load_model(self):
        self.model = joblib.load(self.model_path)

    def load_score_cols(self):
        with open(self.score_cols_path, "rb") as fp:
            self.score_cols = pickle.load(fp)

    def load_data(self, batch_start, batch_end):
        self.df = pd.read_sql(
            f"""SELECT *
                                FROM player_speeds
                                ORDER BY "playerId", "matchId", "eventSec"
                                OFFSET {batch_start}
                                LIMIT {batch_end} """,
            self.cnx_dev,
        )  # read from cnx_dev because prod will not be migrated at that point
        self.X = self.df.copy()

    def prep_data_for_scoring(self):
        self.X["angle"] = (abs(self.X.y_end - self.X.y_start) * 0.45) / (
            abs(self.X.x_end - self.X.x_start) * 0.90
        ).apply(lambda x: math.atan(x))

        self.X["time_elapsed"] = np.where(
            self.X["matchPeriod"] <= 2,
            ((self.X["matchPeriod"] - 1) * 2700 + self.X["eventSec"]) / 60,
            (5400 + (self.X["matchPeriod"] - 3) * 900 + self.X["eventSec"]) / 60,
        )
        self.X["speed_count"] = self.X.groupby("playerId")["speed"].transform("count")
        self.X = self.X[self.X.angle < np.inf]
        self.X = self.X[
            [
                "x_end",
                "y_end",
                "time_elapsed",
                "next_event",
                "prev_event",
                "angle",
                "speed",
            ]
        ]
        self.X = pd.get_dummies(self.X, columns=["prev_event", "next_event"])
        self.y = self.X.pop("speed")
        try:
            self.X = self.X[self.score_cols]
        except:
            missing_cols = [x for x in self.score_cols if x not in self.X]
            for col in missing_cols:
                print(f"Missing column: {col}")
                self.X[col] = 0
            self.X = self.X[self.score_cols]

    def score_data(self):
        self.y_pred = self.model.predict(self.X)

    def prep_data_for_writing(self):
        self.df["model_speed"] = self.y - self.y_pred

    def wipe_table(self):
        pd.DataFrame(columns=self.export_cols).to_sql(
            self.residual_speeds_table,
            self.cnx_dev,
            if_exists="replace",
            method="multi",
        )

    def write_scored_data(self):
        self.df[self.export_cols].to_sql(
            self.residual_speeds_table, self.cnx_dev, if_exists="append", method="multi"
        )


def main():
    cnx_dev = create_engine(postgres_dev_str)
    cnx_prod = create_engine(postgres_prod_str)

    scorer = SpeedScorer(
        "src/models/speed/20200805_09_07_00.joblib",
        "src/models/speed/scoring_cols.pkl",
        cnx_prod,
        cnx_dev,
        "player_speeds",
        "player_model_speeds",
    )
    scorer.load_model()
    scorer.load_score_cols()

    # batching this bitch becaues to_sql just implodes vm's fucking ram:
    batch_n = 5
    cnt_row = cnx_prod.execute("SELECT COUNT(*) FROM player_speeds").first()[0]
    batch_size = int(np.ceil(cnt_row / batch_n))
    for n in range(batch_n):
        print(f"{datetime.now()}: Starting batch {n+1}")
        scorer.load_data(n * batch_size, batch_size)
        scorer.prep_data_for_scoring()
        scorer.score_data()
        scorer.prep_data_for_writing()
        scorer.write_scored_data()
        print(f"{datetime.now()}: Finished batch {n+1}")


if __name__ == "__main__":
    main()