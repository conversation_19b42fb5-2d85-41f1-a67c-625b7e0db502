from datetime import datetime
from src.reporting.ritni_top.RitniReporter import <PERSON>itniReporter
from src.reporting.Mailer import Mailer, MailConfigOptions
from src.helper_funcs import get_time_stamp


def main():
    # mail only on Sun-Tue:
    if datetime.today().weekday() in [0, 1, 2]:
        rep = RitniReporter()
        rep.make_report()
        mail = Mailer(
            f"Ritnitop data export for {get_time_stamp()} ",
            [
                "<EMAIL>",
                "<EMAIL>"
            ],
            rep.content_list,
            MailConfigOptions.outlook_enskai_alerts.value,
        )
        mail.send_mail()


if __name__ == "__main__":
    main()

