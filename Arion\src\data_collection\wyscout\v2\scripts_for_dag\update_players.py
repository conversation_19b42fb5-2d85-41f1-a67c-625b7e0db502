import asyncio

from src.data_collection.wyscout.v2.PlayersUpdater import PlayersUpdater
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.UpdatesChecker import UpdatesChecker


async def main():
    from_scratch = False  # here this is false because we are never updating teams, players, matches from scratch
    players_checker = UpdatesChecker(
        table_name="players_for_collection",
        object_type="players",
        id_name="playerId",
        from_scratch=from_scratch,
    )
    players_updater = PlayersUpdater()
    phantom_objects = ["teams"]

    orc = Orchestrator(
        batch_size=1000,
        updates_checker=players_checker,
        updater=players_updater,
        from_scratch=from_scratch,
        phantom_objects=phantom_objects,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
