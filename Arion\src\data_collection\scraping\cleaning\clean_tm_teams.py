import re
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import postgres_prod_str, postgres_dev_str
from src.data_collection.scraping.cleaning.clean_tm_transfers import Cleaner


class TeamsCleaner(Cleaner):
    def prep_table(self):
        df = self.raw_df
        for col in df.dtypes[df.dtypes == "object"].index:
            df[col] = df[col].str.strip()
        tier_dict = {
            "First Tier": 1,
            "Second Tier": 2,
            "Third Tier": 3,
            "Fourth Tier": 4,
            "Fifth Tier": 5,
            "Sixth Tier": 6,
        }
        df["league_tier"] = df["league_tier"].map(tier_dict).astype("Int64")
        self.df = df


def main():
    cnx_dev = create_engine(postgres_dev_str)
    clean = TeamsCleaner(cnx_dev, "raw_tm_teams", "tm_teams")
    clean.read_raw_table()
    clean.prep_table()
    clean.write_table()


if __name__ == "__main__":
    main()
