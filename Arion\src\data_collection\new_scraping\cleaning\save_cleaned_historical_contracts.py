import traceback

from sqlalchemy.engine import create_engine

from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import HistoricalContract
from src.helper_funcs import fast_read_sql, fast_write_sql


def main():
    engine = create_engine(postgres_prod_str)
    SCHEMA = "meta_scraping"
    prep = HistoricalContract()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.raw_tm_historical_contracts", engine)
    connection = engine.raw_connection()
    cursor = connection.cursor()
    try:
        cursor.execute("DELETE FROM transfermarkt.tm_historical_contracts")
        fast_write_sql(
            prep.clean_df(df),
            "tm_historical_contracts",
            cnx=engine,
            if_exists="append",
            grant=True,
            schema="transfermarkt",
            cursor=cursor,
            connection=connection,
            transaction=True,
        )
        # execute the query in transaction since we are inserting in place now:
        cursor.execute(f"DELETE FROM {SCHEMA}.raw_tm_historical_contracts")
        connection.commit()
        cursor.close()

    except Exception as e:
        connection.rollback()
        cursor.close()
        print(traceback.format_exc())
        raise Exception()

if __name__ == "__main__":
    main()
