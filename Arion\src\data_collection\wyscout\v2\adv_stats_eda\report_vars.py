from datetime import datetime
from sqlalchemy import create_engine
import pandas_profiling
from pandas import ExcelWriter

from settings import postgres_prod_str
from src.helper_funcs import fast_read_sql


def main():
    cnx = create_engine(postgres_prod_str)
    df = fast_read_sql(
        f"""SELECT 
    cm.name || ', ' || cm.area_name as league, 
    ap."firstName" || ' ' || ap."lastName" || ',id: ' || ap."playerId"::text || ',team: ' || tm.name as player,
    pr.primary_position, 
    adv.* from 
    wyscout.advanced_stats adv,
    public.all_players ap,
    public.seasons_matches sm,
    teams tm,
    player_roles pr,
    public.competitions cm
    where adv."matchId" = sm."matchId" 
    and tm."teamId" = ap."currentTeamId"
    and sm."competitionId" = cm."competitionId"
    and ap."playerId" = adv."playerId"
    and pr."playerId" = ap."playerId"
    """,
        cnx,
    )
    print(datetime.now(), "df loaded")
    overall = pandas_profiling.ProfileReport(
        df,
        title="Advanced Stats Overall Analysis",
        pool_size=4,
        progress_bar=True,
        correlations={
            "pearson": {"calculate": True},
            "spearman": {"calculate": False},
            "kendall": {"calculate": False},
            "phi_k": {"calculate": False},
            "cramers": {"calculate": False},
        },
        interactions=None,
        missing_diagrams=None,
        duplicates=None,
    )

    # overall.to_file("src/data_collection/wyscout/v2/overall_report.html")
    with ExcelWriter(
        "src/data_collection/wyscout/v2/adv_stats_eda/advanced_stats_report.xlsx"
    ) as writer:
        for var in ("league", "player", "primary_position"):
            print(datetime.now(), var)
            temp_df = df.groupby(var).describe(
                percentiles=[0.1, 0.25, 0.5, 0.75, 0.9]
            )
            temp_df.to_excel(writer, var)
        writer.save()
    # for var in ("league", "primary_position"):
    #     for lvl in df[var].unique():
    #         sub = df[df[var] == lvl]
    #         rep = pandas_profiling.ProfileReport(
    #             sub,
    #             title=f"Advanced Stats {var} - {lvl}",
    #             pool_size=4,
    #             progress_bar=True,
    #             correlations={
    #                 "pearson": {"calculate": True},
    #                 "spearman": {"calculate": False},
    #                 "kendall": {"calculate": False},
    #                 "phi_k": {"calculate": False},
    #                 "cramers": {"calculate": False},
    #             },
    #             interactions=None,
    #             missing_diagrams=None,
    #             duplicates=None,
    #         )
    #         rep.to_file(f"src/data_collection/wyscout/v2/{var}_{lvl}.html")
