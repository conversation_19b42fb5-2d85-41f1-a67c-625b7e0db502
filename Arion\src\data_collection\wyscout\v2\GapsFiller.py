import pandas as pd

from src.data_collection.wyscout.v2.Filler import Filler
from src.data_collection.wyscout.v2.scripts_for_dag import (
    update_teams,
    update_players,
    update_match_objects,
    collect_match_info_for_empty_games,
)
from src.helper_funcs import (
    fast_write_sql,
    fast_read_sql,
    cloud_log_struct,
    cloud_log_text,
)


class GapsFiller(Filler):
    """Checks relationships between tables and tries to recollect records
    that are supposed to be there but may have been missed because
    of some error during collection
    """

    def __init__(self):
        super().__init__()
        self.scripts = [
            update_teams,
            update_players,
            update_match_objects,
            collect_match_info_for_empty_games,
        ]

    def write_missing_ids_for_collection(self):
        for table in self.gaps_map:
            df = pd.DataFrame(self.gaps_map[table])
            if len(df) > 0:
                fast_write_sql(
                    df,
                    f"{table}_for_collection",
                    self.cnx_prod,
                    schema="meta",
                    if_exists="append",
                )
                cloud_log_struct(
                    self.logger,
                    {
                        "action": "write_missing_ids_for_collection",
                        "ids": self.gaps_map[table],
                    },
                )

    async def fill_gaps(self):
        for script in self.scripts:
            cloud_log_text(
                self.logger, f"start to fill gaps with {script.__name__} "
            )
            await script.main()
