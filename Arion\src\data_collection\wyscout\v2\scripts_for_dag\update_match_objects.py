import asyncio
import os


from src.data_collection.wyscout.v2.MatchObjectsOrchestrator import (
    MatchObjectsOrchestrator,
)
from src.data_collection.wyscout.v2.MatchObjectsUpdater import (
    MatchObjectsUpdater,
)
from src.data_collection.wyscout.v2.MatchesUpdatesChecker import (
    MatchesUpdates<PERSON><PERSON>cker,
)


async def main():

    collection_mode = os.environ["COLLECTION_MODE"]
    if collection_mode == "initial":
        from_scratch = True
        phantom_objects = None
    elif collection_mode == "update":
        from_scratch = False
        phantom_objects = {
            "events": None,
            "matches": ["teams"],
            "lineups": ["teams", "players"],
            "formations": ["teams", "players"],
            "player_match_positions": ["players"],
        }
    else:
        raise ValueError("Invalid collection mode")

    matches_checker = MatchesUpdatesChecker(
        table_name="matches_for_collection",
        object_type="matches",
        id_name="matchId",
        from_scratch=from_scratch,
        all_objects_table="seasons_matches",
        only_active=False,
    )
    matches_updater = MatchObjectsUpdater()
    orc = MatchObjectsOrchestrator(
        batch_size=500,
        updates_checker=matches_checker,
        updater=matches_updater,
        from_scratch=from_scratch,
        phantom_objects=phantom_objects,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
