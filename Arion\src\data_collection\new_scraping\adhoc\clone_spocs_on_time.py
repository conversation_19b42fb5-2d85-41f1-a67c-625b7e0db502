import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from uuid import uuid4

cnx = create_engine(postgres_prod_str)
pd.set_option("max_columns", None)

schema = "crm"

requests_to_transfer_df = pd.read_sql(
    f"""select * from {schema}.team_requests tr where organization_id = 'ebfdb98e-9af4-4ba0-a437-4ce1997132e1' and 'summer_2023' = any(transfer_period)""",
    cnx,
)

try:
    requests_to_add = []
    for index, request in requests_to_transfer_df.iterrows():
        dd = request.to_dict()
        dd["organization_id"] = "2a3458cd-6dfc-4dbe-b4ef-b4f50aa5af41"
        dd["id"] = uuid4()
        requests_to_add.append(dd)
    df_to_save = pd.DataFrame(requests_to_add)
    df_to_save.to_sql(
        "team_requests", cnx, schema=schema, index=None, if_exists="append"
    )
except Exception as e:
    print(e)
    pass
