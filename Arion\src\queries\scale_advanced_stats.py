from settings import postgres_dev_str
from sqlalchemy import create_engine

def generate_string():
    cnx = create_engine(postgres_dev_str)
    cols = cnx.execute('''select array(select column_name
                        from   information_schema.columns
                        where  table_name = 'advanced_stats_total'
                        ) ''').\
                            first()[0].\
                                replace('{', '').\
                                    replace('}', '').\
                                        split(',')
                                        
    scaled = [x for x in cols if '_scaled' in x]
    unscaled = [x.replace('_scaled', '') for x in scaled]
    scale_str = '''UPDATE advanced_stats_total
                    SET '''
    for sc_col, col in zip(scaled, unscaled):
        if scaled.index(sc_col) != len(scaled)-1:
            scale_str = scale_str +f''' "{sc_col}" = "{col}"*wt."league_weight",\n'''
        else:
            scale_str = scale_str +f''' "{sc_col}" = "{col}"*wt."league_weight"\n'''

    scale_str = scale_str + '''FROM 
                                player_match_weights wt
                                WHERE advanced_stats_total."playerId" = wt."playerId" 
                                AND advanced_stats_total."matchId" = wt."matchId";
                            '''
    cnx.execute(scale_str)
if __name__ == "__main__":
    generate_string()
