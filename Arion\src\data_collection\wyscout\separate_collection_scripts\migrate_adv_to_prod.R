source('src/data_collection/wyscout/00_libs.R')
library(readr)
library(dplyr)
library(dbplyr)
library(tidyr)
library(RPostgreSQL)
create_new = as.logical(Sys.getenv("OVERWRITE_ADV_PROD"))

refresh_connection <- function() {
  drv <- dbDriver("PostgreSQL")
  con_list <- dbListConnections(drv)
  if (length(con_list) > 0) {
    for (cn in con_list) {
      dbDisconnect(cn)
    }
  }
  
  con <<- dbConnect(
    drv,
    dbname =  Sys.getenv("DEVELOPMENT_DB"),
    host = Sys.getenv("HOST"),
    port = 5432,
    user = Sys.getenv("USR_LOC"),
    password = Sys.getenv("PWD_LOC")
  )
  
  con_prod <<- dbConnect(
    drv,
    dbname =  Sys.getenv("DB_TO_MOVE_TO"),
    host = Sys.getenv("HOST"),
    port = 5432,
    user = Sys.getenv("USR_LOC"),
    password = Sys.getenv("PWD_LOC")
  )
  
}

write_data_to_prod <- function(iter, table_name, df, create_new_tbl = create_new, cnames = NULL){
  df[setdiff(colnames(df), cnames)] = NA
  if (create_new_tbl) {
    if (iter == 1) {
      dbWriteTable(
        con_prod,
        table_name,
        df,
        overwrite = T,
        rownames = F,
        row.names = F
      )
    } else{
      dbWriteTable(
        con_prod,
        table_name,
        df,
        append = T,
        rownames = F,
        row.names = F
      )
    }
  } else{
    dbWriteTable(
      con_prod,
      table_name,
      df,
      append = T,
      rownames = F,
      row.names = F
    )
  }
}

# Database connection ----
drv <- dbDriver("PostgreSQL")
# creates a connection to the postgres database
# note that "con" will be used later in each connection to the database


con <- dbConnect(
  drv,
  dbname =  Sys.getenv("DEVELOPMENT_DB"),
  host = Sys.getenv("HOST"),
  port = 5432,
  user = Sys.getenv("USR_LOC"),
  password = Sys.getenv("PWD_LOC")
)

con_prod <- dbConnect(
  drv,
  dbname =  'wyscout_raw_production',
  host = Sys.getenv("HOST"),
  port = 5432,
  user = Sys.getenv("USR_LOC"),
  password = Sys.getenv("PWD_LOC")
)

competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')
competition_weights = dbReadTable(con, 'player_match_weights')

# player_match_pos = dbReadTable(con, 'player_match_positions')
# dbWriteTable(
#   con_prod,
#   'player_match_positions',
#   player_match_pos,
#   overwrite = T,
#   rownames = F,
#   row.names = F
# )
# rm(list = c('player_match_pos'))

adv_stats_total_columns = dbGetQuery(con, 'select * from advanced_stats_total limit 1')
scaled_cols = colnames(adv_stats_total_columns)[grepl('scaled', colnames(adv_stats_total_columns))]
to_scale_cols = gsub('_scaled', '', scaled_cols)

adv_stats_total_prod_columns = dbGetQuery(con_prod, 'select * from advanced_stats_total limit 1')
adv_stats_avg_prod_columns = dbGetQuery(con_prod, 'select * from advanced_stats_average limit 1')
adv_stats_pct_prod_columns = dbGetQuery(con_prod, 'select * from advanced_stats_percentage limit 1')

if (create_new) {
  comps_in_adv = dbGetQuery(con,
                            'select distinct "competitionId" from advanced_stats_total')
} else{
  advanced_stats_dev = dbGetQuery(
    con,
    'select distinct "matchId", "playerId", "competitionId" from advanced_stats_total'
  )
  advanced_stats_prod = dbGetQuery(
    con_prod,
    'select distinct "matchId", "playerId", "competitionId" from advanced_stats_total'
  )
  advanced_stats_to_move = advanced_stats_dev[!paste0(advanced_stats_dev$matchId, advanced_stats_dev$playerId) %in%
                                                paste0(advanced_stats_prod$matchId, advanced_stats_prod$playerId),]
  comps_in_adv = unique(advanced_stats_to_move['competitionId'])
}

competitions = competitions %>%
  filter(competitionId %in% comps_in_adv$competitionId)

for (i in 1:nrow(competitions)) {
  # print(i)
  
  refresh_connection()
  
  adv_total_comp = dbGetQuery(
    con,
    paste(
      'select * from advanced_stats_total where "competitionId" =',
      competitions$competitionId[i]
    )
  )
  
  adv_total_comp = adv_total_comp %>%
    filter(!paste(matchId, playerId) %in% 
             paste(advanced_stats_prod$matchId, advanced_stats_prod$playerId))
  
  if (nrow(adv_total_comp) > 0) {
    write_data_to_prod(i, 'advanced_stats_total', adv_total_comp, create_new, colnames(adv_stats_total_prod_columns))
  }
  
  refresh_connection()
  
  adv_avg_comp = dbGetQuery(
    con,
    paste(
      'select * from advanced_stats_average where "competitionId" =',
      competitions$competitionId[i]
    )
  )
  
  adv_avg_comp = adv_avg_comp %>%
    filter(!paste(matchId, playerId) %in% 
             paste(advanced_stats_prod$matchId, advanced_stats_prod$playerId))
  
  if (nrow(adv_avg_comp) > 0) {
    # adv_total_comp = adv_total_comp[!duplicated(adv_total_comp[c('matchId', 'playerId')]), ]
    write_data_to_prod(i, 'advanced_stats_average', adv_avg_comp, create_new, colnames(adv_stats_avg_prod_columns))
  }
  
  refresh_connection()
  
  adv_perc_comp = dbGetQuery(
    con,
    paste(
      'select * from advanced_stats_percentage where "competitionId" =',
      competitions$competitionId[i]
    )
  )
  
  adv_perc_comp = adv_perc_comp %>%
    filter(!paste(matchId, playerId) %in% 
             paste(advanced_stats_prod$matchId, advanced_stats_prod$playerId))
  
  if (nrow(adv_perc_comp) > 0) {
    write_data_to_prod(i, 'advanced_stats_percentage', adv_perc_comp, create_new, colnames(adv_stats_pct_prod_columns))
  }
  
}
