#how to use:

#when using a key-value pair use key:value; if you want to list several times use dashes or square brakes as shown below:
#1)  key:  [string1, string2, string3, string4, string5, string6]

#2) key:
#  - string1
#  - string2
#  - string 3

# 1 and 2 do basically the same thing

# config variable names can include spaces but using underscore to connect words is preferable
# blank lines do not change the code, but can help with readability

#to load yaml file:
# import yaml
#  with open("config.yml", 'r') as ymlfile:
#    cfg = yaml.load(ymlfile, Loader=yaml.FullLoader)

#to programmatically append yaml file:
#  with open('config.yml','r') as yamlfile:
#    cfg = yaml.load(yamlfile)
#    cfg.update(new_yaml_data_dict)
#  with open('config.yml','w') as yamlfile:
#    yaml.dump(cfg, yamlfile, Dumper=yaml.Dumper)

league_scaling_transform_power: 0.33333333333333333333333333333333333333

db_tables:
  raw_advanced_stats_total: advanced_stats_total
  player_positions: player_match_positions
  competitions: competitions
  transformed_vars: distancing_features_date_filtered
  player_ranks: player_rankings
  progression_consistency: players_consistency_progression
  competition_weights: competition_weights
  player_performance_in_advanced_stats: player_average_performance
  derived_advanced_stats: derived_advanced_stats
  transfermarkt_data: transfermarkt_data
  transfermarkt_injuries: transfermarkt_injuries
  tm_to_ws_ids: tm_to_ws_ids
  scaling_attributes: scaling_attributes

scraping:
  transfermarkt:
    continents: [europa, amerika, afrika, asien]
    sleep:
      on_fail: [3, 10]
      on_new_team: [1, 2]
      on_new_league: [2, 5]
      on_new_continent: [5, 10]
  directories: #dont put trailing backslashes for dirs
    scraped_continents: data/raw/scraped/continents
    scraped_leagues: data/raw/scraped/leagues
    scraped_teams: data/raw/scraped/teams
    scraped_players: data/raw/scraped/players
    scraped_player_details: data/raw/scraped/player_details
    scraped_player_injuries: data/raw/scraped/player_injuries
    scraped_league_details: data/raw/scraped/league_details/league_details.csv
    processed_scraped: data/processed/scraped
    processed_matched: data/processed/scraped_matched
    processed_batch_matched: data/processed/scraped_matched/batch_match
    tm_ws_match_logs: logs/tm_ws_match_logs
    data_availability_logs: logs/data_availability
    cleaned_transfermarkt: data/processed/scraped/players_w_team_league.csv
    cleaned_player_profiles: data/processed/scraped/cleaned_player_profiles.csv
    cleaned_player_injuries: data/processed/scraped/cleaned_player_injuries.csv
    cleaned_league_details: data/processed/scraped/cleaned_league_details.csv
    imputed_league_mean_player_values: data/processed/imputed_league_mean_player_values.csv
    ws_fixed_tiers: data/processed/ws_fixed_tiers.csv
    matched_leagues: data/processed/matched_leagues.csv
    leagues_w_weights: data/processed/leagues_w_weights.csv
    progress_trackers:
      directory: data/trackers
      transfermakt: data/trackers/transfermarkt_tracker.pkl
      player_profiles: data/trackers/player_profiles_tracker.pkl
      player_injuries: data/trackers/player_injuries_tracker.pkl
    archives: data/archives

elo_settings:
  start_date: "2015-01-01" #format: YYYY-MM-DD
  min_rating: 1500
  max_rating: 2500
  unknown_rating: 1200
  deviation: 150
  elo_k: 64
  team_rating_smoothing_window: 5

