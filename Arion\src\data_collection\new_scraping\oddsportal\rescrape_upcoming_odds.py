from src.data_collection.new_scraping.scraping.scrape_upcoming_odds import (
    ScrapeUpcomingOdds,
)
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
import asyncio
import pandas as pd
from src.helper_funcs import fast_write_sql

pd.set_option("display.max_columns", 15)
engine = create_engine(postgres_prod_str)

if __name__ == "__main__":
    scraper = ScrapeUpcomingOdds()

    bookie_with_dds = []
    match_dd = []

    # Supply league url
    for i in asyncio.run(
        scraper.loop_through_urls(
            "https://www.oddsportal.com/soccer/england/premier-league/"
        )
    ):
        [bookie_with_dds.append(el) for el in i[0].values()]
        match_dd.append(i[1])

    odds_df = pd.DataFrame(bookie_with_dds)
    match_df = pd.DataFrame(match_dd)

    fast_write_sql(
        odds_df,
        "upcoming_match_odds",
        cnx=engine,
        schema="oddsportal",
        if_exists="append",
        grant=True,
    )

    fast_write_sql(
        match_df,
        "upcoming_matches",
        cnx=engine,
        schema="oddsportal",
        if_exists="append",
        grant=True,
    )
