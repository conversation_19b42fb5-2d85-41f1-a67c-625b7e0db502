import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

# from dag_settings import workdir_potential
workdir_potential = '/home/<USER>/Projects/player-potential'

sys.path.append(workdir_potential)


dag_params = {
    "dag_id": "create_potential_tables",
    "start_date": datetime(2022, 5, 3, 14),
    "schedule_interval": None,
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_potential,
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:


    create_potential_mv = PostgresOperator(
        task_id="create_potential_mv",
        database="wyscout_raw_production",
        sql=open(
            "/home/<USER>/Projects/player-potential/src/queries/create_training_mv.sql"
        ).read(),
    )

    create_potential_tabular_mv = PostgresOperator(
        task_id="create_potential_tabular_mv",
        database="wyscout_raw_production",
        sql=open(
            "/home/<USER>/Projects/player-potential/src/queries/tabular_model_dw.sql"
        ).read(),
    )

    create_train_valid_test_sets = PostgresOperator(
        task_id = "split_train_valid_test_sets",
        database="wyscout_raw_production",
        sql=open('/home/<USER>/Projects/player-potential/src/queries/split_train_validation_test.sql').read()
    )

    create_potential_mv_inference = PostgresOperator(
        task_id="create_potential_mv_inference",
        database="wyscout_raw_production",
        sql=open(
            "/home/<USER>/Projects/player-potential/src/queries/create_training_mv_inference.sql"
        ).read(),
    )

    create_potential_tabular_mv_inference = PostgresOperator(
        task_id="create_potential_tabular_mv_inference",
        database="wyscout_raw_production",
        sql=open(
            "/home/<USER>/Projects/player-potential/src/queries/tabular_model_dw_inference.sql"
        ).read(),
    )
    

    create_potential_mv >> create_potential_tabular_mv >> create_train_valid_test_sets
    create_potential_tabular_mv >> create_potential_mv_inference >> create_potential_tabular_mv_inference
    
