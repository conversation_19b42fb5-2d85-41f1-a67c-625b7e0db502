select "matchId", 
	(events_json->'second')::int as sec, 
	events_json->>'matchTimestamp' as timestamp, 
	events_json->'type'->>'primary' as action_name, 
	events_json->'type'->>'secondary' as secondary_actions
from events_object_db
where (events_json->>'matchPeriod') = '2H' and 
	"matchId" = 5127294 and 
	(events_json->'type'->>'primary') = 'pass' and
	events_json->'type'->'secondary' @> '"short_or_medium_pass"';
