from multiprocessing import cpu_count, Pool
import time
import aiohttp
import asyncio
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
from typing import List
from settings import PROXIES
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)
import requests
#import sys
#policy = asyncio.WindowsSelectorEventLoopPolicy()
#asyncio.set_event_loop_policy(policy)
pd.set_option("display.max_columns", 15)


class TmStaffScraper(Scraper):
    async def loop_through_urls(self, urls: list) -> List[pd.DataFrame]:
        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for url in urls:
                html_tasks.append(fetch(session, url))
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> List[pd.DataFrame]:
        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_staff, htmls)
        pool.close()
        pool.join()

        staff_df = []

        for data in res:
            if data:
                [staff_df.append(d) for d in data]
        return pd.DataFrame(staff_df)

    def scrape_staff(self, tuple):
        text, url = tuple
        soup = BeautifulSoup(text, "html.parser")
        ll_dd = []

        if soup is None:
            print(f"no soup {url}")
            time.sleep(5)
            return []
        if not soup.find("div", attrs={"id": "yw2"}):
            print(f"no history as a coach{url}")
            time.sleep(1)
            return []

        dd = {
            "name": np.nan,
            "role": np.nan,
            "appointed": np.nan,
            "in_charge_until": np.nan,
            "matches": np.nan,
            "points_per_match": np.nan,
            "current_team_id": np.nan,
            "staff_id": np.nan,
            "citizenship": np.nan,
            "birth_date": np.nan,
            "status": "alive",
            "coaching_licence": np.nan,
            "preferred_formation": np.nan,
        }

        history_table = soup.find("div", attrs={"id": "yw2"})
        if not history_table:
            # We don't want staff without history table
            return []

        staff_id = url.split("/")[-1]

        try:
            citizenship = soup.find(
                "span", attrs={"itemprop": "nationality"}
            ).text.strip()
        except:
            citizenship = np.nan
            print(url)
        try:
            birth_date = (
                soup.find("span", attrs={"itemprop": "birthDate"})
                .text.split("(")[0]
                .strip()
            )
        except:
            birth_date = np.nan
            print(url)
        try:
            name = soup.find(
                "h1", attrs={"class": "data-header__headline-wrapper"}
            ).text.strip()
        except:
            name = np.nan
            print(url)
        try:
            status = soup.find("span", attrs={"itemprop": "deathDate"}).text
            status = "dead"
        except:
            status = "alive"
        try:
            trs = soup.find("table", attrs={"class": "auflistung"}).find_all(
                "tr"
            )
            try:
                if trs[-2].find("th").text.strip() == "Coaching Licence:":
                    coaching_licence = trs[-2].find("td").text.strip()
                else:
                    raise Exception()
            except:
                coaching_licence = np.nan
            try:
                if trs[-1].find("th").text.strip() == "Preferred formation:":
                    preferred_formation = trs[-1].find("td").text.strip()
                else:
                    raise Exception()
            except:
                preferred_formation = np.nan
        except:
            coaching_licence = np.nan
            preferred_formation = np.nan

        trs = history_table.findAll("tr")
        for tr in trs:
            tds = tr.findAll("td")
            try:
                if len(tds) != 6:
                    continue
                turl = tds[1].find("a")["href"]
                tid = (
                    turl.split("/")[-3]
                    if "saison_id" in turl
                    else turl.split("/")[-1]
                )
                dd["current_team_id"] = tid if tid.isdigit() else np.nan
            except:
                try:
                    if "Career break" in tds[1].text.strip():
                        continue
                    else:
                        print(url)
                        return []
                except:
                    print(url)
                    return []
            dd["role"] = str(tds[1].renderContents()).split(">")[-1][:-1]
            dd["appointed"] = tds[2].text.strip()
            dd["in_charge_until"] = tds[3].text.strip()
            dd["matches"] = tds[4].text
            dd["points_per_match"] = tds[5].text
            dd["staff_id"] = staff_id
            dd["citizenship"] = citizenship
            dd["birth_date"] = birth_date
            dd["name"] = name
            dd["status"] = status
            dd["coaching_licence"] = coaching_licence
            dd["preferred_formation"] = preferred_formation
            ll_dd.append(dd.copy())
            dd = {key: np.nan for key in dd}
        return ll_dd


if __name__ == "__main__":
    scraper = TmStaffScraper()
    print(
        asyncio.run(
            scraper.loop_through_urls(
                [
                    "https://www.transfermarkt.com/metodi-tomanov/profil/trainer/100"
                ]
            )
        )
    )
