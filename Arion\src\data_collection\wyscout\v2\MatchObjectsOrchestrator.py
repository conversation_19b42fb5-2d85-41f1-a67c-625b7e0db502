from typing import Type, List

import pandas as pd
from sqlalchemy import types

from src.helper_funcs import cloud_log_text
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.Up<PERSON><PERSON><PERSON><PERSON> import Up<PERSON><PERSON><PERSON><PERSON>
from src.data_collection.wyscout.v2.Updater import Updater


class MatchObjectsOrchestrator(Orchestrator):
    def __init__(
        self,
        batch_size: int,
        updates_checker: Type[UpdatesChecker],
        updater: Type[Updater],
        from_scratch=False,
        skip_empty_games: bool = True,
        phantom_objects: dict = None,
    ):
        super().__init__(
            batch_size=batch_size,
            updates_checker=updates_checker,
            updater=updater,
            from_scratch=from_scratch,
            skip_empty_games=skip_empty_games,
            phantom_objects=phantom_objects,
        )

    def drop_updated_existing_records(self, cursor, object_type, ids):
        # drop only for matches, this will cascade to the other tables
        if object_type == "matches":
            cursor.execute(
                f"""DELETE FROM wyscout.{object_type} WHERE "matchId" IN {ids}"""
            )

    def set_collection_status(self, active: bool):
        """Set the flag in the meta SQL table depending on whether the collection is active or not

        Args:
            active (bool): status of collection
        """
        status = 1 if active else 0
        self.cnx_prod.execute(
            f"UPDATE meta.matches_collection_status SET status = {status} "
        )
        cloud_log_text(self.logger, f"Collection status set to {status} ")

    async def write_to_db(
        self,
        updater_resp,
        prepped_ids_in_batch: List[int],
        connection,
        cursor,
    ):
        object_types = [
            "matches",
            "events",
            "formations",
            "lineups",
            "player_match_positions",
        ]
        # loop through object types(formations, lineups, events, matches)
        for obj in object_types:
            # try to write:
            df_temp = pd.concat(
                [x.get(obj, pd.DataFrame()) for x in updater_resp]
            )
            phantom_objects_temp = self.phantom_objects[obj]
            await self.loop_through_phantom_relationships(
                phantom_objects_temp, obj, df_temp
            )

            dtype = {"events": types.JSON} if obj == "events" else None
            self.handle_write_single_table(
                df_temp, prepped_ids_in_batch, obj, connection, cursor, dtype
            )
