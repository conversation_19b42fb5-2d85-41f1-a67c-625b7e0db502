# Get events data
source('src/data_collection/wyscout/00_libs.R')

collected_events = dbGetQuery(con, 'select distinct "matchId" from events')
adv_stats_matches = dbGetQuery(con, 'select distinct "matchId" from advanced_stats_total')
competition_matches = adv_stats_matches %>%
  filter(!matchId %in% collected_events$matchId)
tag_list_for_events = dbReadTable(con, 'available_event_tags')

event_df_types <- dbGetQuery(con, 'select * from events limit 1')
form_df_types <- dbGetQuery(con, 'select * from formation_events limit 1')

i = 0
events = list()
formations = list()
t1 = Sys.time()

for (i in 1:length(competition_matches$matchId)) {
  match_id = competition_matches$matchId[i]
  # Sys.sleep(0.085)
  cont = NULL
  try({
    resp = GET(
      paste0(
        'https://apirest.wyscout.com/v2/matches/',
        match_id,
        '/events?details=tag&fetch=formations'
      ),
      authenticate(wyscout_username, wyscout_pass)
    )
    cont = content(resp, as = 'text')
    cont = fromJSON(cont)
  })
  
  
  
  temp_df = NULL
  
  try({
    temp_df = get_events_df(cont)
    temp_df = temp_df %>%
      mutate(matchPeriod = ifelse(
        matchPeriod == '1H',
        1,
        ifelse(
          matchPeriod == '2H',
          2,
          ifelse(
            matchPeriod == 'E1',
            3,
            ifelse(matchPeriod == 'E2',
                   4,
                   ifelse(matchPeriod == 'P', 5, NA))
          )
        )
      ))
    temp_df = data.frame(temp_df, stringsAsFactors = F)
    events[[length(events) + 1]] = temp_df
  })
  
  temp_df = NULL
  try({
    temp_df = get_formations(cont)
    temp_df = temp_df %>%
      mutate(teamId = as.numeric(teamId))
    temp_df = data.frame(temp_df, stringsAsFactors = F)
    formations[[length(formations) + 1]] = temp_df
  })
  
  
  if (i %% 500 == 0 | i == nrow(competition_matches)) {
    
    evt_df = plyr::ldply(events, rbind)
    form_df = do.call(plyr::rbind.fill, formations)
    
    event_df_types = event_df_types[match(colnames(evt_df), colnames(event_df_types))]
    form_df_types = form_df_types[match(colnames(form_df), colnames(form_df_types))]
    
    evt_df[] <- mapply(FUN = as,evt_df,sapply(event_df_types,class),SIMPLIFY = FALSE)
    form_df[] <- mapply(FUN = as,form_df,sapply(form_df_types,class),SIMPLIFY = FALSE)
    
    # break
    
    try({
      dbWriteTable(
        con,
        'events',
        evt_df,
        overwrite = F,
        append = T,
        row.names = F,
        rownames = F
      )
    })
    
    try({
      dbWriteTable(
        con,
        'formation_events',
        form_df,
        overwrite = F,
        append = T,
        row.names = F,
        rownames = F
      )
    })
    
    events = list()
    formations = list()
  }
}
t2 = Sys.time()

