# # Create Database Structure

source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)

con = make_connection()
# Get Areas ----
resp = GET(
  'https://apirest.wyscout.com/v2/areas',
  authenticate(wyscout_username, wyscout_pass)
)
cont = content(resp)
downloaded_areas = dbReadTable(con, 'competition_areas')
areas = plyr::ldply(cont$areas, data.frame, stringsAsFactors = F)

areas = areas[!areas$alpha3code%in%downloaded_areas$alpha3code,]

if(nrow(areas) > 0){
  RPostgreSQL::dbWriteTable(
    con,
    'competition_areas',
    areas,
    overwrite = F,
    append = T,
    row.names = F,
    rownames = F
  )
}

dbDisconnect(con)

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'competition_areas',tbl_qry))
}

dbDisconnect(con)