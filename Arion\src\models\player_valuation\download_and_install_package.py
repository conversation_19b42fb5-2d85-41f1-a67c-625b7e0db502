import subprocess
from src.helper_funcs import download_blob, get_final_file_from_gcloud
import os


def main():
    
    download_dir = './wheels'

    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    bucket_name = 'player-valuation'
    
    folder_name = 'package_files'
    filename = get_final_file_from_gcloud(folder_name)

    dest_file_name = ''.join([download_dir, filename])
    download_blob(bucket_name, ''.join([folder_name, filename]), dest_file_name)
    # subprocess.call('chmod u+rx ./src/models/player_valuation/install_package.sh')
    rc = subprocess.call(['./src/models/player_valuation/install_package.sh', dest_file_name])
    if rc != 0:
        raise Exception('The package installation failed. Terminating.')

if __name__ == "__main__":
    main()