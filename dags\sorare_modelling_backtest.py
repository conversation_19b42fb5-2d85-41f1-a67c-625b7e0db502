import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir_sorare_test as workdir_sorare

sys.path.append(workdir_sorare)
experiment_name1 = 'entity_insteadof_name'
experiment_name2 = 'remove_starting_interest_kw'

dag_params = {
    "dag_id": "sorare_test_weekly_seasonality",
    "start_date": datetime(2022, 2, 7, 14),
    "schedule_interval": None,
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_sorare,
        "experiment_name1": f'"{experiment_name1}"',
        "experiment_name2": f'"{experiment_name2}"',
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    sorare_experiment1_nowknd = BashOperator(
        task_id="sorare_experiment1_no_weekend_seasonality",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name1}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 0
                           """,
    )

    sorare_experiment1_wknd = BashOperator(
        task_id="sorare_experiment1_weekend_seasonality",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name1}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 1
                           """,
    )

    sorare_experiment2_nowknd = BashOperator(
        task_id="sorare_experiment2_no_weekend_seasonality",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name2}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 0
                           """,
    )

    sorare_experiment2_wknd = BashOperator(
        task_id="sorare_experiment2_weekend_seasonality",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name2}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 1
                           """,
    )


    sorare_experiment1_nowknd
    sorare_experiment1_wknd
    sorare_experiment2_nowknd
    sorare_experiment2_wknd
