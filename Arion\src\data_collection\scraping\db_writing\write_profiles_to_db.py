import os
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, types

from src.data_collection.scraping.db_writing.BaseDbWriter import BaseDbWriter
from src.helper_funcs import read_config
from settings import postgres_dev_str


class ProfileWriter(BaseDbWriter):
    def prep_for_writing(self):
        self.merged_df = self.merged_df[
            [
                "agent",
                "contract_expiry",
                "current_value",
                "highest_value",
                "highest_value_date",
                "joined",
                "strong_foot",
                "last_update",
                "playerId",
                "player_url",
            ]
        ]

        self.merged_df = self.merged_df.rename(
            columns={
                "last_update": "player_value_last_update",
                "joined": "date_joined_current_team",
            }
        )
        # dropping entire duplicated rows, but keeping one of them, then dropping
        # duplicated player ids but dropping both because we have different matches and we want to make
        # sure we have no wrong matches
        self.merged_df = self.merged_df.drop_duplicates()
        self.merged_df = self.merged_df.drop_duplicates(
            ["playerId"], keep=False
        )

        self.dtypes = {
            "agent": types.String(),
            "current_club_id": types.BigInteger(),
            "contract_expiry": types.Date(),
            "current_value": types.Float(),
            "highest_value": types.Float(),
            "highest_value_date": types.Date(),
            "date_joined_current_team": types.Date(),
            "player_value_last_update": types.Date(),
            "strong_foot": types.String(),
            "playerId": types.BigInteger(),
            "player_url": types.String(),
        }

    def write_agent_info(self):
        """Collecting historical data about player agents so this
        table gets appended instead of overwritten as the regular one"""
        self.agent_df = self.merged_df[["playerId", "agent"]]
        try:
            date_collected = os.getenv("date_collected")
        except:
            date_collected = str(datetime.today().date())
        self.agent_df["date_collected"] = date_collected
        self.agent_df.to_sql(
            "tm_historical_agents",
            self.cnx,
            index=False,
            if_exists="append",
            method="multi",
        )


def main():
    cnx = create_engine(postgres_dev_str)
    config = read_config("config.yml")
    writer = ProfileWriter(
        config["db_tables"]["transfermarkt_data"],
        config["db_tables"]["tm_to_ws_ids"],
        config["scraping"]["directories"]["cleaned_player_profiles"],
        cnx,
    )
    writer.read_df_to_match()
    writer.read_tm_ws_ids()
    writer.merge_tables()
    writer.prep_for_writing()
    writer.write_to_db()


if __name__ == "__main__":
    main()
