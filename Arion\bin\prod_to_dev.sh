#!/bin/bash
USR=$(grep '^USR =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
PASS=$(grep '^PASS =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
HOST=$(grep '^HOST =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
PORT=$(grep '^PORT =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')

psql "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" \
-c "DROP TABLE IF EXISTS $TABLE;"

pg_dump "dbname=wyscout_raw_production host=$HOST user=$USR password=$PASS port=$PORT" \
-t $TABLE -O | psql "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" 

psql "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" -c \
"GRANT ALL ON TABLE public.$TABLE TO daniel, elvan, kliment WITH GRANT OPTION;"
