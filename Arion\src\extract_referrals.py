from sqlalchemy import create_engine
import pandas as pd
from settings import postgres_prod_str
from src.helper_funcs import fast_read_sql, get_sql_array_str
from datetime import date, datetime

cnx_prod = create_engine(postgres_prod_str)


def get_referrals_for_a_player(tm_player_id: str):
    main_guy_transfer = fast_read_sql(
        "select * from transfermarkt.tm_transfers tt where tm_player_id ="
        f" {tm_player_id}",
        cnx_prod,
    )

    teams = {}
    for index, row in main_guy_transfer.iterrows():
        if row["left_id"] not in teams:
            teams[row["left_id"]] = {"left_date": row["date"]}
        else:
            teams[row["left_id"]]["left_date"] = row["date"]
        if row["joined_id"] not in teams:
            teams[row["joined_id"]] = {"joined_date": row["date"]}
        else:
            teams[row["joined_id"]]["joined_date"] = row["date"]

    print(teams)

    common_teams = fast_read_sql(
        f"""select tt2.* from transfermarkt.tm_transfers tt
join transfermarkt.tm_transfers tt2 on tt.joined_id = tt2.joined_id or tt.joined_id = tt2.left_id 
where tt.tm_player_id = {tm_player_id} and tt2.joined_alt != 'Retired' and tt2.tm_player_id != {tm_player_id}""",
        cnx_prod,
    )

    specific_storage = []

    for tm_id in set(common_teams["joined_id"]):
        specific_storage.append(
            common_teams[common_teams["joined_id"] == tm_id]
        )
    for tm_id in set(common_teams["left_id"]):
        specific_storage.append(common_teams[common_teams["left_id"] == tm_id])
    # print(set(common_teams["joined_id"]), set(common_teams["left_id"]))

    referrals = []

    for index, transfer in common_teams.iterrows():
        team_id = False
        if transfer["joined_id"] in teams:
            team_id = transfer["joined_id"]
            player_id = transfer["tm_player_id"]
            if "joined_date" in teams[team_id]:

                # Joined after our Join AND Joined before our leave
                his_join = datetime.strptime(transfer["date"], "%Y-%m-%d")
                our_join = datetime.strptime(
                    teams[team_id]["joined_date"], "%Y-%m-%d"
                )

                try:
                    time_now = teams[team_id]["left_date"]
                except:
                    time_now = str(date.today())

                our_leave = datetime.strptime(time_now, "%Y-%m-%d")

                if his_join > our_join and his_join < our_leave:
                    referrals.append(
                        {"team_id": team_id, "tm_player_id": player_id}
                    )
        if transfer["left_id"] in teams:
            team_id = transfer["left_id"]
            player_id = transfer["tm_player_id"]
            if "joined_date" in teams[team_id]:
                his_leave = datetime.strptime(transfer["date"], "%Y-%m-%d")
                our_join = datetime.strptime(
                    teams[team_id]["joined_date"], "%Y-%m-%d"
                )

                outbound_transfers_with_team = None
                for transfers in specific_storage:
                    if team_id in transfers["joined_id"]:
                        outbound_transfers_with_team = transfers
                if outbound_transfers_with_team is not None:
                    join_transfer = outbound_transfers_with_team[
                        outbound_transfers_with_team["tm_player_id"]
                        == player_id
                    ]
                    if not join_transfer.empty:
                        his_join = datetime.strptime(
                            join_transfer["date"].values[0], "%Y-%m-%d"
                        )
                        # Joined before our Join AND left after our join
                        if his_join < our_join and his_leave > our_join:
                            referrals.append(
                                {"team_id": team_id, "tm_player_id": player_id}
                            )
    return pd.DataFrame(referrals).drop_duplicates(keep="first")


df = get_referrals_for_a_player("218441")

team_ids = {}

for i, r in df.iterrows():
    if r["team_id"] in team_ids:
        team_ids[r["team_id"]].append(r["tm_player_id"])
    else:
        team_ids[r["team_id"]] = [r["tm_player_id"]]

print(team_ids)


df_ll = []
for team_id, players in team_ids.items():
    data = fast_read_sql(
        f"""
    select td.name, td.birth_date, td.current_club, tt.team_name as where_they_played_together, td."position" from transfermarkt.transfermarkt_data td 
    join transfermarkt.tm_to_ws_ids ttwi on ttwi."playerId" = td."playerId" 
    join transfermarkt.tm_teams tt on tt.team_id = {team_id}
    where ttwi.tm_player_id in {get_sql_array_str(players)}
    """,
        cnx_prod,
    )
    df_ll.append(data)

big_data = pd.concat(df_ll)

print(big_data)
big_data.to_csv('team_referrals.csv', index=False)
