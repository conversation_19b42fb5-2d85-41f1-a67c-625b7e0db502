import json
import pandas as pd
from typing import Union, List

from src.data_collection.wyscout.v2.Updater import Updater
from src.data_collection.wyscout.v2.MatchPrepper import MatchPrepper
from multiprocessing import Pool, cpu_count


class MatchesUpdater(Updater):
    def __init__(self):
        super().__init__(None)
        self.base_url = "https://apirest.wyscout.com/v3/matches/_ID"
        self.object_type = "matches"
        self.id_name = "matchId"
        self.if_exists = "append"
        self.prepper = MatchPrepper()
        self.cpu = cpu_count()

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[dict, List[dict]]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        return [json.loads(resp)]

    def prep(self, results):
        results = list(filter(lambda x: x is not None, results))
        pool = Pool(processes=self.cpu)
        res = pool.map(self.prepper.prep, results)
        pool.close()
        pool.join()
        df = pd.concat([x["matches"] for x in res])
        numeric_cols = df._get_numeric_data().columns
        for col in numeric_cols:
            df[col] = df[col].astype("Int64")
        return df
