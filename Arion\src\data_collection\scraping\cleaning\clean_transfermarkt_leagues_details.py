import pandas as pd
import numpy as np
import re

from src.helper_funcs import read_config


def fix_cols_names(df):
    df.columns = [
        "mean_age",
        "current_champions",
        "foreigners",
        "divisionLevel",
        "most_valueable_player",
        "number_teams",
        "players",
        "record_champs",
        "uefa_coef",
        "area_name",
        "league_name",
        "mean_player_value",
    ]
    return df


def conv_str_to_float_col(x):
    pattern = r"\d+,?\d?"
    # try:
    if "mill" in str(x).lower():
        return (
            float(re.search(pattern, str(x)).group().replace(",", "."))
            * 10 ** 6
        )
    elif "th" in str(x).lower():
        return (
            float(re.search(pattern, str(x)).group().replace(",", "."))
            * 10 ** 3
        )
    else:
        return np.nan


def conv_uefa_coef(x):
    if "." in str(x):
        return float(x) * 1000
    else:
        return float(x)


def fix_cols(df):
    df = df.dropna(how="all")
    df.index.name = "competitionId"
    df = df.reset_index()
    lg_lvl_dict = {
        "First": 1,
        "Second": 2,
        "Third": 3,
        "Fourth": 4,
        "Fifth": 5,
        "Sixth": 6,
    }
    for col in df.columns:
        df[col] = df[col].astype(str).str.replace("\n", "")
        df[col] = df[col].replace("nan", np.nan)
    df["mean_player_value"] = df["mean_player_value"].apply(
        conv_str_to_float_col
    )
    df["divisionLevel"] = (
        df["divisionLevel"]
        .apply(lambda x: str(x).strip().split(" ")[0])
        .map(lg_lvl_dict)
    )

    df["uefa_coef"] = df["uefa_coef"].apply(
        lambda x: str(x).split("\xa0\xa0")[1].split(" ")[0]
        if str(x) != "nan"
        else np.nan
    )

    df["uefa_coef"] = df["uefa_coef"].apply(lambda x: conv_uefa_coef(x))

    df = df[
        [
            "competitionId",
            "league_name",
            "area_name",
            "divisionLevel",
            "uefa_coef",
            "mean_player_value",
        ]
    ]
    return df


def merge(new, prev):  # TODO cleanup this crap
    # prev = pd.read_csv('data/processed/leagues_w_weights.csv', index_col=0)
    new["league_weight"] = (
        np.sqrt(new.mean_player_value) / np.sqrt(new.mean_player_value).max()
    )
    mg = pd.merge(
        new,
        prev,
        left_on=["area_name", "league_level"],
        right_on=["area_name", "divisionLevel"],
    )
    mg = mg.drop_duplicates(keep=False)
    return mg


def main(take_df=None, return_df=False):
    config = read_config()
    if take_df is None:
        scraped_loc = config["scraping"]["directories"][
            "scraped_league_details"
        ]
        df = pd.read_csv(scraped_loc, index_col=0)
    else:
        df = take_df.copy()
    df = df.drop_duplicates()
    df = fix_cols_names(df)
    df = fix_cols(df)
    output_path = config["scraping"]["directories"]["cleaned_league_details"]
    df.to_csv(output_path)
    # mg.to_excel('data/processed/ys_comps_w_mean_value_new.xlsx')
    if return_df:
        return df


if __name__ == "__main__":
    main()
