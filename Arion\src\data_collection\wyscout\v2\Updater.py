import asyncio
import json
import traceback
from typing import Union, <PERSON>, Dict, Tuple, Optional
from time import sleep

import aiohttp
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from tenacity import retry, wait_fixed, stop_after_attempt
from pandas.api.types import is_numeric_dtype, is_string_dtype

from src.helper_funcs import (
    fast_write_sql,
    fast_read_sql,
    get_cloud_logger,
    cloud_log_text,
    cloud_log_struct,
    check_existance,
    AuthProvider,
)
from settings import postgres_prod_str, WYSCOUT_USR, WYSCOUT_PASS
from src.data_collection.wyscout.v2.RecordsFiller import RecordsFiller


class Updater:
    def __init__(
        self,
        table_name: str,
        auth_provider: AuthProvider = AuthProvider(WYSCOUT_USR, WYSCOUT_PASS),
    ):
        """
        Collects, preps, and rights a certain ws object to db

        Args:
            table_name (str): db table
        """
        self.logger = get_cloud_logger()
        self.cnx_prod = create_engine(postgres_prod_str)
        self.table_name = table_name
        self.auth_provider = auth_provider
        self.collection_list: Optional[List[int]] = None
        self.dtype: Optional[dict] = None
        self.sleep_time: float = 1 / 12
        self.object_type: Optional[str] = None
        self.if_exists: str = "fail"
        self.id_name: Optional[str] = None
        self.create_unknown_records_flag: bool = False
        self.prepped_ids_list: List[int] = []
        self.no_data_ids_list: List[int] = []
        self.timeout_happening: bool = False
        self.timeouts_retries_dict: Dict[str, int] = {}
        self.timeout_sleep_time: int = 30
        self.max_n_retries: Optional[int] = None
        self.write_in_loop: bool = False
        self.base_url: Optional[str] = None

    def get_objects_for_collection(self, source_table, source_schema, id_name):
        """
        Gets objects that need to be collected
        """
        id_series = fast_read_sql(
            f'SELECT "{id_name}" from {source_schema}.{source_table}',
            self.cnx_prod,
        )[id_name]
        self.convert_id_series_to_collection_list(id_series)

    def convert_id_series_to_collection_list(self, id_series: pd.Series):
        if is_numeric_dtype(id_series):
            self.collection_list = id_series[id_series > -1].tolist()
        elif is_string_dtype(id_series):
            self.collection_list = id_series[id_series != "unknown"].tolist()

    def extract_payload_from_resp(
        self, resp: str, code: int = None
    ) -> Union[dict, List[dict], None]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        payload = json.loads(resp)[self.object_type]
        if len(payload) == 0:
            return None
        return payload

    def handle_200_request(self, resp: str, code: int):
        payload = self.extract_payload_from_resp(resp, code)
        self.prepped_ids_list.append(code)
        if payload is None:
            self.no_data_ids_list.append(code)
        return payload

    def handle_non_200_request(
        self, resp: str, code: int, status: int, url: str
    ):
        # sourcery skip: merge-nested-ifs
        # if we are having a timeout, propagate set payload to None and propagate
        #  the  504 status up to process_response so that it can handle the timeout logic:
        if status in {429, 504}:
            return None
        elif status in {400, 404}:
            # this part handles the pathetic wyscout practice of returning 400 code as status
            # and the passing some other crap in the body because they decided so:
            err_payload = json.loads(resp)
            if err_payload.get("error", {}).get("code") == 404:
                self.no_data_ids_list.append(code)
                return None
        # if it is not a timeout/rate limit and not a 404, log the error, raise Exception so tenacity can
        # retry calling
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "bad status from wyscout: status:",
                "status": status,
                "error": resp,
                "url": url,
            },
        )
        raise Exception(
            f"Bad status from wyscout: status: {status}, url: {url}"
        )

    @retry(wait=wait_fixed(10), stop=stop_after_attempt(2))
    async def fetch_response(
        self,
        session: aiohttp.ClientSession,
        code: int,
        url: str,
        params: dict = None,
    ) -> Tuple[Union[Union[List, Dict], None], int]:
        """Concerned with getting request, extracting the payload and the respective actions depending on the status

        Args:
            session (aiohttp.ClientSession): [description]
            code (int): [description]
            url (str): [description]
            params (dict, optional): [description]. Defaults to None.

        Raises:
            Exception: [description]

        Returns:
            [type]: [description]
        """
        async with session.get(
            url,
            auth=aiohttp.BasicAuth(
                self.auth_provider.usr, self.auth_provider.pwd
            ),
            params=params,
        ) as response:
            resp = await response.text()
            status = response.status
            if status == 200:
                payload = self.handle_200_request(resp, code)
            else:
                payload = self.handle_non_200_request(resp, code, status, url)
        return payload, status

    async def check_timeouts(self):
        if self.timeout_happening:
            sleep(self.timeout_sleep_time)
            cloud_log_text(self.logger, "timing out")
            if self.max_n_retries is not None:
                self.check_timeout_retries()

    def clear_timeout_if_happening(self, status: int):
        if status not in (504, 429) and self.timeout_happening:
            cloud_log_text(self.logger, "clearing timeout status")
            self.timeout_happening = False

    def check_timeout_retries(self):
        n_retries = max(self.timeouts_retries_dict.values())
        # if max retries are exceeded, raise Exc and shut it down
        if n_retries >= self.max_n_retries:
            cloud_log_text(self.logger, "max retries exceeded")
            raise Exception("Max time outs from wyscout exceed")

    def handle_request_timeout(self, url: str):
        # if this is the first timeout, set flag and sleep:
        if not self.timeout_happening:
            self.timeout_happening = True
            sleep(self.timeout_sleep_time)
            # add it to the dict with timeouts:
        if url not in self.timeouts_retries_dict:
            self.timeouts_retries_dict[url] = 1
        # if we have tried before, increase the counter
        else:
            self.timeouts_retries_dict[url] += 1

    async def process_response(
        self,
        session: aiohttp.ClientSession,
        code: int,
        params: dict = None,
    ):
        """Call fetch_response and handle potential timeouts

        Args:
            session (aiohttp.ClientSession):
            url (str): request url
            params (dict, optional): request params. Defaults to None.
            parent_id (int, optional): Id to be added to the response. For example, when querying
            seasons-teams endpoint, passing seasonId to add to team record. Defaults to None.

        Returns:
            list: list with items returned from api response
        """
        if self.base_url is None:
            raise ValueError("No base url specified")
        url = self.base_url.replace("_ID", str(code))
        try:
            payload, status = await self.fetch_response(
                session, code, url, params
            )
            if status in {504, 429}:
                self.handle_request_timeout(url)
                # if max retries not exceeded, try again
                return await self.process_response(session, code)
        except:
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "action": "failed processing request",
                    "error": traceback.format_exc(),
                    "url": url,
                },
            )
            # re-raise after logging
            raise Exception(traceback.format_exc())
        # if we have managed to get a request without a timeout
        # and the last request had timed out, clear the status:
        self.clear_timeout_if_happening(status)
        return payload

    async def collect(self) -> Union[List, None]:
        """Collects a batch of ids from Wyscout endpoint

        Returns:
            list: A list of responses
        """
        if self.collection_list is None:
            raise ValueError("Collection list not implemented")
        tasks = []
        async with aiohttp.ClientSession() as session:
            for code in self.collection_list:
                await asyncio.sleep(self.sleep_time)
                await self.check_timeouts()
                tasks.append(
                    asyncio.create_task(self.process_response(session, code))
                )
            # collect results form tasks:
            results = await asyncio.gather(*tasks)
            # filter for None responses (i.e. empty/broken responses)
            results = list(filter(lambda x: x is not None, results))
            if results:
                return list(np.concatenate(results))
            else:
                return

    def prep(self, result: list) -> pd.DataFrame:
        """Take a list of responses and prep them into a df for writing

        Args:
            result (list): list of api responses to be prepped in tabular format
        """
        pass

    @staticmethod
    def flatten_dict_cols(
        df: pd.DataFrame, keep_parent_col_name: bool, subset: List[str] = None
    ) -> pd.DataFrame:
        """Takes a pandas df and flattens all nested dict columns

        Args:
            df (pd.DataFrame):
            keep_parent_col_name (bool): if true then resulting columns will be parent-column_subcolumns,
            otherwise then column will be directly equal to the subcolumn name
            subset (list, optional): if a list is passed this is parformed only on a subset of columns.
            Defaults to None.


        Returns:
            pd.DataFrame: flattened df
        """
        cols = df.columns if subset is None else subset
        df_list = []
        cols_to_drop = []
        for col in cols:
            dropped_nans = df[col].dropna()
            if len(dropped_nans) > 0:
                first_non_null = dropped_nans.values[0]
                if isinstance(first_non_null, dict):
                    keys = first_non_null.keys()
                    filled = df[col].where(
                        df[col].notnull(), lambda x: {k: np.nan for k in keys}
                    )
                    flat_col_df = pd.DataFrame(filled.tolist())
                    if keep_parent_col_name:
                        flat_col_df.columns = [
                            f"{col}_{subcol}" for subcol in flat_col_df.columns
                        ]
                    df_list.append(flat_col_df)
                    cols_to_drop.append(col)
        df = df.drop(columns=cols_to_drop)
        df_list.insert(0, df)
        df = pd.concat(df_list, axis=1)
        return df

    def create_unknown_records(self, df, connection, cursor):
        rc = RecordsFiller()
        un = rc.generate_unknown_record_from_sample_df(df)
        fast_write_sql(
            un,
            self.table_name,
            self.cnx_prod,
            schema="wyscout",
            if_exists="append",
            transaction=True,
            connection=connection,
            cursor=cursor,
        )
        cloud_log_struct(
            self.logger,
            {
                "action": "create_unknown_records",
                "object_type": self.table_name,
            },
        )

    def rollback_failed_write(self, connection, cursor):
        connection.rollback()
        cursor.close()
        cloud_log_struct(
            self.logger,
            {
                "orchestrator": self.__class__.__name__,
                "updater": self.__class__.__name__,
                "object_type": self.object_type,
                "action": "failed_write",
                "error": traceback.format_exc(),
            },
        )
        raise Exception(traceback.format_exc())

    def commit_transaction(self, connection, cursor):
        """Try-except for the transaction commit, it may fail if any constraint are
        violated at the end of the operations

        Args:
            connection ([type]): [description]
            cursor ([type]): [description]

        Raises:
            Exception: [description]
        """
        try:
            connection.commit()
            cursor.close()
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "object_type": self.object_type,
                    "action": "commit_transaction",
                },
            )
        except:
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "object_type": self.object_type,
                    "action": "transaction_failed_at_commit",
                },
            )
            raise Exception(traceback.format_exc())

    def write_to_db(self, df: pd.DataFrame, keep_keys: bool = True):
        """
        Writes updated df
        Args:
            df(pd.DataFrame): Dataframe for writing to db
            keep_keys(bool): if true, and table needs to be replaced, different writing strategy will be used
        """
        # we need to run in transaction and defer all constrains because
        # when we do if_exists=replace, we will drop the and we dont
        #  want to get a fk error
        connection = self.cnx_prod.raw_connection()
        cursor = connection.cursor()
        try:
            existance = check_existance(
                self.cnx_prod, self.table_name, "wyscout"
            )
            keep_keys = keep_keys if existance else False
            if keep_keys:
                # if we want to keep keys, first we set contraints to deferred, so that
                # we check the constraints all the way at the end;
                # then we delete from table first; note that truncate table and drop table
                # will raise error because the foreign key needs to be cascaded in those cases but not when deleting
                cursor.execute("SET CONSTRAINTS ALL DEFERRED;")
                if self.if_exists == "replace":
                    cursor.execute(f"DELETE FROM wyscout.{self.table_name}")
                # if not replacing, but appending, we dont need to delete everything,
                # so the append part is same in both cases;
                # if there are no indexes that we care about, we just go ahead and do the standard writing procedure:

            if_exists_temp = "append" if keep_keys else self.if_exists
            fast_write_sql(
                df,
                self.table_name,
                self.cnx_prod,
                if_exists=if_exists_temp,
                dtype=self.dtype,
                schema="wyscout",
                transaction=True,
                connection=connection,
                cursor=cursor,
            )
            # creating of unknown records should happen before commiting
            # because otherwise we may violate the foreign key constraints:
            if self.create_unknown_records_flag:
                self.create_unknown_records(df, connection, cursor)
        # if things go south rollback, log the error and close the cursor:
        except:
            self.rollback_failed_write(connection, cursor)

        # commit once we are done and then log to the cloud:
        self.commit_transaction(connection, cursor)

    async def loop(self):
        """
        Splits objects for collection list into batches if necessary, collects,
        and writes every single batch
        """
        results = await self.collect()
        if results is None:
            return
        df = self.prep(results)
        if self.write_in_loop:
            self.write_to_db(df)
            return
        return df
