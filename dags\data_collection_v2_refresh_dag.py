from datetime import datetime, timedelta
import schedule
import time
from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

from dag_settings import workdir


COLLECTION_MODE = "update"

dag_params = {
    "dag_id": "data_collection_v2_refresh_dag",
    "start_date": datetime(2021, 7, 29, 8),
    "schedule_interval": "0 1 * * *",
    "params": {"workdir": workdir, "COLLECTION_MODE": COLLECTION_MODE},
    "dagrun_timeout": timedelta(minutes=480),
    "max_active_runs": 1,
    "default_view": "tree",
    "catchup": False,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    update_areas = BashOperator(
        task_id="update_areas",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_areas.py """,
    )

    update_competitions = BashOperator(
        task_id="update_competitions",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_competitions.py """,
    )

    update_rounds = BashOperator(
        task_id="update_rounds",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_rounds.py """,
    )
    update_seasons = BashOperator(
        task_id="update_seasons",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons.py """,
    )

    update_seasons_teams = BashOperator(
        task_id="update_seasons_teams",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_teams.py """,
    )

    update_teams = BashOperator(
        task_id="update_teams",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_teams.py """,
    )

    update_players = BashOperator(
        task_id="update_players",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_players.py """,
    )

    update_seasons_players = BashOperator(
        task_id="update_seasons_players",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_players.py """,
    )

    update_seasons_matches = BashOperator(
        task_id="update_seasons_matches",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_matches.py """,
    )

    update_transfers = BashOperator(
        task_id="update_transfers",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_transfers.py """,
    )

    update_match_objects = BashOperator(
        task_id="update_match_objects",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_match_objects.py """,
    )

    derive_advanced_stats = BashOperator(
        task_id="derive_advanced_stats",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/derive_advanced_stats.py""",
    )

    collect_match_info_for_empty_games = BashOperator(
        task_id="collect_match_info_for_empty_games",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/collect_match_info_for_empty_games.py""",
    )

    update_fixtures = BashOperator(
        task_id="update_fixtures",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_fixtures.py""",
    )
    update_leagues_standings = BashOperator(
        task_id="update_leagues_standings",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_leagues_standings.py""",
    )
    update_cups_standings = BashOperator(
        task_id="update_cups_standings",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/update_cups_standings.py""",
    )

    update_historical_elo = BashOperator(
        task_id="update_historical_elo",
        bash_command=""" cd {{ params.workdir }}
                        Rscript src/features_creation/calculate_team_strengths.R """,
    )

    update_scaling_attributes = PostgresOperator(
        task_id="update_scaling_attributes",
        database="wyscout_raw_production",
        sql=open(
            workdir + "/src/queries/new_queries/create_scaling_attributes.sql"
        ).read(),
    )

    def migration_scheduler():
        def job_that_executes_once():
            return schedule.CancelJob

        schedule.every().day.at("02:00").do(job_that_executes_once)

        while len(schedule.jobs) > 0:
            schedule.run_pending()
            time.sleep(60)

    create_indexes = PostgresOperator(
        task_id="create_indexes",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_indexes_v2.sql").read(),
    )

    update_priority_players_table = PostgresOperator(
        task_id="update_priority_players_table",
        database="wyscout_raw_production",
        sql=open(
            workdir + "/src/queries/update_priority_players_table.sql"
        ).read(),
    )

    update_agg_rating_table = PostgresOperator(
        task_id="update_agg_rating_table",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/update_agg_rating_table.sql")
        .read()
    )

    score_roles = BashOperator(
        task_id="score_roles",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            arionflow_venv/bin/python3 src/features_creation/score_roles.py""",
    )

    # ritnitop_dump = BashOperator(
    #     task_id="ritnitop_dump",
    #     bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                         arionflow_venv/bin/python3 src/reporting/ritni_top/ritnitop_dump.py""",
    # )

    refresh_player_match_info_mv = PostgresOperator(
        task_id="refresh_player_match_info_mv",
        database="wyscout_raw_production",
        sql=open(
            workdir + "/src/queries/create_player_match_info_mv.sql"
        ).read(),
    )

    update_young_players_appearances = BashOperator(
        task_id="update_young_players_appearances",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/reporting/talent_hunter/create_young_players_appearances_table.py """,
    )
    create_missing_records = BashOperator(
        task_id="create_missing_records",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/wyscout/v2/scripts_for_dag/create_missing_records.py """,
    )

    vacuum_analyze = BashOperator(
        task_id="vacuum_analyze",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            arionflow_venv/bin/python3 src/queries/run_full_vacuum_queries.py""",
    )
    grouper = DummyOperator(task_id="grouper")
    grouper2 = DummyOperator(task_id="grouper2")

    grant_list = []
    for db in ["wyscout_raw_production", "wyscout_raw_development"]:
        grant_list.append(
            PostgresOperator(
                task_id="grant_all_to_all_" + db,
                database=db,
                sql=open(workdir + "/src/queries/grant_all_to_all.sql").read(),
            )
        )
    grant_to_service_accs = PostgresOperator(
        task_id="grant_to_service_accs",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/grant_services_accs.sql").read(),
    )

    refresh_agg_stats_view = PostgresOperator(
        task_id='refresh_agg_stats',
        database='wyscout_raw_production',
        sql='REFRESH MATERIALIZED VIEW derived_tables.player_aggregate_stats_view;'
    )

    refresh_player_info_mv = PostgresOperator(
        task_id='refresh_player_info_mv',
        database='wyscout_raw_production',
        sql='REFRESH MATERIALIZED VIEW wyscout.player_info2;'
    )

    refresh_team_info_mv = PostgresOperator(
        task_id='refresh_team_info_mv',
        database='wyscout_raw_production',
        sql='REFRESH MATERIALIZED VIEW wyscout.team_info2;'
    )


    # NOTE player/mat
    (
        update_areas
        >> update_competitions
        >> update_seasons
        >> update_teams
        >> update_players
        >> update_transfers
        >> update_seasons_teams
        >> update_seasons_players
        >> update_fixtures
        >> grouper2
        >> [
            update_match_objects,
            derive_advanced_stats,
        ]
        >> collect_match_info_for_empty_games
        >> update_rounds
        >> update_seasons_matches  # this needs to happen after collection of match objects in order to satisfy the
        # constraint of seasons_matches to matches foreign key
        >> refresh_player_match_info_mv
        >> update_historical_elo 
        >> [update_scaling_attributes, update_agg_rating_table]
        >> update_leagues_standings
        >> update_cups_standings
        >> grouper
        >> create_indexes
        >> create_missing_records
        >> vacuum_analyze
        >> grant_list
        >> grant_to_service_accs
    )
    score_roles >> vacuum_analyze
    refresh_player_match_info_mv >> refresh_agg_stats_view >> [
        # ritnitop_dump,
        update_young_players_appearances,
        update_priority_players_table, 
    ]
    update_historical_elo >> [score_roles, refresh_team_info_mv]
    update_players >> refresh_player_info_mv
