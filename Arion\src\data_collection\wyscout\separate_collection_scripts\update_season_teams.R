# Get Season Matches ----
source('src/data_collection/wyscout/00_libs.R')
owt = as.logical(Sys.getenv("OVERWRITE_SEASON_TABLES"))
try({dbDisconnect(con)})
con = make_connection()

competition_seasons = dbReadTable(con, 'competition_seasons')
competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')
competition_seasons = competition_seasons[competition_seasons$competitionId%in%competitions$competitionId,]

if(!owt){
  
  downloaded_seasons = dbGetQuery(con, 'select distinct "seasonId" from seasons_matches')

  # Get final season for each competition
  final_seasons = competition_seasons %>%
    group_by(competitionId) %>%
    summarise(seasonId = max(seasonId, na.rm = T))

  competition_seasons = competition_seasons[!competition_seasons$seasonId%in%downloaded_seasons$seasonId,]

  # Combine missing seasons and final competition seasons
  competition_seasons = rbind(competition_seasons, final_seasons)

  # Get unique seasons
  competition_seasons = unique(competition_seasons)
  
  written_season_teams = dbGetQuery(con, 'select distinct "seasonId", "teamId" from seasons_teams')

}

seasons_teams = list()
cnt = 0
for (comp_id in competition_seasons$seasonId[1:length(competition_seasons$seasonId)]) {
  cnt = cnt + 1
  # print(cnt)
  try({
    
    Sys.sleep(0.08)
    ## Get Teams
    resp = GET(
      paste0(
        'https://apirest.wyscout.com/v2/seasons/',
        comp_id,
        '/teams'
      ),
      authenticate(wyscout_username, wyscout_pass)
    )
    try({
      cont = content(resp)
      if(length(cont$teams)>0){
        tms = cont$teams
        tms = lapply(tms, remove_null)
        comp_teams = plyr::ldply(tms, data.frame, stringsAsFactors = F)
        #comp_teams['competition'] = comp_id # Ask Daniel What to do with the children teams
        comp_teams = comp_teams[!grepl('children', colnames(comp_teams))]
        comp_teams['seasonId'] = comp_id
        if(!is.null(seasons_teams)){
          seasons_teams[setdiff(names(comp_teams), names(seasons_teams))] = NA
          comp_teams[setdiff(names(seasons_teams), names(comp_teams))] = NA
        }
        seasons_teams[[length(seasons_teams) + 1]] = comp_teams
      }
    })
  })
  
  
}

seasons_teams = do.call(plyr::rbind.fill, seasons_teams)

colnames(seasons_teams) = gsub('.', '_', colnames(seasons_teams), fixed = T)
colnames(seasons_teams) = gsub('wyId', 'teamId', colnames(seasons_teams))
seasons_teams = seasons_teams[!duplicated(seasons_teams[c('teamId', 'seasonId')]),]

if(!owt){
  
  seasons_teams = seasons_teams[!paste(seasons_teams$teamId, seasons_teams$seasonId) %in%
                                  paste(written_season_teams$teamId, written_season_teams$seasonId),]
}


try({dbDisconnect(con)})
con = make_connection()

# idx = which(!duplicated(season$matchId))
# seasons_matches = seasons_matches[idx,]
colnames(seasons_teams) = gsub('.', '_', colnames(seasons_teams), fixed = T)
RPostgreSQL::dbWriteTable(
  con,
  'seasons_teams',
  seasons_teams,
  overwrite = owt,
  append = !owt,
  row.names = F,
  rownames = F
)