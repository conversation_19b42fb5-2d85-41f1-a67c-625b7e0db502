import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "refresh_teams_dag",
    "start_date": datetime(2021, 12, 7),
    "schedule_interval": timedelta(days=30),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}


with DAG(**dag_params) as dag:
    get_teams_from_transfers = BashOperator(
        task_id = "get_teams_from_transfers",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/get_teams_from_transfers.py
                        """
    )

    populate_progresss_table = PostgresOperator(
        task_id = "populate_progress_table",
        sql = """INSERT INTO meta_scraping.tm_teams_progress SELECT team_id::INTEGER, team_url FROM meta_scraping.tm_teams_urls""",
        database = "wyscout_raw_production"
    )

    rescrape_teams = BashOperator(
        task_id = "rescrape_teams",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/rescrape_tm_teams.py
                           """,
    )

    check_if_teams_are_ready_for_migration = BashOperator(
    task_id = "check_if_teams_are_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_teams_before_migration.py
                           """,
    )

    save_cleaned_teams = BashOperator(
        task_id="save_cleaned_teams",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_teams.py
                           """,
    )




(
    get_teams_from_transfers
    >>
    populate_progresss_table
    >> rescrape_teams
    >> [check_if_teams_are_ready_for_migration]
)

(
    check_if_teams_are_ready_for_migration
    >> save_cleaned_teams
)