import pandas as pd


class MatchPrepper:
    def prep(self, match):
        teamsData = match.pop("teamsData")
        # keeping relevant info:
        relevant_keys = [
            "wyId",
            "label",
            "dateutc",
            "status",
            "duration",
            "winner",
            "competitionId",
            "seasonId",
            "roundId",
            "gameweek",
            "hasDataAvailable",
            "venue",
            "referees",
        ]
        filt_dict = {k: v for k, v in match.items() if k in relevant_keys}
        # formatting teams data
        if teamsData is not None:
            relevant_team_keys = [
                "teamId",
                "side",
                "score",
                "scoreHT",
                "scoreET",
                "scoreP",
                "coachId",
            ]
            for team in teamsData.values():
                side = team["side"]
                for key in relevant_team_keys:
                    filt_dict[f"{side}_{key}"] = team[key]
        # renaming matchId and date
        filt_dict["matchId"] = filt_dict.pop("wyId")
        filt_dict["date"] = filt_dict.pop("dateutc")
        df = pd.DataFrame([filt_dict])
        # required because wyscout inputs some fucked dates apparently:
        df["date"] = pd.to_datetime(df["date"], errors="coerce")
        return {"matches": df}
