from abc import ABC, abstractmethod
from datetime import datetime
import json
import re
import time
from tenacity.stop import stop_after_attempt
from tenacity import retry
from src.data_collection.new_scraping.scraping.father_scraper import Scraper
from settings import PROXIES
import pandas as pd
import requests
import numpy as np
from urllib.parse import unquote

class ScrapeOddsPortal(Scraper, ABC):
    headers = {
        "authority": "fb.oddsportal.com",
        "sec-ch-ua": '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
        "sec-ch-ua-mobile": "?1",
        "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Mobile Safari/537.36",
        "sec-ch-ua-platform": '"Android"',
        "accept": "*/*",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-dest": "script",
        "referer": "https://www.oddsportal.com/",
        "accept-language": "en-US,en;q=0.9",
        "cookie": "_gid=GA1.2.1535347184.1640001592; _ga=GA1.2.471828136.1640001592; _gat_UA-821699-19=1; _ga_5YY4JY41P1=GS1.1.1640086973.5.0.1640086981.0",
    }

    proxies = {"https": PROXIES}

    timestamp_now = int(datetime.now().timestamp()) + 700000

    @abstractmethod
    async def loop_through_urls(self, league_url) -> 'list[pd.DataFrame]':
        pass

    @retry(
    stop=stop_after_attempt(5),
    )
    async def extract_data_from_js(self, tup):  # sourcery no-metrics
        date, url = tup[0]
        home, away, id = tup[1]

        time_now_ms = int(round(time.time() * 1000))

        match_dict = {
            "match_id": np.nan,  #
            "home_name": np.nan,  #
            "away_name": np.nan,  #
            "match_date": np.nan,  #
        }

        all_odds_dict = {}

        odds_dict = {
            "home_odd": np.nan,  #
            "away_odd": np.nan,  #
            "draw_odd": np.nan,  #
            "over_0_5": np.nan,
            "under_0_5": np.nan,
            "over_1_5": np.nan,
            "under_1_5": np.nan,
            "over_2_5": np.nan,
            "under_2_5": np.nan,
            "over_3_5": np.nan,
            "under_3_5": np.nan,
            "over_4_5": np.nan,
            "under_4_5": np.nan,
            "over_5_5": np.nan,
            "under_5_5": np.nan,
            "both_to_score": np.nan, #
            "both_not_to_score": np.nan, #
            "bookie_id": np.nan, #
            "match_id": id,  #
            "date_collected": str(datetime.today().date()), #
        }

        match_dict["home_name"] = home
        match_dict["away_name"] = away
        match_dict["match_id"] = id
        match_dict["match_date"] = str(date)


        match_page = self.get_response(url)

        page_json = json.loads(re.findall(r"PageEvent\(({.*})\)", match_page)[0])

        key = unquote(page_json["xhash"])
        o_x_t_text = self.get_response(
            f"https://fb.oddsportal.com/feed/match/1-1-{id}-1-2-{key}.dat?_={time_now_ms}"
        )
        o_u_text = self.get_response(
            f"https://fb.oddsportal.com/feed/match/1-1-{id}-2-2-{key}.dat?_={time_now_ms}"
        )
        bts_text = self.get_response(
            f"https://fb.oddsportal.com/feed/match/1-1-{id}-13-2-{key}.dat?_={time_now_ms}"
        )
        
        o_x_t_data = json.loads(re.findall(r"\s({.*})", o_x_t_text)[0])
        o_u_data = json.loads(re.findall(r"\s({.*})", o_u_text)[0])
        bts_data = json.loads(re.findall(r"\s({.*})", bts_text)[0])
        try:
            o_x_t_data["d"]["oddsdata"]
        except: 
            print(o_x_t_data)
        for b_id, values in o_x_t_data["d"]["oddsdata"]["back"]["E-1-2-0-0-0"]["odds"].items():
            all_odds_dict[b_id] = odds_dict.copy()
            all_odds_dict[b_id]['bookie_id'] = b_id
            try:
                all_odds_dict[b_id]['home_odd'] = values['0']
            except:
                all_odds_dict[b_id]['home_odd'] = values[0]
            try:
                all_odds_dict[b_id]['draw_odd'] = values['1']
            except:
                all_odds_dict[b_id]['draw_odd'] = values[1]
            try:
                all_odds_dict[b_id]['away_odd'] = values['2']
            except:
                all_odds_dict[b_id]['away_odd'] = values[2]

        for b_id, values in bts_data["d"]["oddsdata"]["back"]["E-13-2-0-0-0"]["odds"].items():
        # Handling Both team to score data
            try:
                # Try if the odds are dicts - 1 is for No and 0 is for Yes
                bts_n = values['1']
                bts_y = values['0']
            except Exception as e:
                # Its array here - 0 is for Yes and 1 is for No
                bts_n = values[1]
                bts_y = values[0]
            if b_id not in all_odds_dict:
                all_odds_dict[b_id] = odds_dict.copy()
            all_odds_dict[b_id]['both_to_score'] = bts_y
            all_odds_dict[b_id]["both_not_to_score"] = bts_n
        # Handling Over/Under
        # Over 0.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-0.5-0"]["odds"].items():
            if b_id not in all_odds_dict:
                all_odds_dict[b_id] = odds_dict.copy()
                all_odds_dict[b_id]['bookie_id'] = b_id
            try:
                all_odds_dict[b_id]['over_0_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_0_5'] = values['0']
        #Under 0.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-0.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_0_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_0_5'] = values['1']
        #Over 1.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-1.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['over_1_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_1_5'] = values['0']
        #Under 1.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-1.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_1_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_1_5'] = values['1']
        #Over 2.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-2.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['over_2_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_2_5'] = values['0']
        #Under 2.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-2.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_2_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_2_5'] = values['1']
        #Over 3.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-3.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['over_3_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_3_5'] = values['0']
        #Under 3.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-3.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_3_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_3_5'] = values['1']
        #Over 4.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-4.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['over_4_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_4_5'] = values['0']
        #Under 4.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-4.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_4_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_4_5'] = values['1']
        #Over 5.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-5.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['over_5_5'] = values[0]
            except:
                all_odds_dict[b_id]['over_5_5'] = values['0']
        #Under 5.5
        for b_id, values in o_u_data["d"]["oddsdata"]["back"]["E-2-2-0-5.5-0"]["odds"].items():
            try:
                all_odds_dict[b_id]['under_5_5'] = values[1]
            except:
                all_odds_dict[b_id]['under_5_5'] = values['1']

        return (all_odds_dict, match_dict)

    def get_response(self, url: str) -> str:
        return requests.get(url, headers=self.headers, proxies=self.proxies).text
