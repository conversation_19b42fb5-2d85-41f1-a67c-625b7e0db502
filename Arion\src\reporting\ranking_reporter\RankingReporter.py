from datetime import datetime, timedelta
import requests
import json

import pandas as pd
from src.reporting.Reporter import Reporter
from src.helper_funcs import get_sql_array_str, fast_read_sql
from settings import RANKS_URL, RANKS_PASS, RANKS_USR

class RankingReporter(Reporter):
    """
    This class is responsible for reporting based on the model_config
    """

    def __init__(
        self, model_config: dict, outputs_config: dict, html_template: str
    ):
        """
        Constructor.
        """
        super().__init__()
        self.model_config = model_config
        self.outputs_config = outputs_config
        self.html_template = html_template
        self.initial_df_dict = {}  # stores the parsed responses from the api
        self.prepped_df_dict = {}  # stores formated, joined with reference info, split by comp dfs

    def get_info_for_batch(self, df: pd.DataFrame) -> pd.DataFrame:
        info = fast_read_sql(
            f"""SELECT * from wyscout.player_info
        WHERE "playerId" IN {get_sql_array_str(df["playerId"].tolist())} """,
            self.cnx_prod,
        )
        return pd.merge(df, info, on="playerId")

    @staticmethod
    def process_model_resp(resp: dict) -> pd.DataFrame:
        df = pd.DataFrame(resp)
        df = df.reset_index().rename(columns={"index": "playerId"})
        df["playerId"] = df["playerId"].astype(int)
        return df

    def format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        model_cols = [
            x
            for x in df.columns
            if any(x.endswith(y) for y in ["_reg", "_str", "_rat"])
        ]
        df = df[
            self.outputs_config["before_model_cols"]
            + model_cols
            + self.outputs_config["after_model_cols"]
        ]
        # rounding model cols to 3 digits:
        for col in model_cols:
            df[col] = df[col].apply(lambda x: round(x, 3))
        # rounding role cols to 1 digit:
        for col in df.columns:
            if col.endswith("_position_percent"):
                df[col] = df[col].apply(lambda x: round(x, 1))

    def split_df_by_comp(self, df: pd.DataFrame) ->pd.DataFrame:
        pass

    def get_data(self):
        for pos in self.model_config["positions"]:
            payload = {}
            for k, v in self.model_config['model_params'].items():
                payload[k]  = v
            for param in (
                "target_position",
                "comparable_positions",
            ):
                payload[param] = pos[param]
            payload["var_weights"] = json.dumps(pos["variables"])
            payload["str_vars"] = json.dumps(
                {k: v for k, v in pos["variables"].items() if "str" in k}
            )
            payload["window_end"] = str(datetime.today().date())
            payload["windowd_start"] = str(
                datetime.today().date()
                - timedelta(
                    days=self.model_config["model_params"]["n_days_window"]
                )
            )

            print(payload)
            resp = requests.post(
                RANKS_URL, params=payload, auth=(RANKS_USR, RANKS_PASS)
            )
            resp.raise_for_status()
            self.initial_df_dict[pos] = self.process_model_resp(
                resp.json()["player_ranks"]
            )
    def make_report(self):
        pass


with open("src\\reporting\\ranking_reporter\\sample_config.json") as config_json:
    config = json.load(config_json)
with open("src\\reporting\\ranking_reporter\\sample_output_config.json") as output_json:
    output_config = json.load(output_json)
report = RankingReporter(config, output_config, "html")

report.get_data()


#variables - var_weights