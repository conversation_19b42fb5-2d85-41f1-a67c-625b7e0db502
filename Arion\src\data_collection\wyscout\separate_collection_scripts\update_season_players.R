source('src/data_collection/wyscout/00_libs.R')

try({dbDisconnect(con)})
con = make_connection()

owt = as.logical(Sys.getenv("OVERWRITE_SEASON_TABLES"))
competition_seasons = dbReadTable(con, 'competition_seasons')
competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')
competition_seasons = competition_seasons[competition_seasons$competitionId%in%competitions$competitionId,]

if(!owt){
  
  downloaded_seasons = dbGetQuery(con, 'select distinct "seasonId" from seasons_matches')

  # Get final season for each competition
  final_seasons = competition_seasons %>%
    group_by(competitionId) %>%
    summarise(seasonId = max(seasonId, na.rm = T))

  competition_seasons = competition_seasons[!competition_seasons$seasonId%in%downloaded_seasons$seasonId,]

  # Combine missing seasons and final competition seasons
  competition_seasons = rbind(competition_seasons, final_seasons)

  # Get unique seasons
  competition_seasons = unique(competition_seasons)
  
  written_season_players = dbGetQuery(con, 'select distinct "seasonId", "playerId" from seasons_players')

}

seasons_players = list()

cnt = 0
for (comp_id in competition_seasons$seasonId[1:length(competition_seasons$seasonId)]) {
  cnt = cnt + 1
  # print(cnt)
  try({
    
    ## Get Players
    try({
      resp = GET(
        paste0(
          'https://apirest.wyscout.com/v2/seasons/',
          comp_id,
          '/players'
        ),
        authenticate(wyscout_username, wyscout_pass)
      )
      #cnt = cnt + 1
      cont = content(resp)
      if(length(cont$players)>0){
        
        cps = cont$players
        cps = lapply(cps, remove_null)
        cps = lapply(cps, function(x) lapply(x, remove_null))
        comp_players = plyr::ldply(cps, flatten_squad_data)
        comp_players['seasonId'] = comp_id
        
        colnames(comp_players) = gsub('wyId', 'playerId', colnames(comp_players))
        colnames(comp_players) = gsub('.', '_', colnames(comp_players), fixed = T)
        seasons_players[[length(seasons_players) + 1]] = comp_players
        
        # RPostgreSQL::dbWriteTable(
        #   con,
        #   'competition_players',
        #   comp_players,
        #   overwrite = owt,
        #   append = !owt,
        #   row.names = F,
        #   rownames = F
        # )
      }
      
    })
    
  })
  
  
}

seasons_players = do.call(plyr::rbind.fill, seasons_players)
colnames(seasons_players) = gsub('.', '_', colnames(seasons_players), fixed = T)
colnames(seasons_players) = gsub('wyId', 'playerId', colnames(seasons_players))
seasons_players = seasons_players[!duplicated(seasons_players[c('playerId', 'seasonId')]),]

if(!owt){

  seasons_players = seasons_players[!paste(seasons_players$playerId, seasons_players$seasonId) %in%
                                      paste(written_season_players$playerId, written_season_players$seasonId),]

}

try({dbDisconnect(con)})
con = make_connection()

colnames(seasons_players) = gsub('.', '_', colnames(seasons_players), fixed = T)
RPostgreSQL::dbWriteTable(
  con,
  'seasons_players',
  seasons_players,
  overwrite = owt,
  append = !owt,
  row.names = F,
  rownames = F
)