import pandas as pd
import numpy as np


class LineupsPrepper:
    def calculate_minutes_tagged(self, lineups, ts):
        """Calculates minutesTagged

        Args:
            lineups (pd.DataFrame): df prepped by LineupsPrepper

        Returns:
            pd.DataFrame: same df with additional minutesTagged column
        """
        lineups = lineups.copy()
        lineups["duration"] = ts["total_duration"]
        adjusted_cols_list = []
        for col in ["sent_off_min", "subbed_out_min", "subbed_in_min"]:
            adjusted_in_secs = lineups[col] * 60
            adjusted_cols_list.append(f"{col}_adjusted")
            lineups[f"{col}_adjusted"] = np.where(
                adjusted_in_secs.isnull(),
                adjusted_in_secs,
                np.where(
                    adjusted_in_secs.fillna(0) > ts["first_half_end"],
                    (ts["first_half_duration"] + adjusted_in_secs - 45 * 60)
                    / 60,
                    (adjusted_in_secs - ts["first_half_start"]) / 60,
                ),
            )

        start_min = np.where(
            lineups["starting_lineup"] == 1,
            0,
            lineups["subbed_in_min_adjusted"],
        )

        out_min = lineups[
            ["duration"]
            + [x for x in adjusted_cols_list if "subbed_in" not in x]
        ].min(axis=1)
        lineups["minutesTagged"] = out_min - start_min
        lineups = lineups.drop(columns=["duration"] + adjusted_cols_list)
        lineups["minutesTagged"] = (
            lineups["minutesTagged"]
            .fillna(0)
            .astype(float)
            .round()
            .astype("Int64")
        )
        return lineups

    def prep(self, match, ts):
        lineups = []
        for teamId, data in match["teamsData"].items():
            temp_start = pd.DataFrame(data["formation"]["lineup"])
            temp_bench = pd.DataFrame(data["formation"]["bench"])
            sub_list = data["formation"]["substitutions"]
            subs_happened = len(sub_list) > 0
            if subs_happened:

                subs = pd.concat(
                    [
                        pd.DataFrame(
                            columns=[
                                "playerIn",
                                "playerOut",
                                "minute",
                                "assists",
                            ]
                        ),
                        pd.DataFrame(sub_list),
                    ],
                )
                subs = subs.fillna(-1)
                if (
                    len(
                        subs[
                            (subs.playerOut.notnull())
                            & (subs.playerIn.isnull())
                        ]
                    )
                    > 0
                ):
                    print(match["wyId"], sub_list)
                subs = subs.rename(columns={"minute": "substitutionMinute"})
                in_out_dict = pd.Series(
                    subs.playerOut.values, index=subs.playerIn
                ).to_dict()
                out_in_dict = pd.Series(
                    subs.playerIn.values, index=subs.playerOut
                ).to_dict()
                temp_bench["subbed_in_for"] = temp_bench["playerId"].map(
                    in_out_dict
                )
                temp_bench["subbed_out_for"] = temp_bench["playerId"].map(
                    out_in_dict
                )
                temp_start["subbed_out_for"] = temp_start["playerId"].map(
                    out_in_dict
                )
            else:
                temp_bench["subbed_in_for"] = np.nan
                temp_bench["subbed_out_for"] = np.nan
                temp_start["subbed_out_for"] = np.nan
            temp_start["starting_lineup"] = 1
            temp_bench["starting_lineup"] = 0
            temp_lineup = pd.concat([temp_start, temp_bench])
            temp_lineup["matchId"] = match["wyId"]
            temp_lineup["teamId"] = int(teamId)
            temp_lineup["sent_off_min"] = (
                temp_lineup["redCards"]
                .replace("0", np.nan)
                .astype(float)
                .astype("Int64")
            )
            temp_lineup = temp_lineup[
                [
                    "playerId",
                    "teamId",
                    "matchId",
                    "subbed_out_for",
                    "subbed_in_for",
                    "sent_off_min",
                    "starting_lineup",
                ]
            ]
            if subs_happened:
                temp_lineup = pd.merge(
                    temp_lineup,
                    subs[["substitutionMinute", "playerOut"]].rename(
                        columns={
                            "playerOut": "subbed_in_for",
                            "substitutionMinute": "subbed_in_min",
                        }
                    ),
                    how="left",
                    on="subbed_in_for",
                )
                temp_lineup = pd.merge(
                    temp_lineup,
                    subs[["substitutionMinute", "playerIn"]].rename(
                        columns={
                            "playerIn": "subbed_out_for",
                            "substitutionMinute": "subbed_out_min",
                        }
                    ),
                    how="left",
                    on="subbed_out_for",
                )

            for col in [
                "subbed_out_for",
                "subbed_in_for",
                "subbed_in_min",
                "subbed_out_min",
            ]:
                if not subs_happened:
                    temp_lineup[col] = np.nan
            numeric_cols = temp_lineup._get_numeric_data().columns
            for num_col in numeric_cols:
                temp_lineup[num_col] = temp_lineup[num_col].astype("Int64")
            temp_lineup = self.calculate_minutes_tagged(temp_lineup, ts)
            # dropping duplicates because wyscout suck:
            temp_lineup = temp_lineup.drop_duplicates(subset=["playerId"])
            lineups.append(temp_lineup)

        cc = pd.concat(lineups)
        cc = cc.drop_duplicates(subset=['playerId', 'matchId'])
        check = cc[cc.playerId.isnull()]
        if len(check) > 0:
            print(check.matchId.tolist())
        return {"lineups": cc}

    def write(self):
        pass
