with main_pos as (
	select distinct on ("playerId") pos."playerId",
		pos."position" -- corresponds to all_players.code2, but with case difference
,
		sum(pos."percent") total_pct
	from wyscout.player_match_positions pos
	group by "playerId",
		"position"
	order by "playerId",
		total_pct desc
) 
,
competition_filter as (
	select competitions."competitionId"
	from wyscout.competitions competitions
	where 1 = 1
		and competitions."format" = 'Domestic league' --		and competitions.area_alpha2code = 'EN'
		and position(lower("name") in 'friend') = 0
		and "divisionLevel" = 1 --	limit 1
),
matches as (
	select *
	from wyscout.matches matches
	where matches."competitionId" in (
			select "competitionId"
			from competition_filter
		)
),
team_sides as (
	select "matchId",
		"home_teamId" as "teamId",
		'home' as side
	from matches m
	union
	select "matchId",
		"away_teamId" as "teamId",
		'away' as side
	from matches m
),
team_lineups_first as (
	select wyscout.lineups."playerId",
		wyscout.lineups."teamId",
		wyscout.lineups."matchId",
		wyscout.lineups."subbed_out_for",
		wyscout.lineups."subbed_in_for",
		wyscout.lineups."sent_off_min",
		wyscout.lineups."starting_lineup",
		wyscout.lineups."subbed_in_min",
		wyscout.lineups."subbed_out_min",
		"side",
		case
			when starting_lineup = 1
			and subbed_out_min is null then 1
			else 0
		end as played_full_match,
		case
			when starting_lineup = 0
			and subbed_in_min is null then 0
			else greatest(1, "minutesTagged")
		end as "minutesTagged"
	from wyscout.lineups
		inner join team_sides on lineups."matchId" = team_sides."matchId"
		and lineups."teamId" = team_sides."teamId"
),
team_lineups as (
	select tlf.*,
		sq."teamId" as "opponentId"
	from team_lineups_first tlf
		left join (
			select *
			from team_sides
			where side = 'away'
		) sq on tlf."matchId" = sq."matchId"
	where 1 = 1
),
team_rating as (
	select rating."matchId",
		rating."teamId",
		rating.side,
		lag(rating.rating, 1) over (
			partition by "teamId"
			order by rating."matchId"
		) rating,
		lag(rating.smoothed_rating, 1) over (
			partition by "teamId"
			order by rating."matchId"
		) smoothed_rating
	from public.team_match_rating rating
	where 1 = 1 --		
		--	and matches."competitionId" in (select "competitionId" from competition_filter) -- Filter after feature engineering bc of teams moving up down divisions
),
team_rating_select as (
	select team_1."matchId",
		team_1."teamId" as "teamId",
		team_1."rating" as rating,
		team_1."smoothed_rating" as smoothed_rating,
		team_2."teamId" as other_team_id,
		team_2."rating" as other_rating,
		team_2."smoothed_rating" as other_smoothed_rating
	from (
			select *
			from team_rating
			where side = 'home'
		) team_1
		left join (
			select *
			from team_rating
			where side = 'away'
		) team_2 on team_1."matchId" = team_2."matchId"
		and team_1.side != team_2.side
	where 1 = 1
),
injuries as (
	select l2."playerId",
		l2."matchId",
		case
			when ti."injured_until" > date(m2."date") then 1
			else 0
		end as currently_injured
	from team_lineups_first l2
		left join transfermarkt.transfermarkt_injuries ti on l2."playerId" = ti."playerId"
		left join matches m2 on m2."matchId" = l2."matchId"
	where 1 = 1
		and ti."injured_from" < date(m2."date")
		and ti."injured_until" > date(m2."date")
),
goals as (
	select *
	from derived_tables.shots
	where is_goal = 'true'
),
goals_conceded_raw as (
	select l."playerId",
		l."matchId",
		l."teamId",
		"minutesTagged",
		case
			when starting_lineup = 1 then coalesce(l.subbed_in_min, 0)
			else l.subbed_in_min
		end as subbed_in_min,
		case
			when "minutesTagged" > 0 then coalesce(
				l.subbed_out_min,
				coalesce(subbed_in_min, 0) + "minutesTagged"
			)
			else l.subbed_out_min
		end as subbed_out_min,
		goals."minute" as concession_min,
		goals."second" as concession_second,
		case
			when "minutesTagged" > 60
			and (
				goals."minute"::bigint between case
					when starting_lineup = 1 then coalesce(l.subbed_in_min, 0)
					else l.subbed_in_min
				end
				and case
					when "minutesTagged" > 0 then coalesce(
						l.subbed_out_min,
						coalesce(subbed_in_min, 0) + "minutesTagged"
					)
					else l.subbed_out_min
				end
			) then 1
			else 0
		end as conceded_goal
	from wyscout.lineups l,
		goals
	where l."matchId" = goals."matchId"
		and l."teamId" = goals."opponentId"::bigint
),
goals_conceded as (
	select "playerId",
		"matchId",
		sum(conceded_goal) as total_concessions,
		case
			when sum(conceded_goal) > 0 then 0
			else 1
		end as clean_sheet
	from goals_conceded_raw
	group by "playerId",
		"matchId"
) --
,
player_variable_select as (
	select
		/*	Identification	*/
		lineups."playerId" -- unique key part
,
		lineups."matchId" -- unique key part
,
		90.0 as "match_duration",
		matches."date",
		lineups."teamId",
		lineups."opponentId",
		matches."competitionId",
		trs."rating",
		trs."other_rating" as opponent_rating
		/*	Base for target variables	*/
,
		all_players.role_code2 -- basic code corresponding to the role name
,
		main_pos."position" -- Demographics
,
		all_players."birthDate" "birthDate",
		all_players.height,
		case
			when all_players.height is not null
			and all_players.height > 0 then all_players.weight * 10000 / (all_players.height * all_players.height)
			else null
		end as bmi
		/*	variables	*/
,
		lineups."minutesTagged" -- Player plays at least 1 second / 60 minutes 
,
		coalesce(injuries.currently_injured, 0) currently_injured,
		lineups.starting_lineup,
		lineups.played_full_match,
		coalesce(adv.goals, 0) goals,
		gc.total_concessions total_concessions,
		gc.clean_sheet clean_sheet,
		coalesce(adv.assists, 0) assists,
		coalesce(adv."yellow_cards", 0) yellow_cards,
		coalesce(adv."red_cards", 0) red_cards,
		case
			when adv.yellow_cards = 0
			and adv.red_cards = 1 then 1
			else 0
		end as direct_red_cards,
		coalesce(adv."shots", 0) shots,
		coalesce(adv."head_shots", 0) head_shots,
		coalesce(adv."gk_shots_against", 0) gk_shots_against,
		case
			when coalesce(adv."penalty_goals", 0) + coalesce(adv."missed_penalties", 0) > 0 then coalesce(adv."penalty_goals", 0)
			else null
		end as penalty_goals,
		case
			when coalesce(adv."penalty_goals", 0) + coalesce(adv."missed_penalties", 0) > 0 then coalesce(adv."missed_penalties", 0)
			else null
		end as missed_penalties,
		coalesce(adv."penalty_goals", 0) + coalesce(adv."missed_penalties", 0) penalties,
		coalesce(adv."linkup_plays", 0) linkup_plays,
		coalesce(adv."fouls", 0) fouls,
		coalesce(adv."passes", 0) passes,
		coalesce(adv."crosses", 0) crosses,
		coalesce(adv."dribbles", 0) dribbles,
		coalesce(adv."interceptions", 0) interceptions,
		coalesce(adv."accelerations", 0) accelerations,
		coalesce(adv."shot_assists", 0) shot_assists,
		coalesce(adv."recoveries", 0) recoveries,
		coalesce(adv."losses", 0) losses,
		coalesce(adv."penalty_fouls", 0) penalty_fouls,
		coalesce(adv."penalty_conceded_goals", 0) penalty_conceded_goals,
		coalesce(adv."aerial_duels", 0) aerial_duels,
		coalesce(adv."ground_duels", 0) ground_duels,
		coalesce(adv."aerial_duels", 0) + coalesce(adv."ground_duels", 0) duels,
		coalesce(adv."passes_to_final_third", 0) passes_to_final_third,
		coalesce(adv."passes_to_penalty_area", 0) passes_to_penalty_area,
		coalesce(adv."shots_on_target", 0) shots_on_target,
		coalesce(adv."shots_outside_penalty", 0) shots_outside_penalty,
		coalesce(adv."shots_inside_penalty", 0) shots_inside_penalty,
		coalesce(adv."mean_xg", 0) mean_xg,
		coalesce(adv."sum_xg", 0) sum_xg,
		case
			when role_code2 = 'GK' then adv."gk_saves"
			else null
		end as gk_saves -- for goalkeeper
,
		case
			when role_code2 = 'GK' then coalesce(adv."gk_save_xg", 0)
			else null
		end as gk_save_xg
	from team_lineups lineups
		left join wyscout.players all_players on lineups."playerId" = all_players."playerId"
		left join wyscout.advanced_stats adv on adv."matchId" = lineups."matchId"
		and adv."playerId" = lineups."playerId"
		left join matches on lineups."matchId" = matches."matchId"
		left join main_pos on lineups."playerId" = main_pos."playerId"
		left join team_rating_select trs on lineups."matchId" = trs."matchId"
		and lineups."teamId" = trs."teamId"
		left join injuries on injuries."playerId" = lineups."playerId"
		and injuries."matchId" = lineups."matchId"
		left join goals_conceded gc on lineups."playerId" = gc."playerId"
		and lineups."matchId" = gc."matchId"
	where 1 = 1 --		and matches."competitionId" in (select "competitionId" from competition_filter)
),
team_variable_select as (
	select distinct
		/*	Identification	*/
		"matchId" -- unique key part
,
		"date",
		"teamId",
		"opponentId",
		/*	variables	*/
		sum(goals) goals,
		sum(assists) assists,
		sum(yellow_cards) yellow_cards,
		sum(red_cards) red_cards,
		sum(direct_red_cards) direct_red_cards,
		sum(shots) shots,
		sum(head_shots) head_shots,
		sum(gk_shots_against) shots_against,
		sum(penalty_goals) penalty_goals,
		sum(missed_penalties) missed_penalties,
		sum(penalties) penalties,
		sum(linkup_plays) linkup_plays,
		sum(fouls) fouls,
		sum(passes) passes,
		sum(crosses) crosses,
		sum(dribbles) dribbles,
		sum(interceptions) interceptions,
		sum(accelerations) accelerations,
		sum(shot_assists) shot_assists,
		sum(recoveries) recoveries,
		sum(losses) losses,
		sum(currently_injured) currently_injured,
		sum(penalty_fouls) penalty_fouls,
		sum(penalty_conceded_goals) penalty_conceded_goals,
		sum(aerial_duels) aerial_duels,
		sum(ground_duels) ground_duels,
		sum(duels) duels,
		sum(passes_to_final_third) passes_to_final_third,
		sum(passes_to_penalty_area) passes_to_penalty_area,
		sum(shots_on_target) shots_on_target,
		sum(shots_outside_penalty) shots_outside_penalty,
		sum(shots_inside_penalty) shots_inside_penalty,
		sum(mean_xg) mean_xg,
		sum(sum_xg) sum_xg,
		sum("gk_saves") gk_saves,
		sum(gk_save_xg) gk_save_xg
	from player_variable_select
	group by "matchId" -- unique key part
,
		"date",
		"teamId",
		"opponentId"
),
historical_team_select as(
	SELECT distinct "matchId",
		"teamId",
		"date",
		avg("goals") over w team_avg_goals,
		avg(assists) over w team_avg_assists,
		avg(yellow_cards) over w team_avg_yellowcards,
		avg(red_cards) over w team_avg_redcards,
		avg(direct_red_cards) over w team_avg_direct_redcards,
		avg(shots) over w team_avg_shots,
		avg(head_shots) over w team_avg_headshots,
		avg(shots_against) over w team_avg_shots_against,
		avg(penalty_goals) over w team_avg_penalty_goals,
		avg(missed_penalties) over w team_avg_penalty_misses,
		avg(penalties) over w team_avg_penalties,
		avg(linkup_plays) over w team_avg_linkup_plays,
		avg(fouls) over w team_avg_fouls,
		avg(passes) over w team_avg_passes,
		avg(crosses) over w team_avg_crosses,
		avg(dribbles) over w team_avg_dribbles,
		avg(interceptions) over w team_avg_interceptions,
		avg(shot_assists) over w team_avg_shot_assists,
		avg(recoveries) over w team_avg_recoveries,
		avg(losses) over w team_avg_losses,
		avg(penalty_fouls) over w team_avg_penalty_fouls,
		avg(penalty_conceded_goals) over w team_avg_penalty_conceded_goals,
		avg(aerial_duels) over w team_avg_aerial_duels,
		avg(ground_duels) over w team_avg_ground_duels,
		avg(duels) over w team_avg_duels,
		avg(passes_to_final_third) over w team_avg_passes_to_final_third,
		avg(passes_to_penalty_area) over w team_avg_passes_to_penalty_area,
		avg(shots_on_target) over w team_avg_shots_on_target,
		avg(shots_outside_penalty) over w team_avg_shots_outside_penalty,
		avg(shots_inside_penalty) over w team_avg_shots_inside_penalty,
		avg(mean_xg) over w team_avg_mean_xg,
		avg(sum_xg) over w team_avg_sum_xg,
		avg(gk_saves) over w team_avg_gk_saves,
		avg(gk_save_xg) over w team_avg_gk_save_xg
	FROM team_variable_select tt WINDOW w AS (
			PARTITION BY tt."teamId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
),
historical_opponent_select as(
	SELECT distinct "matchId",
		"opponentId",
		"date",
		avg("goals") over w opp_avg_goals,
		avg(assists) over w opp_avg_assists,
		avg(yellow_cards) over w opp_avg_yellowcards,
		avg(red_cards) over w opp_avg_redcards,
		avg(direct_red_cards) over w opp_avg_direct_redcards,
		avg(shots) over w opp_avg_shots,
		avg(head_shots) over w opp_avg_headshots,
		avg(shots_against) over w opp_avg_shots_against,
		avg(penalty_goals) over w opp_avg_penalty_goals,
		avg(missed_penalties) over w opp_avg_penalty_misses,
		avg(penalties) over w opp_avg_penalties,
		avg(linkup_plays) over w opp_avg_linkup_plays,
		avg(fouls) over w opp_avg_fouls,
		avg(passes) over w opp_avg_passes,
		avg(crosses) over w opp_avg_crosses,
		avg(dribbles) over w opp_avg_dribbles,
		avg(interceptions) over w opp_avg_interceptions,
		avg(shot_assists) over w opp_avg_shot_assists,
		avg(recoveries) over w opp_avg_recoveries,
		avg(losses) over w opp_avg_losses,
		avg(penalty_fouls) over w opp_avg_penalty_fouls,
		avg(penalty_conceded_goals) over w opp_avg_penalty_conceded_goals,
		avg(aerial_duels) over w opp_avg_aerial_duels,
		avg(ground_duels) over w opp_avg_ground_duels,
		avg(duels) over w opp_avg_duels,
		avg(passes_to_final_third) over w opp_avg_passes_to_final_third,
		avg(passes_to_penalty_area) over w opp_avg_passes_to_penalty_area,
		avg(shots_on_target) over w opp_avg_shots_on_target,
		avg(shots_outside_penalty) over w opp_avg_shots_outside_penalty,
		avg(shots_inside_penalty) over w opp_avg_shots_inside_penalty,
		avg(mean_xg) over w opp_avg_mean_xg,
		avg(sum_xg) over w opp_avg_sum_xg,
		avg(gk_saves) over w opp_avg_gk_saves,
		avg(gk_save_xg) over w opp_avg_gk_save_xg
	FROM team_variable_select tt WINDOW w AS (
			PARTITION BY tt."opponentId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
),
historical_player_select as (
	select distinct "matchId",
		"playerId",
		"date",
		sum(match_duration) over w as total_match_time,
		sum("minutesTagged" * "goals") over w per90_goals,
		sum("minutesTagged" * assists) over w per90_assists,
		sum("minutesTagged" * yellow_cards) over w per90_yellowcards,
		sum("minutesTagged" * red_cards) over w per90_redcards,
		sum("minutesTagged" * direct_red_cards) over w per90_direct_redcards,
		sum("minutesTagged" * shots) over w per90_shots,
		sum("minutesTagged" * head_shots) over w per90_headshots,
		sum("minutesTagged" * gk_shots_against) over w per90_gk_shots_against,
		sum("minutesTagged" * penalty_goals) over w per90_penalty_goals,
		sum("minutesTagged" * missed_penalties) over w per90_penalty_misses,
		sum("minutesTagged" * penalties) over w per90_penalties,
		sum("minutesTagged" * linkup_plays) over w per90_linkup_plays,
		sum("minutesTagged" * fouls) over w per90_fouls,
		sum("minutesTagged" * passes) over w per90_passes,
		sum("minutesTagged" * crosses) over w per90_crosses,
		sum("minutesTagged" * dribbles) over w per90_dribbles,
		sum("minutesTagged" * interceptions) over w per90_interceptions,
		sum("minutesTagged" * shot_assists) over w per90_shot_assists,
		sum("minutesTagged" * recoveries) over w per90_recoveries,
		sum("minutesTagged" * losses) over w per90_losses,
		sum("minutesTagged" * penalty_fouls) over w per90_penalty_fouls,
		sum("minutesTagged" * penalty_conceded_goals) over w per90_penalty_conceded_goals,
		sum("minutesTagged" * aerial_duels) over w per90_aerial_duels,
		sum("minutesTagged" * ground_duels) over w per90_ground_duels,
		sum("minutesTagged" * duels) over w per90_duels,
		sum("minutesTagged" * passes_to_final_third) over w per90_passes_to_final_third,
		sum("minutesTagged" * passes_to_penalty_area) over w per90_passes_to_penalty_area,
		sum("minutesTagged" * shots_on_target) over w per90_shots_on_target,
		sum("minutesTagged" * shots_outside_penalty) over w per90_shots_outside_penalty,
		sum("minutesTagged" * shots_inside_penalty) over w per90_shots_inside_penalty,
		sum("minutesTagged" * mean_xg) over w per90_mean_xg,
		sum("minutesTagged" * sum_xg) over w per90_sum_xg,
		case
			when role_code2 = 'GK' then sum("minutesTagged" * gk_saves) over w
			else null
		end as per90_gk_saves,
		case
			when role_code2 = 'GK' then sum("minutesTagged" * gk_save_xg) over w
			else null
		end as per90_gk_save_xg
	from (
			select *
			from player_variable_select
			where "minutesTagged" > 0
		) tt WINDOW w AS (
			PARTITION BY tt."playerId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
) 
,
per90_player_features as (
	select "matchId",
		"playerId",
		lag(per90_goals / (total_match_time), 1) over w as per90_goals,
		lag(per90_assists / (total_match_time), 1) over w per90_assists,
		lag(per90_yellowcards / (total_match_time), 1) over w per90_yellowcards,
		lag(per90_redcards / (total_match_time), 1) over w per90_redcards,
		lag(per90_direct_redcards / (total_match_time), 1) over w per90_direct_redcards,
		lag(per90_shots / (total_match_time), 1) over w per90_shots,
		lag(per90_headshots / (total_match_time), 1) over w per90_headshots,
		lag(per90_gk_shots_against / (total_match_time), 1) over w per90_gk_shots_against,
		lag(per90_penalty_goals / (total_match_time), 1) over w per90_penalty_goals,
		lag(per90_penalty_misses / (total_match_time), 1) over w per90_penalty_misses,
		lag(per90_penalties / (total_match_time), 1) over w per90_penalties,
		lag(per90_linkup_plays / (total_match_time), 1) over w per90_linkup_plays,
		lag(per90_fouls / (total_match_time), 1) over w per90_fouls,
		lag(per90_passes / (total_match_time), 1) over w per90_passes,
		lag(per90_crosses / (total_match_time), 1) over w per90_crosses,
		lag(per90_dribbles / (total_match_time), 1) over w per90_dribbles,
		lag(per90_interceptions / (total_match_time), 1) over w per90_interceptions,
		lag(per90_shot_assists / (total_match_time), 1) over w per90_shot_assists,
		lag(per90_recoveries / (total_match_time), 1) over w per90_recoveries,
		lag(per90_losses / (total_match_time), 1) over w per90_losses,
		lag(per90_penalty_fouls / (total_match_time), 1) over w per90_penalty_fouls,
		lag(
			per90_penalty_conceded_goals / (total_match_time),
			1
		) over w per90_penalty_conceded_goals,
		lag(per90_aerial_duels / (total_match_time), 1) over w per90_aerial_duels,
		lag(per90_ground_duels / (total_match_time), 1) over w per90_ground_duels,
		lag(per90_duels / (total_match_time), 1) over w per90_duels,
		lag(
			per90_passes_to_final_third / (total_match_time),
			1
		) over w per90_passes_to_final_third,
		lag(
			per90_passes_to_penalty_area / (total_match_time),
			1
		) over w per90_passes_to_penalty_area,
		lag(per90_shots_on_target / (total_match_time), 1) over w per90_shots_on_target,
		lag(
			per90_shots_outside_penalty / (total_match_time),
			1
		) over w per90_shots_outside_penalty,
		lag(
			per90_shots_inside_penalty / (total_match_time),
			1
		) over w per90_shots_inside_penalty,
		lag(per90_mean_xg / (total_match_time), 1) over w per90_mean_xg,
		lag(per90_sum_xg / (total_match_time), 1) over w per90_sum_xg,
		lag(per90_gk_saves / (total_match_time), 1) over w per90_gk_saves,
		lag(per90_gk_save_xg / (total_match_time), 1) over w per90_gk_save_xg
	from historical_player_select tt
	where total_match_time > 0 WINDOW w AS (
			PARTITION BY tt."playerId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
	order by "playerId",
		"matchId"
),
team_feature_select as (
	SELECT distinct "matchId",
		"teamId",
		lag("team_avg_goals", 1) over w team_avg_goals,
		lag(team_avg_assists, 1) over w team_avg_assists,
		lag(team_avg_yellowcards, 1) over w team_avg_yellowcards,
		lag(team_avg_redcards, 1) over w team_avg_redcards,
		lag(team_avg_direct_redcards, 1) over w team_avg_direct_redcards,
		lag(team_avg_shots, 1) over w team_avg_shots,
		lag(team_avg_headshots, 1) over w team_avg_headshots,
		lag(team_avg_shots_against, 1) over w team_avg_shots_against,
		lag(team_avg_penalty_goals, 1) over w team_avg_penalty_goals,
		lag(team_avg_penalty_misses, 1) over w team_avg_penalty_misses,
		lag(team_avg_penalties, 1) over w team_avg_penalties,
		lag(team_avg_linkup_plays, 1) over w team_avg_linkup_plays,
		lag(team_avg_fouls, 1) over w team_avg_fouls,
		lag(team_avg_passes, 1) over w team_avg_passes,
		lag(team_avg_crosses, 1) over w team_avg_crosses,
		lag(team_avg_dribbles, 1) over w team_avg_dribbles,
		lag(team_avg_interceptions, 1) over w team_avg_interceptions,
		lag(team_avg_shot_assists, 1) over w team_avg_shot_assists,
		lag(team_avg_recoveries, 1) over w team_avg_recoveries,
		lag(team_avg_losses, 1) over w team_avg_losses,
		lag(team_avg_penalty_fouls, 1) over w team_avg_penalty_fouls,
		lag(team_avg_penalty_conceded_goals, 1) over w team_avg_penalty_conceded_goals,
		lag(team_avg_aerial_duels, 1) over w team_avg_aerial_duels,
		lag(team_avg_ground_duels, 1) over w team_avg_ground_duels,
		lag(team_avg_duels, 1) over w team_avg_duels,
		lag(team_avg_passes_to_final_third) over w team_avg_passes_to_final_third,
		lag(team_avg_passes_to_penalty_area) over w team_avg_passes_to_penalty_area,
		lag(team_avg_shots_on_target, 1) over w team_avg_shots_on_target,
		lag(team_avg_shots_outside_penalty, 1) over w team_avg_shots_outside_penalty,
		lag(team_avg_shots_inside_penalty, 1) over w team_avg_shots_inside_penalty,
		lag(team_avg_mean_xg, 1) over w team_avg_mean_xg,
		lag(team_avg_sum_xg, 1) over w team_avg_sum_xg,
		lag(team_avg_gk_saves, 1) over w team_avg_gk_saves,
		lag(team_avg_gk_save_xg, 1) over w team_avg_gk_save_xg
	FROM historical_team_select tt WINDOW w AS (
			PARTITION BY tt."teamId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
),
opponent_feature_select as (
	SELECT distinct "matchId",
		"opponentId",
		lag("opp_avg_goals", 1) over w opp_avg_goals,
		lag(opp_avg_assists, 1) over w opp_avg_assists,
		lag(opp_avg_yellowcards, 1) over w opp_avg_yellowcards,
		lag(opp_avg_redcards, 1) over w opp_avg_redcards,
		lag(opp_avg_direct_redcards, 1) over w opp_avg_direct_redcards,
		lag(opp_avg_shots, 1) over w opp_avg_shots,
		lag(opp_avg_headshots, 1) over w opp_avg_headshots,
		lag(opp_avg_shots_against, 1) over w opp_avg_shots_against,
		lag(opp_avg_penalty_goals, 1) over w opp_avg_penalty_goals,
		lag(opp_avg_penalty_misses, 1) over w opp_avg_penalty_misses,
		lag(opp_avg_penalties, 1) over w opp_avg_penalties,
		lag(opp_avg_linkup_plays, 1) over w opp_avg_linkup_plays,
		lag(opp_avg_fouls, 1) over w opp_avg_fouls,
		lag(opp_avg_passes, 1) over w opp_avg_passes,
		lag(opp_avg_crosses, 1) over w opp_avg_crosses,
		lag(opp_avg_dribbles, 1) over w opp_avg_dribbles,
		lag(opp_avg_interceptions, 1) over w opp_avg_interceptions,
		lag(opp_avg_shot_assists, 1) over w opp_avg_shot_assists,
		lag(opp_avg_recoveries, 1) over w opp_avg_recoveries,
		lag(opp_avg_losses, 1) over w opp_avg_losses,
		lag(opp_avg_penalty_fouls, 1) over w opp_avg_penalty_fouls,
		lag(opp_avg_penalty_conceded_goals, 1) over w opp_avg_penalty_conceded_goals,
		lag(opp_avg_aerial_duels, 1) over w opp_avg_aerial_duels,
		lag(opp_avg_ground_duels, 1) over w opp_avg_ground_duels,
		lag(opp_avg_duels, 1) over w opp_avg_duels,
		lag(opp_avg_passes_to_final_third) over w opp_avg_passes_to_final_third,
		lag(opp_avg_passes_to_penalty_area) over w opp_avg_passes_to_penalty_area,
		lag(opp_avg_shots_on_target, 1) over w opp_avg_shots_on_target,
		lag(opp_avg_shots_outside_penalty, 1) over w opp_avg_shots_outside_penalty,
		lag(opp_avg_shots_inside_penalty, 1) over w opp_avg_shots_inside_penalty,
		lag(opp_avg_mean_xg, 1) over w opp_avg_mean_xg,
		lag(opp_avg_sum_xg, 1) over w opp_avg_sum_xg,
		lag(opp_avg_gk_saves, 1) over w opp_avg_gk_saves,
		lag(opp_avg_gk_save_xg, 1) over w opp_avg_gk_save_xg
	FROM historical_opponent_select tt WINDOW w AS (
			PARTITION BY tt."opponentId"
			ORDER BY DATE(tt."date") RANGE INTERVAL '6' MONTH PRECEDING
		)
),
final_select as (
	select -- Identifiers
		pvs."playerId",
		pvs."matchId",
		pvs."date",
		pvs."teamId",
		pvs."opponentId",
		pvs."competitionId" -- ELO
,
		pvs.rating,
		pvs.opponent_rating -- Injuries
,
		pvs.currently_injured -- Role
,
		pvs.role_code2 -- basic code corresponding to the role name
,
		pvs."position" -- Demographics
,
		pvs."birthDate",
		pvs.height,
		pvs.bmi 
		
		-- Target Variables
,
		pvs."minutesTagged" --	, pvs.starting_lineup
,
		pvs.played_full_match,
		pvs.goals,
		pvs.assists,
		pvs.yellow_cards,
		pvs.red_cards,
		pvs.direct_red_cards,
		pvs.gk_saves,
		pvs.missed_penalties,
		pvs.penalty_fouls,
		pvs.clean_sheet,
		pvs.total_concessions,

		-- Historical per 90 averages of players
		ppf.per90_goals,
		ppf.per90_assists,
		ppf.per90_yellowcards,
		ppf.per90_redcards,
		ppf.per90_direct_redcards,
		ppf.per90_shots,
		ppf.per90_headshots,
		ppf.per90_gk_shots_against,
		ppf.per90_penalty_goals,
		ppf.per90_penalty_misses,
		ppf.per90_penalties,
		ppf.per90_linkup_plays,
		ppf.per90_fouls,
		ppf.per90_passes,
		ppf.per90_crosses,
		ppf.per90_dribbles,
		ppf.per90_interceptions,
		ppf.per90_shot_assists,
		ppf.per90_recoveries,
		ppf.per90_losses,
		ppf.per90_penalty_fouls,
		ppf.per90_penalty_conceded_goals,
		ppf.per90_aerial_duels,
		ppf.per90_ground_duels,
		ppf.per90_duels,
		ppf.per90_passes_to_final_third,
		ppf.per90_passes_to_penalty_area,
		ppf.per90_shots_on_target,
		ppf.per90_shots_outside_penalty,
		ppf.per90_shots_inside_penalty,
		ppf.per90_mean_xg,
		ppf.per90_sum_xg,
		ppf.per90_gk_saves,
		ppf.per90_gk_save_xg,
		-- Historical per match averages of player's team
		tfs.team_avg_goals,
		tfs.team_avg_assists,
		tfs.team_avg_yellowcards,
		tfs.team_avg_redcards,
		tfs.team_avg_direct_redcards,
		tfs.team_avg_shots,
		tfs.team_avg_headshots,
		tfs.team_avg_shots_against,
		tfs.team_avg_penalty_goals,
		tfs.team_avg_penalty_misses,
		tfs.team_avg_penalties,
		tfs.team_avg_linkup_plays,
		tfs.team_avg_fouls,
		tfs.team_avg_passes,
		tfs.team_avg_crosses,
		tfs.team_avg_dribbles,
		tfs.team_avg_interceptions,
		tfs.team_avg_shot_assists,
		tfs.team_avg_recoveries,
		tfs.team_avg_losses,
		tfs.team_avg_penalty_fouls,
		tfs.team_avg_penalty_conceded_goals,
		tfs.team_avg_aerial_duels,
		tfs.team_avg_ground_duels,
		tfs.team_avg_duels,
		tfs.team_avg_passes_to_final_third,
		tfs.team_avg_passes_to_penalty_area,
		tfs.team_avg_shots_on_target,
		tfs.team_avg_shots_outside_penalty,
		tfs.team_avg_shots_inside_penalty,
		tfs.team_avg_mean_xg,
		tfs.team_avg_sum_xg,
		tfs.team_avg_gk_saves,
		tfs.team_avg_gk_save_xg,
		-- Historical per match averages of player's opponent team
		ofs.opp_avg_goals,
		ofs.opp_avg_assists,
		ofs.opp_avg_yellowcards,
		ofs.opp_avg_redcards,
		ofs.opp_avg_direct_redcards,
		ofs.opp_avg_shots,
		ofs.opp_avg_headshots,
		ofs.opp_avg_shots_against,
		ofs.opp_avg_penalty_goals,
		ofs.opp_avg_penalty_misses,
		ofs.opp_avg_penalties,
		ofs.opp_avg_linkup_plays,
		ofs.opp_avg_fouls,
		ofs.opp_avg_passes,
		ofs.opp_avg_crosses,
		ofs.opp_avg_dribbles,
		ofs.opp_avg_interceptions,
		ofs.opp_avg_shot_assists,
		ofs.opp_avg_recoveries,
		ofs.opp_avg_losses,
		ofs.opp_avg_penalty_fouls,
		ofs.opp_avg_penalty_conceded_goals,
		ofs.opp_avg_aerial_duels,
		ofs.opp_avg_ground_duels,
		ofs.opp_avg_duels,
		ofs.opp_avg_passes_to_final_third,
		ofs.opp_avg_passes_to_penalty_area,
		ofs.opp_avg_shots_on_target,
		ofs.opp_avg_shots_outside_penalty,
		ofs.opp_avg_shots_inside_penalty,
		ofs.opp_avg_mean_xg,
		ofs.opp_avg_sum_xg,
		ofs.opp_avg_gk_saves,
		ofs.opp_avg_gk_save_xg
	from player_variable_select pvs
		left join per90_player_features ppf on pvs."matchId" = ppf."matchId"
		and pvs."playerId" = ppf."playerId"
		left join team_feature_select tfs on pvs."matchId" = tfs."matchId"
		and pvs."teamId" = tfs."teamId"
		left join opponent_feature_select ofs on pvs."matchId" = ofs."matchId"
		and pvs."opponentId" = ofs."opponentId"
)
create MATERIALIZED VIEW if NOT EXISTS derived_tables.ff_wrangling_mv
AS SELECT * FROM final_select;
REFRESH MATERIALIZED VIEW derived_tables.ff_wrangling_mv;
