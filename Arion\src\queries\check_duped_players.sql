select * 
from wyscout.players p, 
(
select count(*), p."firstName", p."lastName", p."birthDate", p."birthArea_name"
from wyscout.players p
where "birthDate" is not null and "firstName" is not null and "lastName" is not null 
group by p."firstName", p."lastName", p."birthDate", p."birthArea_name"
having count(*) > 1) d
where p."lastName" = d."lastName" and p."firstName" = d."firstName" and p."birthDate" = d."birthDate" and p."birthArea_name" = d."birthArea_name"
order by p."firstName", p."lastName", p."birthDate", p."birthArea_name"