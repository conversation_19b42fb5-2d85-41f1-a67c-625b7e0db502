import json
from datetime import datetime

import pandas as pd


class EventsPrepper:
    def prep(self, events):
        # yes we are dumping the json that we have just loaded
        # and thats retarded but otherwise we have to rewrite gazillion things so eh
        return {
            "events": pd.DataFrame(
                [
                    {
                        "matchId": events[0]["matchId"],
                        "events": json.dumps(events),
                    }
                ]
            )
        }
