import json
from settings import postgres_prod_str
from sqlalchemy import create_engine


eng = create_engine(postgres_prod_str)

def main():
    with open("""src\\data_collection\\new_scraping\\adhoc\\bookies.json""") as f:
        bookies = json.load(f)

    for key, value in bookies.items():
        eng.execute(f"INSERT INTO oddsportal.bookies(bookie_id, bookie_name) VALUES({key}, '{value['WebName']}')")

if __name__ == "__main__":
    main()