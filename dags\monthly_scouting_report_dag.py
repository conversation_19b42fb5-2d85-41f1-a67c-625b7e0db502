from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir

dag_params = {
    "dag_id": "monthly_scouting_report",
    "start_date": datetime(2021, 8, 31),
    "schedule_interval": "0 5 * * 2",  # 5 AM utc every Tuesday
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    send_monthly_scouting_report = BashOperator(
        task_id="send_monthly_scouting_report",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/reporting/monthly_scouting_v1/send_monthly_scouting_report.py """,
    )

    send_monthly_scouting_report
