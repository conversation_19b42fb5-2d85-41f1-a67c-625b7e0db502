from pv.model_training import PlayerValueModeler
import src.models.player_valuation.utils.database_connect as dbconnect
from src.models.player_valuation.utils.config import *
from  itertools import product
import pandas as pd

def main():

    cnx_prod = dbconnect.main()
    modelling_df = pd.read_sql_table('tmp_valuation_df', cnx_prod, schema='player_valuation')
    modelling_df.season = modelling_df.date.dt.year
    split_type = None

    model_type = 'regression'
    tm_value_used = [True, False]
    log_transformed = [True, False]
    root_transformed = [True, False]

    df = pd.DataFrame({'model_type': model_type,
        'tm_value_used':tm_value_used,
        'log_transformed':log_transformed,
        'root_transformed':root_transformed})

    columns = list(df.columns)

    df = pd.DataFrame(list(product(*df.values.T)))
    df.columns = columns
    df = df[~((df.root_transformed == True) & (df.log_transformed == True))].\
        drop_duplicates().reset_index(drop = True)

    # Regression
    excl_vars = ['birthDate', 'day', 'month', 'year', 'mv_group', 'tw2']
    # incl_vars = ['total_fee_alltime_buying_transfer', 'mv', 
    #     'count_alltime_buying_transfer', 'age_at_time', 'days_since_last_match', 
    #     'mean_player_value', 'tenure_in_team', 'team_rating', 
    #     'passportArea_alpha3code', 'foot', 'International_cup_club_0_alltime_team',
    #     'avg_competition_rating', 'Domestic_league_club_1_prevseason_team', 
    #     'Domestic_league_club_1_prevseason_player', 'player_rating', 'birthArea_alpha3code',
    #     'Domestic_league_club_1_alltime_player', 'code', 'player_to_role_popularity_ratio', 
    #     'unique_roles', 'transfer_window', 'season']

    # all_vars = ['playerId', 'date', 'fee'] + excl_vars + incl_vars
    # all_vars = list(set(all_vars))
    all_vars = list(modelling_df.columns)
    categorical_vars = list(set(all_vars).difference(excl_vars).\
        intersection(set(['code', 'foot', 'passportArea_alpha3code', 'birthArea_alpha3code', 'transfer_window'])))
    for i, row in df.iterrows():
        model_type = row['model_type']
        log_transformed = row['log_transformed']
        root_transformed = row['root_transformed']
        tm_value_used = row['tm_value_used']

        if 'mv' not in all_vars:
                all_vars.append('mv')

        if tm_value_used:
            if 'mv' in excl_vars:
                excl_vars = [x for x in excl_vars if x != 'mv']
        else:
            if 'mv' not in excl_vars:
                excl_vars.append('mv')

        PVM_regr = PlayerValueModeler(modelling_df[all_vars], 'fee', ['playerId', 'date'], excl_vars, 
            model_type= model_type, 
            log_transform_y=log_transformed, root_transform_y=root_transformed,
            cat_vars = categorical_vars,
            cnx =cnx_prod, std_threshold=0.1)
        PVM_regr.prep_modelling_df()
        PVM_regr.set_target_variable(num_break=10)
        PVM_regr.optimize_hyperparams(direction = 'minimize', n_trials = 10, split_type = split_type)
        PVM_regr.get_cv_metrics(split_type = split_type, write_to_db=True)
        PVM_regr.train_final_model()
        # rename(PVM_regr.local_model_path, PVM_regr.destination_file_name)
        PVM_regr.export_model_to_gcloud()

if __name__ == "__main__":
    main()