source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)
# creates a connection to the postgres database
# note that "con" will be used later in each connection to the database

con <- make_connection()

wyscout_username = Sys.getenv("wyscout_username")
wyscout_pass = Sys.getenv("wyscout_pass")

tracking_table_name = 'adv_stats_to_exclude'
unparsed_table_name = 'unparsed_adv_stats'


# Get player matches
pm_query = 'select distinct tml."matchId", tml."playerId", sm."dateutc" 
from team_matches_lineups tml, seasons_matches sm
where sm."matchId" = tml."matchId" and tml."matchId" is not null and tml."playerId" is not null
and sm."dateutc" > \'2015-12-31\''
pm_query =  gsub('\n', ' ', pm_query)

# player_matches2 = player_matches

pm_query = paste(pm_query, 'order by sm."dateutc" desc')
player_matches = dbGetQuery(con, pm_query)

# # Get relevant season matches
seasons_matches = dbReadTable(con, 'seasons_matches')

seasons_matches = seasons_matches %>%
  mutate(dateutc = as.Date(dateutc)) %>%
  distinct()
# 
# seasons_matches = seasons_matches %>%
#   filter(seasonId %in% final_seasons$seasonId)
# 
# # Get player_matches of interest
# 

# player_matches = player_matches %>%
#   inner_join(seasons_matches[c('competitionId', 'matchId', 'dateutc')]) 
# 
# 
# # Get player matches that are NOT in advanced stats, for the competitions we have previously collected data for
# player_matches = player_matches[!duplicated(player_matches[c('matchId', 'playerId')]), ]

advanced_stats_matches = dbGetQuery(
  con,
  'select distinct "playerId", "matchId", "competitionId" from advanced_stats_total'
)
player_matches = player_matches %>%
  anti_join(advanced_stats_matches[c('playerId', 'matchId', 'competitionId')])

# idx = which(
#   player_matches$dateutc > as.Date('2015-12-31')
# )
# player_matches = player_matches[idx, ]

tbls = dbListTables(con)

player_total_columns = NULL
player_avg_columns = NULL
player_perc_columns = NULL
player_pos_columns = NULL

owt_total = T
owt_avg = T
owt_perc = T
owt_pos = T

ttl_cols_loaded = F
avg_cols_loaded = F
perc_cols_loaded = F
pos_cols_loaded = F

if ('advanced_stats_total' %in% tbls) {
  player_total_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_total limit 1'))
  owt_total = F
  ttl_cols_loaded = T
}

if ('advanced_stats_average' %in% tbls) {
  player_avg_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_average limit 1'))
  owt_avg = F
  avg_cols_loaded = T
}

if ('advanced_stats_percentage' %in% tbls) {
  player_perc_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_percentage limit 1'))
  owt_perc = F
  perc_cols_loaded = T
}

if ('player_match_positions' %in% tbls) {
  player_pos_columns = colnames(dbGetQuery(con, 'select * from player_match_positions limit 1'))
  owt_pos = F
  pos_cols_loaded = T
}


if (tracking_table_name %in% tbls){
  untracked_matches_df = dbReadTable(con, tracking_table_name)
  player_matches = player_matches %>%
    anti_join(untracked_matches_df)
}

if(unparsed_table_name %in% tbls) {
  unparsed_matches_df = dbReadTable(con, unparsed_table_name)
}

dbDisconnect(con)
player_totals = list()
player_averages = list()
player_percentages = list()
player_match_positions = list()
untracked_matches = list()
unparsed_matches = list()

for (i in 1:nrow(player_matches)) {
  mid = player_matches$matchId[i]
  player_id = player_matches$playerId[i]
  
  try({
    Sys.sleep(0.08)
    
    received_resp = F
    request_cnt = 0
    while(!received_resp & request_cnt < 1){
      resp = GET(
        paste0(
          'https://apirest.wyscout.com/v2/players/',
          player_id,
          '/matches/',
          mid,
          '/advancedstats'
        ),
        authenticate(wyscout_username, wyscout_pass)
      )
      
      cont = content(resp, as = 'text', encoding = 'UTF-8')
      cont = tryCatch({fromJSON(cont)}, 
               error = function(e){data.frame(playerId  = player_id, matchId = mid, parsed_json = F)
                 print(paste('Invalid JSON for player', player_id, 'and match', mid))},
               finally = NULL)
      request_cnt = request_cnt+1
      
      if(resp$status_code==200){
        received_resp = T
      }else if('error' %in% names(cont)){
        if(cont$error$code==404){
          untracked_matches[[length(untracked_matches) + 1]] = data.frame(matchId = mid, playerId = player_id)
        }
      }else if('parsed_json' %in% names(cont)){
        received_resp = F
        unparsed_matches[[length(unparsed_matches) + 1]] = cont
      }
      else{
        untracked_matches[[length(untracked_matches) + 1]] = data.frame(matchId = mid, playerId = player_id)
      }
    }
    
    # cont = content(resp, as = 'text', encoding = 'UTF-8')
    # cont = fromJSON(cont)
    if(received_resp){
      
      totals_temp = cont$total
      comp_id = cont$competitionId
      sid = cont$seasonId
      rid = cont$roundId
      totals_temp[sapply(totals_temp, is.null)] = NA
      
      avgs_temp = cont$average
      avgs_temp[sapply(avgs_temp, is.null)] = NA
      
      perc_temp = cont$percent
      perc_temp[sapply(perc_temp, is.null)] = NA
      
      if (length(cont$positions) > 0) {
        pos_temp = cbind(cont$positions$position, cont$positions$percent)
        colnames(pos_temp) = c('name', 'code', 'percent')
        pos_temp$matchId = mid
        pos_temp$playerId = player_id
        player_match_positions[[length(player_match_positions) + 1]] = pos_temp
      }
      
      if (totals_temp$minutesTagged > 0) {
        totals_temp = data.frame(totals_temp, stringsAsFactors = F)
        totals_temp$matchId = mid
        totals_temp$playerId = player_id
        totals_temp$competitionId = comp_id
        player_totals[[length(player_totals) + 1]] = totals_temp
        
        
        
        avgs_temp = data.frame(avgs_temp, stringsAsFactors = F)
        avgs_temp$matchId = mid
        avgs_temp$playerId = player_id
        avgs_temp$competitionId = comp_id
        player_averages[[length(player_averages) + 1]] = avgs_temp
        
        perc_temp = data.frame(perc_temp, stringsAsFactors = F)
        perc_temp$matchId = mid
        perc_temp$playerId = player_id
        perc_temp$competitionId = comp_id
        player_percentages[[length(player_percentages) + 1]] = perc_temp
        
      }else{
        untracked_matches[[length(untracked_matches) + 1]] = data.frame(matchId = mid, playerId = player_id)
      }
    }
  })
  
  if (i %% 1000 == 0 | i == nrow(player_matches)) {
    if (length(player_totals) > 1) {
      
      player_totals = do.call(plyr::rbind.fill, player_totals)
      player_averages = do.call(plyr::rbind.fill, player_averages)
      player_percentages = do.call(plyr::rbind.fill, player_percentages)
      player_match_positions = do.call(plyr::rbind.fill, player_match_positions)
      drv <- dbDriver("PostgreSQL")
      
      con <- make_connection()
      
      player_totals = player_totals[!grepl('scaled', colnames(player_totals))]
      columns_to_omit = c(
        'matches',
        'matchesInStart',
        'matchesSubstituted',
        'matchesComingOff',
        'minutesOnField',
        'minutesTagged',
        'label',
        'date',
        'dateutc',
        'status',
        'competitionId',
        'seasonId',
        'roundId',
        'gameweek',
        'matchId',
        'playerId',
        'name',
        'Priority',
        'NewPr',
        'area_id',
        'area_name',
        'area_alpha2code',
        'area_alpha3code',
        'category',
        'gender',
        'divisionLevel',
        'second_prio',
        'format',
        'type'
      )
      
      # Remove for loop, use apply
      scaled_totals = player_totals[setdiff(colnames(player_totals), columns_to_omit)] * NA
      colnames(scaled_totals) = paste(colnames(scaled_totals), 'scaled', sep = '_')
      player_totals = cbind(player_totals, scaled_totals)
      
      
      
      tbls = dbListTables(con)
      
      if(length(untracked_matches) > 0){
        untracked_matches = do.call(plyr::rbind.fill, untracked_matches) %>% distinct()
        owt_untracked = F
        if (tracking_table_name %in% tbls){
          dbWriteTable(con,
                       tracking_table_name,
                       untracked_matches,
                       overwrite = owt_untracked,
                       append = !owt_untracked,
                       row.names = F,
                       rownames = F)
        }else{
          owt_untracked = T
          dbWriteTable(con,
                       tracking_table_name,
                       untracked_matches,
                       overwrite = owt_untracked,
                       append = !owt_untracked,
                       row.names = F,
                       rownames = F)
        }
        untracked_matches = list()
      }
      
      if(length(unparsed_matches) > 0){
        unparsed_matches = do.call(plyr::rbind.fill, unparsed_matches) %>% distinct()
        
        owt_unparsed = F
        if (unparsed_table_name %in% tbls){
          unparsed_matches = unparsed_matches %>%
            anti_join(unparsed_matches_df[c('playerId', 'matchId')])
          dbWriteTable(con,
                       unparsed_table_name,
                       unparsed_matches,
                       overwrite = owt_unparsed,
                       append = !owt_unparsed,
                       row.names = F,
                       rownames = F)
        }else{
          owt_unparsed = T
          dbWriteTable(con,
                       unparsed_table_name,
                       unparsed_matches,
                       overwrite = owt_unparsed,
                       append = !owt_unparsed,
                       row.names = F,
                       rownames = F)
        }
        unparsed_matches = list()
      }
      
      if ('advanced_stats_total' %in% tbls) {
        player_total_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_total limit 1'))
        owt_total = F
        ttl_cols_loaded = T
      } else{
        player_total_columns = colnames(player_totals)
        owt_total = T
        ttl_cols_loaded = T
      }
      
      if ('advanced_stats_average' %in% tbls) {
        player_avg_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_average limit 1'))
        owt_avg = F
        avg_cols_loaded = T
      } else{
        player_avg_columns = colnames(player_averages)
        owt_avg = T
        avg_cols_loaded = T
      }
      
      if ('advanced_stats_percentage' %in% tbls) {
        player_perc_columns = colnames(dbGetQuery(con, 'select * from advanced_stats_percentage limit 1'))
        owt_perc = F
        perc_cols_loaded = T
      } else{
        player_perc_columns = colnames(player_percentages)
        owt_perc = T
        perc_cols_loaded = T
      }
      
      if ('player_match_positions' %in% tbls) {
        player_pos_columns = colnames(dbGetQuery(con, 'select * from player_match_positions limit 1'))
        owt_pos = F
        pos_cols_loaded = T
      } else{
        player_pos_columns = colnames(player_match_positions)
        owt_pos = T
        pos_cols_loaded = T
      }
      
      numeric_columns_total = colnames(player_totals)[sapply(player_totals, is.numeric)]
      numeric_columns_avg = colnames(player_averages)[sapply(player_averages, is.numeric)]
      numeric_columns_perc = colnames(player_percentages)[sapply(player_percentages, is.numeric)]
      
      numeric_columns_total = numeric_columns_total[!grepl('Id', numeric_columns_total, ignore.case = T)]
      numeric_columns_avg = numeric_columns_avg[!grepl('Id', numeric_columns_avg, ignore.case = T)]
      numeric_columns_perc = numeric_columns_perc[!grepl('Id', numeric_columns_perc, ignore.case = T)]
      
      player_totals[numeric_columns_total] = sapply(player_totals[numeric_columns_total], as.numeric)
      player_averages[numeric_columns_avg] = sapply(player_averages[numeric_columns_avg], as.numeric)
      player_percentages[numeric_columns_perc] = sapply(player_percentages[numeric_columns_perc], as.numeric)
      
      if (nrow(player_totals) > 1) {
        
        player_totals = player_totals %>%
          left_join(seasons_matches)
        player_averages = player_averages %>%
          left_join(seasons_matches)
        RPostgreSQL::dbWriteTable(
          con,
          'advanced_stats_total',
          player_totals[player_total_columns],
          overwrite = owt_total,
          append = !owt_total,
          row.names = F,
          rownames = F
        )
        RPostgreSQL::dbWriteTable(
          con,
          'advanced_stats_average',
          player_averages[player_avg_columns],
          overwrite = owt_avg,
          append = !owt_avg,
          row.names = F,
          rownames = F
        )
        RPostgreSQL::dbWriteTable(
          con,
          'advanced_stats_percentage',
          player_percentages[player_perc_columns],
          overwrite = owt_perc,
          append = !owt_perc,
          row.names = F,
          rownames = F
        )
      }
      
      if (!is.null(player_match_positions)) {
        RPostgreSQL::dbWriteTable(
          con,
          'player_match_positions',
          player_match_positions[player_pos_columns],
          overwrite = owt_pos,
          append = !owt_pos,
          row.names = F,
          rownames = F
        )
      }
      dbDisconnect(con)
      
      
    }
    player_totals = list()
    player_averages = list()
    player_percentages = list()
    player_match_positions = list()
    
  }
  
}

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'advanced_stats_total',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'advanced_stats_average',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'advanced_stats_percentage',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'player_match_positions',tbl_qry))
  dbGetQuery(con, gsub('tb_name', tracking_table_name,tbl_qry))
}

dbDisconnect(con)