from time import sleep
import requests
import traceback
import re
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import PROXIES, postgres_prod_str
from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)


class ProfileIO:
    def __init__(
        self,
        cnx_prod,
        unmatched_players_table,
        tm_to_ws_ids_table,
        raw_tm_to_ws_ids_table,
        unmachable_players_table,
        match_player_log_table,
    ):
        self.cnx_prod = cnx_prod
        self.unmatched_players_table = unmatched_players_table
        self.tm_to_ws_ids_table = tm_to_ws_ids_table
        self.raw_tm_to_ws_ids_table = raw_tm_to_ws_ids_table
        self.profiles_to_match = None
        self.unmachable_players_table = unmachable_players_table
        self.match_player_log_table = match_player_log_table

    def get_profiles_to_match(self):
        # we are getting only missing players are unique by their two names and birth date:
        query = """SELECT tm."playerId", tm."firstName", tm."lastName", tm."birthDate"
                    FROM (SELECT MAX(wp."playerId") AS "playerId", wp."firstName", wp."lastName", wp."birthDate" FROM 
                        meta_scraping.unmatched_tm_players um,
                        wyscout.players wp
                        WHERE um."playerId" = wp."playerId"
                        GROUP BY wp."firstName", wp."lastName", wp."birthDate"
						  HAVING COUNT(wp."playerId") < 2
						  ) tm
						LEFT JOIN
                        meta_scraping.ws_tm_unmatchable_players umb
						ON tm."playerId" = umb."playerId"
						WHERE umb."playerId" IS NULL
                        """
        self.profiles_to_match = pd.read_sql(query, self.cnx_prod)

    def write_matched_pair(self, matched_pair):
        matched_pair.to_sql(
            self.raw_tm_to_ws_ids_table,
            self.cnx_prod,
            schema="meta_scraping",
            if_exists="append",
            index=False,
        )

    def write_unmatchable_player(self, playerId):
        pd.DataFrame([playerId], columns=["playerId"]).to_sql(
            self.unmachable_players_table,
            self.cnx_prod,
            schema="meta_scraping",
            if_exists="append",
            index=False,
        )

    def save_log(self, df):
        df.to_sql(
            self.match_player_log_table,
            self.cnx_prod,
            schema="meta_scraping",
            if_exists="append",
            index=False,
        )

    def remove_matched_player_from_unmatched_table(self, playerId):
        query = f"""DELETE FROM meta_scraping.{self.unmatched_players_table}
                    WHERE  "playerId" = {playerId}"""
        self.cnx_prod.execute(query)


class Searcher:
    def __init__(self, search_url, proxies):
        self.session = requests.Session()
        self.session.verify=False
        self.session.headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 5.1)"
                " AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/49.0.2623.112 Safari/537.36"
            )
        }
        self.search_url = search_url
        self.proxies = {"https": proxies}
        self.query_params = {
            "Detailsuche[vorname]": "",
            "Detailsuche[name]": "",
            "Detailsuche[name_anzeige]": "",
            "Detailsuche[passname]": "",
            "Detailsuche[genaue_suche]": "0",
            "speichern": "Submit search",
            "Detailsuche[geb_ort]": "",
            "Detailsuche[genaue_suche_geburtsort]": "0",
            "Detailsuche[land_id]": "",
            "Detailsuche[zweites_land_id]": "",
            "Detailsuche[geb_land_id]": "",
            "Detailsuche[kontinent_id]": "",
            "Detailsuche[geburtstag]": "doesn\\'t matter",
            "Detailsuche[geburtsmonat]": "doesn\\'t matter",
            "Detailsuche[geburtsjahr]": "",
            "alter": "0 - 150",
            "Detailsuche[age]": "0;150",
            "Detailsuche[minAlter]": "0",
            "Detailsuche[maxAlter]": "150",
            "jahrgang": "1850 - 2006",
            "Detailsuche[jahrgang]": "1850;2006",
            "Detailsuche[minJahrgang]": "1850",
            "Detailsuche[maxJahrgang]": "2006",
            "groesse": "0 - 220",
            "Detailsuche[groesse]": "0;220",
            "Detailsuche[minGroesse]": "0",
            "Detailsuche[maxGroesse]": "220",
            "Detailsuche[hauptposition_id]": "",
            "Detailsuche[nebenposition_id_1]": "",
            "Detailsuche[nebenposition_id_2]": "",
            "Detailsuche[minMarktwert]": "0",
            "Detailsuche[maxMarktwert]": "200.000.000",
            "Detailsuche[fuss_id]": "",
            "Detailsuche[fuss_id]": "",
            "Detailsuche[captain]": "",
            "Detailsuche[captain]": "",
            "Detailsuche[rn]": "",
            "Detailsuche[wettbewerb_id]": "",
            "Detailsuche[w_land_id]": "",
            "nm_spiele": "0 - 200",
            "Detailsuche[nm_spiele]": "0;200",
            "Detailsuche[minNmSpiele]": "0",
            "Detailsuche[maxNmSpiele]": "200",
            "Detailsuche[trans_id]": "0",
            "Detailsuche[aktiv]": "0",
            "Detailsuche[vereinslos]": "0",
            "Detailsuche[leihen]": "0",
        }

    @staticmethod
    def xstr(s):
        return "" if s is None else str(s)

    @retry(wait=wait_random(min=10, max=30), stop=stop_after_attempt(2))
    def get_search_url(self, search_param_dict, full_name=False):
        sleep(5)
        # try to get birth date, for those with null birthdate, we will shoot without one
        try:
            year, month, day = map(
                lambda x: int(x), search_param_dict["birthDate"].split("-")
            )
        except:
            year, month, day = (
                "",
                "doesn\\'t matter",
                "doesn\\'t matter",
            )
        try:
            firstName = search_param_dict["firstName"]
        except:
            firstName = ""
        try:
            lastName = search_param_dict["lastName"]
        except:
            lastName = ""
        payload = self.query_params.copy()
        payload["Detailsuche[geburtsjahr]"] = year
        payload["Detailsuche[geburtsmonat]"] = month
        payload["Detailsuche[geburtstag]"] = day

        # default search with first and last name
        if not full_name:
            payload["Detailsuche[vorname]"] = firstName
            payload["Detailsuche[name]"] = lastName
        else:
            # this is made for latino people with many names, combine first with last
            # and pass it as a full name in the pay load, custom string func, takes care
            # of None names so that it doesnt break:
            fullName = self.xstr(firstName) + " " + self.xstr(lastName)
            payload["Detailsuche[passname]"] = fullName

        resp = self.session.post(
            self.search_url, proxies=self.proxies, data=payload
        )
        return re.search(r"https://www.transfermarkt.com/detailsuche/spielerdetail/suche/\d+", resp.text,).group(0)

    @retry(wait=wait_random(min=10, max=30), stop=stop_after_attempt(2))
    def get_search_soup(self, url):
        soup = get_soup_from_url(url, self.session, proxies=PROXIES)
        return soup


class Looper:
    def __init__(self, profiles, matcher, searcher, io, deduper):
        self.profiles = profiles
        self.matcher = matcher
        self.searcher = searcher
        self.io = io
        self.deduper = deduper
        self.log_dict = {
            "first_last": [],
            "url": [],
            "status": [],
            "playerId": [],
        }

    def search_and_match(
        self, search_param_dict, row, match_type, full_name=False
    ):
        search_url = self.searcher.get_search_url(search_param_dict, full_name)
        search_soup = self.searcher.get_search_soup(search_url)

        if search_soup is not None:
            matched_pair = self.matcher.determine_match(
                search_soup, row["playerId"]
            )
            # if we have a match write it to matching table and delete it from missing table:
            if matched_pair is not None:
                self.io.write_matched_pair(matched_pair)
                self.io.remove_matched_player_from_unmatched_table(
                    row["playerId"]
                )
                self.log_dict["first_last"].append(
                    f"{row['firstName']} {row['lastName']}"
                )
                self.log_dict["url"].append(search_url)
                self.log_dict["status"].append(match_type)
                self.log_dict["playerId"].append(row["playerId"])
                return True

    def loop_profiles(self):
        for i, row in self.profiles.iterrows():
            search_param_dict = {
                "firstName": row["firstName"],
                "lastName": row["lastName"],
                "birthDate": row["birthDate"],
            }
            match_result = self.search_and_match(
                search_param_dict, row, "first_last", full_name=False
            )

            if not match_result:
                # if we dont hit both names + birthdate, we try the latino fix (aka full name):
                match_result_full = self.search_and_match(
                    search_param_dict, row, "full_name", full_name=True
                )
                # NOTE for now we stop matching by first and last name only, since match rates are pathetic
                # if we dont hit full name + birthdate, we try last name + birthdate:
                if not match_result_full:
                    # search_param_dict = {
                    #     'firstName': '',
                    #     'lastName': row['lastName'],
                    #     'birthDate': row['birthDate']
                    # }
                    # match_result_last = self.search_and_match(search_param_dict, row, 'last_name', full_name=False)

                    # # if we dont hit last name + birth, we try first name + birth date:
                    # if not match_result_last:
                    #     search_param_dict = {
                    #         'firstName': row['firstName'],
                    #         'lastName': '',
                    #         'birthDate': row['birthDate']
                    #     }
                    #     match_result_first = self.search_and_match(search_param_dict, row, 'first_name', full_name=False)
                    #     # if we dont hit anything, add to unmatchable ppl
                    #     if not match_result_first:
                    self.io.write_unmatchable_player(row["playerId"])

                    self.log_dict["first_last"].append(
                        f"{row['firstName']} {row['lastName']}"
                    )
                    self.log_dict["url"].append(np.nan)
                    self.log_dict["status"].append("unmatchable")
                    self.log_dict["playerId"].append(row["playerId"])

            # Every 1k ppl, clean up and move :
            if i != 0 and i % 1000 == 0:
                self.deduper.dedupe_tm_to_ws_table(self.log_dict)
                self.deduper.migrate_tm_to_ws_table()
                self.log_dict = {
                    key: [] for key, value in self.log_dict.items()
                }
        # clean up and move one final time at the end of matching
        self.deduper.dedupe_tm_to_ws_table(self.log_dict)
        self.deduper.migrate_tm_to_ws_table()
        self.log_dict = {key: [] for key, value in self.log_dict.items()}


class Matcher:
    def __init__(self, cnx_prod, tm_to_ws_ids_table):
        self.cnx_prod = cnx_prod
        self.tm_to_ws_ids_table = tm_to_ws_ids_table
        self.matched_tm_ids = None

    def load_matched_profiles(self):
        query = (
            f"SELECT tm_player_id FROM meta_scraping.{self.tm_to_ws_ids_table}"
        )
        df = pd.read_sql(query, self.cnx_prod)
        self.matched_tm_ids = df["tm_player_id"].tolist()

    def append_matched_guys(self, playerId):
        self.matched_tm_ids.append(playerId)

    def determine_match(self, soup, ws_playerId):
        table = soup.find("table", attrs={"class": "items"})
        try:
            players = table.find_all("tr", attrs={"class": ["odd", "even"]})
        except Exception as e:
            return
        if len(players) != 1:
            return
        url = players[0].find("td", attrs={"hauptlink"}).find("a")["href"]
        tm_id = url.split("/")[-1]
        tm_player_url = "https://www.transfermarkt.com" + url
        return pd.DataFrame(
            {
                "playerId": [ws_playerId],
                "tm_player_id": [tm_id],
                "tm_player_url": [tm_player_url],
            }
        )


class Deduper:
    def __init__(
        self, cnx_prod, tm_to_ws_ids_table, raw_tm_to_ws_ids_table, io
    ):
        self.cnx_prod = cnx_prod
        self.tm_to_ws_ids_table = tm_to_ws_ids_table
        self.raw_tm_to_ws_ids_table = raw_tm_to_ws_ids_table
        self.io = io

    def dedupe_tm_to_ws_table(self, log_dict):
        with open("src/queries/dedupe_tm_to_ws_ids.sql", "r") as file:
            query = file.read()
            self.cnx_prod.execute(query)
            with self.cnx_prod.connect() as connection:
                transaction = connection.begin()
                try:
                    self.io.save_log(pd.DataFrame(log_dict))

                    connection.execute(
                        """DELETE FROM meta_scraping.raw_tm_to_ws_ids msr
                    USING transfermarkt.tm_to_ws_ids mst WHERE msr.tm_player_id = mst.tm_player_id 
                    and mst."playerId" = msr."playerId" """
                    )

                    connection.execute(
                        """INSERT INTO meta_scraping.check_matching
                    SELECT msr."playerId", msr.tm_player_id, msr.tm_player_url FROM meta_scraping.raw_tm_to_ws_ids msr
                    INNER JOIN transfermarkt.tm_to_ws_ids tt ON msr.tm_player_id = tt.tm_player_id"""
                    )

                    connection.execute(
                        """INSERT INTO meta_scraping.check_matching SELECT
                        msc."playerId", mscm.tm_player_id, msc.tm_player_url FROM
                        transfermarkt.tm_to_ws_ids msc
                        INNER JOIN meta_scraping.check_matching mscm ON
                        mscm.tm_player_id = msc.tm_player_id"""
                    )

                    connection.execute(
                        """DELETE FROM meta_scraping.raw_tm_to_ws_ids msr USING
                    meta_scraping.check_matching msc WHERE msr.tm_player_id = msc.tm_player_id"""
                    )

                    connection.execute(
                        """DELETE FROM transfermarkt.tm_to_ws_ids msr USING
                    meta_scraping.check_matching msc WHERE msr.tm_player_id = msc.tm_player_id"""
                    )

                    transaction.commit()
                except Exception as e:
                    print(traceback.format_exc())
                    transaction.rollback()

    def migrate_tm_to_ws_table(self):
        query = (
            f"INSERT INTO transfermarkt.{self.tm_to_ws_ids_table} SELECT * FROM"
            f" meta_scraping.{self.raw_tm_to_ws_ids_table}"
        )
        self.cnx_prod.execute(query)


def main():
    cnx_prod = create_engine(postgres_prod_str)

    io = ProfileIO(
        cnx_prod,
        "unmatched_tm_players",
        "tm_to_ws_ids",
        "raw_tm_to_ws_ids",
        "ws_tm_unmatchable_players",
        "match_player_log",
    )
    io.get_profiles_to_match()
    searcher = Searcher(
        "https://www.transfermarkt.com/detailsuche/spielerdetail/suche/",
        PROXIES,
    )
    matcher = Matcher(cnx_prod, "tm_to_ws_ids")
    deduper = Deduper(cnx_prod, "tm_to_ws_ids", "raw_tm_to_ws_ids", io)
    looper = Looper(io.profiles_to_match, matcher, searcher, io, deduper)
    try:
        looper.loop_profiles()
    except Exception as e:
        # if it fails at some point, clean up and move so we start fresh:
        deduper.dedupe_tm_to_ws_table(pd.DataFrame)
        deduper.migrate_tm_to_ws_table()
        raise Exception(e)


if __name__ == "__main__":
    main()
