import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "refresh_matched_players_v2_dag",
    "start_date": datetime(2021, 12, 6),
    "schedule_interval": timedelta(days=7),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            # "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    populate_progresss_table = PostgresOperator(
        task_id="populate_progress_table",
        sql="""INSERT INTO meta_scraping.player_progress_table SELECT * FROM transfermarkt.tm_to_ws_ids""",
        database="wyscout_raw_production",
    )

    truncate_raw_tables = BashOperator(
        task_id="truncate_raw_tables",
                bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/truncate_raw_profile_tables.py
                           """,
    )


    rescrape_players = BashOperator(
        task_id="rescrape_players",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/rescrape_player_profiles.py
                           """,
    )

    save_cleaned_profiles = BashOperator(
        task_id="save_cleaned_profiles",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_profiles.py
                           """,
    )

    save_cleaned_transfers = BashOperator(
        task_id="save_cleaned_transfers",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_transfers.py
                           """,
    )

    save_cleaned_contracts = BashOperator(
        task_id="save_cleaned_contracts",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_historical_contracts.py
                           """,
    )

    # map_transfer_types = PostgresOperator(
    #     task_id="map_transfer_types",
    #     database="wyscout_raw_production",
    #     sql=open(workdir + "/src/queries/map_transfer_types.sql").read(),
    # )

    check_last_week_data = PostgresOperator(
        task_id="check_last_week_data",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/check_last_week_players.sql").read(),
    )

    check_if_players_are_ready_for_migration = BashOperator(
        task_id="check_if_players_are_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_player_before_migration.py
                           """,
    )

    check_if_transfers_are_ready_for_migration = BashOperator(
        task_id="check_if_transfers_are_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_transfers_before_migration.py
                           """,
    )

    check_if_agents_are_ready_for_migration = BashOperator(
        task_id="check_if_agents_are_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_agents_before_migration.py
                           """,
    )

    migrate_agents = PostgresOperator(
        task_id="migrate_agents",
        database="wyscout_raw_production",
        sql="""INSERT INTO transfermarkt.tm_historical_agents SELECT * FROM meta_scraping.raw_agent_history_table""",
    )

    migrate_profiles = BashOperator(
        task_id="migrate_profiles",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/migrate_profiles.py
                           """,
    )

(
    populate_progresss_table
    >> truncate_raw_tables
    >> rescrape_players
    >> [
        save_cleaned_contracts,
        check_if_players_are_ready_for_migration,
        check_if_transfers_are_ready_for_migration,
        check_if_agents_are_ready_for_migration,
    ]
)
check_if_agents_are_ready_for_migration >> migrate_agents

(
    check_if_transfers_are_ready_for_migration
    >> save_cleaned_transfers
    # >> map_transfer_types
)
(
    check_if_players_are_ready_for_migration
    >> save_cleaned_profiles
    >> check_last_week_data
    >> migrate_profiles
)
