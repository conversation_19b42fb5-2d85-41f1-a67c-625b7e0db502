source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)

con = make_connection('DEVELOPMENT_DB')
# Using advanced stats total because for these games we should have formations
match_ids = dbGetQuery(con, 'select distinct "matchId" from events')
formations_collected = dbGetQuery(con, 'select distinct "matchId" from formation_events')
formation_columns = dbGetQuery(con, 'select * from formation_events limit 1')
formation_columns = colnames(formation_columns)
match_ids = match_ids %>%
  anti_join(formations_collected)
formations_all = list()

for (i in 1:nrow(match_ids)) {
  mid = match_ids$matchId[i]
  Sys.sleep(0.1)
  resp = GET(
    paste0(
      "https://apirest.wyscout.com/v2/matches/",
      mid, '/events?fetch=formations'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  try({
  cont = content(resp, type = 'text')
  cont = jsonlite::fromJSON(cont)
  temp_df = NULL
  
    temp_df = get_formations(cont)
    temp_df = temp_df %>%
      mutate(teamId = as.numeric(teamId))
    temp_df = data.frame(temp_df, stringsAsFactors = F)
    formations_all[[length(formations_all) + 1]] = temp_df
  })
}

form_df = do.call(rbind.fill, formations_all)
form_df = form_df[formation_columns]

dbDisconnect(con)
con <- make_connection()
dbWriteTable(con, 'formation_events', form_df, append = T, overwrite = F, row.names = F)
dbDisconnect(con)
