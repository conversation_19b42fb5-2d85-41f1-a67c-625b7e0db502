import os
import pickle
from datetime import datetime
from time import sleep
import requests
from lxml import etree

import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import PROXIES, postgres_prod_str, postgres_dev_str
from src.helper_funcs import read_config, get_time_stamp, fast_write_sql
from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)


class PlayerScraper:
    def __init__(self, config, cnx_prod, cnx_dev):
        # initiate empty df, columns are same as dict keys
        self.cnx_prod = cnx_prod
        self.cnx_dev = cnx_dev
        self.session = requests.Session()
        self.session.headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 5.1)"
                " AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/49.0.2623.112 Safari/537.36"
            )
        }

        self.df = pd.DataFrame(
            columns=[
                "player_url",
                "player_id",
                "name",
                "full_name",
                "name_in_home_country",
                "birth_date",
                "height",
                "citizenship",
                "position",
                "agent",
                "current_club",
                "current_club_id",
                "league_tier",
                "league_country",
                "joined",
                "contract_expiry",
                "current_value",
                "last_update",
                "highest_value",
                "highest_value_date",
                "strong_foot",
            ]
        )

        self.transfer_dfs_list = []
        self.transfers_df = None
        self.data_dict = {x: [] for x in self.df.columns}
        # export diff file at every run
        self.cleaned_tm = config["cleaned_transfermarkt"]
        self.export_file = f'{config["scraped_player_details"]}/player_details{get_time_stamp()}.csv'
        # use progress tracker instead of prepped file - better design
        self.progress_tracker = None
        self.progress_tracker_path = config["progress_trackers"][
            "player_profiles"
        ]
        self.priority_players = None

    @staticmethod
    def return_sibling(src, sibling_str, el="th"):
        return src.find(el, text=sibling_str).find_next_sibling("td").text

    def get_priority_ids(self, refresh=False):
        """Select scra"""
        if refresh:
            if self.cnx_prod is None:
                raise ValueError("Connection to db needed if we are refreshing")
            df = pd.read_sql(
                "SELECT tm_player_url AS player_url  from tm_to_ws_ids",
                self.cnx_prod,
            )
            self.priority_players = df["player_url"]
        else:
            source_df = pd.read_csv(self.cleaned_tm)
            source_df["continent"] = pd.Categorical(
                source_df["continent"], ["europa", "amerika", "afrika", "asien"]
            )
            # extracting league level to sort
            source_df["priority"] = (
                source_df["league_id"].str.extract(r"(\d)").astype(float)
            )
            # leagues without number go to lowest priority
            source_df["priority"] = source_df["priority"].fillna(
                source_df["priority"].max() + 1
            )
            # putting youth leagues as lowest priority
            source_df["league_name"] = source_df["league_name"].fillna("nan")
            source_df["youth"] = np.where(
                source_df["league_name"].str.contains("U\d+|Under", regex=True),
                1,
                0,
            )
            # start with all men competitions, first tiers, and then loop through continents,
            # finally sort by id to scrape together competitions

            source_df = source_df.sort_values(
                by=["youth", "priority", "continent", "league_id"]
            )
            # dropping dupes because we have 1.5k ppl with more than 1 entry,
            # most of them are retired but just in case; dropping after sorting to keep higher priority entries
            source_df = source_df.drop_duplicates(subset=["player_id"])
            self.priority_players = source_df.player_url

    def load_tracker(self):
        if self.progress_tracker is None:
            try:
                with open(self.progress_tracker_path, "rb") as track_in:
                    self.progress_tracker = pickle.load(track_in)
            except:
                self.progress_tracker = []

    def save_tracker(self):
        if self.progress_tracker is not None:
            try:
                with open(self.progress_tracker_path, "wb") as track_out:
                    pickle.dump(self.progress_tracker, track_out)
            except Exception as e:
                print(e)

    @staticmethod
    def get_base_info(dom, data_dict):
        def add_item(dom, path):
            try:
                return str(dom.xpath(path)[0])
            except:
                return np.nan

        key_path_dict = {
            "full_name": (
                "//span[text()='Full name:']/following::span[1]/text()"
            ),
            "name_in_home_country": (
                "//span[text()='Name in home"
                " country:']/following::span[1]/text()"
            ),
            "birth_date": (
                "//span[text()='Date of birth:']/following::span[1]//text()"
            ),
            "height": "//span[text()='Height:']/following::span[1]/text()",
            "citizenship": (
                "//span[text()='Citizenship:']/following::span[1]/img/@title"
            ),
            "position": "//span[text()='Position:']/following::span[1]/text()",
            "agent": (
                "//span[text()='Player agent:']/following::span[1]/a/text()"
            ),
            "strong_foot": "//span[text()='Foot:']/following::span[1]/text()",
            "joined": "//span[text()='Joined:']/following::span[1]/text()",
            "contract_expiry": (
                "//span[text()='Contract expires:']/following::span[1]/text()"
            ),
        }
        for k, v in key_path_dict.items():
            data_dict[k].append(add_item(dom, v))
        club_url = str(
            dom.xpath(
                "//span[contains(text(),'Current"
                " club:')]/following::span[1]/a/@href"
            )[0]
        )
        if club_url:
            data_dict["current_club"].append(club_url.split("/")[1])
            data_dict["current_club_id"].append(club_url.split("/")[-1])
        else:
            data_dict["current_club"].append(np.nan)
            data_dict["current_club_id"].append(np.nan)

    @staticmethod
    def extract_transfers(soup, player_url):
        def try_except_extract(item, expr, except_value=np.nan):
            try:
                return eval(expr)
            except Exception as e:
                return except_value

        info_dict = {
            var: []
            for var in [
                "season",
                "date",
                "left_team_season_transfers_url",
                "left_id",
                "left_country",
                "left_alt",
                "left_name",
                "joined_team_season_transfers_url",
                "joined_id",
                "joined_alt",
                "joined_name",
                "joined_country",
                "mv",
                "fee",
            ]
        }
        # find the transfer history table
        try:
            tbody = (
                soup.find(
                    attrs={"class": "box transferhistorie viewport-tracking"}
                )
                .find(attrs={"class": "responsive-table"})
                .find("tbody")
            )
        except:
            print(f"Fucked transer box {player_url}")
            return

        for row in tbody.find_all("tr"):
            tds = row.find_all("td")
            # skipping funky rows such as single row that says transfer history: or upcomming transfers:
            if len(tds) != 11:
                continue
            for k, v in {
                "season": "item[0].text",
                "date": "item[1].text",
                "left_team_season_transfers_url": "item[2].a['href']",
                "left_id": "item[2].a['id']",
                "left_alt": "item[2].a.img['alt']",
                "left_country": "item[3].img['alt']",
                "left_name": "item[4].text",
                "joined_team_season_transfers_url": "item[5].a['href']",
                "joined_id": "item[5].a['id']",
                "joined_alt": "item[5].a.img['alt']",
                "joined_country": "item[6].img['alt']",
                "joined_name": "item[7].text",
                "mv": "item[8].text",
                "fee": "item[9].text",
            }.items():
                # try-excepting everything:
                info_dict[k].append(try_except_extract(tds, v))
        temp_df = pd.DataFrame(info_dict)
        temp_df["tm_player_id"] = int(player_url.split("/")[-1])
        return temp_df

    @retry(
        wait=wait_random(min=10, max=30),
        stop=stop_after_attempt(5),
    )
    def scrape_player(self, player_url):
        soup = get_soup_from_url(player_url, self.session, proxies=PROXIES)
        dom = etree.HTML(str(soup))

        if soup is None:
            print(f"no soup {player_url}")
            return
        if not soup.find("h1", attrs={"itemprop": "name"}):
            print(f"no name {player_url}")
            return
        if dom.xpath("//span[text()='Retired']"):
            return
        try:
            self.data_dict["name"].append(
                soup.find(attrs={"itemprop": "name"}).text
            )
        except:
            print(f"no name f{player_url}")
            return  # exit cause we dont care about profile without name
        self.progress_tracker.append(player_url)

        self.data_dict["player_url"].append(player_url)
        self.data_dict["player_id"].append(player_url.split("/")[-1])

        self.get_base_info(dom, self.data_dict)
        try:
            flags = soup.find(attrs={"class": "dataZusatzDaten"}).find_all(
                attrs={"class": "flaggenrahmen"}
            )
            correct_flag = [
                x for x in flags if "show-for-small" not in x["class"]
            ][0]
            self.data_dict["league_tier"].append(
                correct_flag.find_parent().text
            )
            self.data_dict["league_country"].append(correct_flag["title"])
        except:
            self.data_dict["league_tier"].append(np.nan)
            self.data_dict["league_country"].append(np.nan)
        try:
            self.data_dict["current_value"].append(
                soup.find(attrs={"class": "zeile-oben"})
                .find(attrs={"class": "right-td"})
                .text
            )

            self.data_dict["last_update"].append(
                soup.find(attrs={"class": "zeile-mitte"})
                .find(attrs={"class": "right-td"})
                .text
            )

            self.data_dict["highest_value"].append(
                soup.find(attrs={"class": "zeile-unten"})
                .find(attrs={"class": "right-td"})
                .text
            )

            self.data_dict["highest_value_date"].append(
                soup.find(attrs={"class": "zeile-unten"})
                .find(attrs={"class": "right-td"})
                .span.text
            )
        except:
            self.data_dict["current_value"].append(np.nan)
            self.data_dict["last_update"].append(np.nan)
            self.data_dict["highest_value"].append(np.nan)
            self.data_dict["highest_value_date"].append(np.nan)

        temp_df = self.extract_transfers(soup, player_url)
        if temp_df is not None:
            self.transfer_dfs_list.append(temp_df)

    def concat_transfers_df(self):
        """
        Concat
        """
        df = pd.concat(self.transfer_dfs_list, axis=0)
        self.transfers_df = df

    def prep_players(self):
        try:
            self.df = pd.DataFrame(self.data_dict)
        except Exception as e:
            for key in self.data_dict:
                print(key, len(self.data_dict[key]))
            raise Exception(e)

    def export_scraped(self):
        try:
            self.df.to_csv(self.export_file, index=False, encoding="utf-16")
        except:
            sleep(30)
            self.df.to_csv(self.export_file, index=False, encoding="utf-16")

    def write_raw_transfers(self):
        """
        Read raw transfers, subset only new ones and write them to dev db
        """
        table_name = "raw_tm_transfers"
        collected = pd.read_sql(
            f"""SELECT concat("tm_player_id", ' ', "date") as transfer_id FROM  {table_name}""",
            self.cnx_dev,
        )
        tr_ids = (
            self.transfers_df["tm_player_id"].astype(str)
            + " "
            + self.transfers_df["date"]
        )
        write_df = self.transfers_df[~tr_ids.isin(collected.transfer_id)]
        write_df.to_sql(
            table_name, self.cnx_dev, if_exists="append", index=False
        )

        # important to reset list once each batch is saved so that we dont save dupes:
        self.transfer_dfs_list = []


# specifying scraper class so that loop players can be used with other scrapers
#  such as PlayerInjuryScraper
def loop_players(scraper_class, config, cnx_prod, cnx_dev, refresh):
    def wrap_up(scraper):
        # do this only if we have non-empty records:
        if len(scraper.data_dict["player_id"]) > 0:
            scraper.prep_players()
            if len(scraper.transfer_dfs_list) > 0:
                scraper.concat_transfers_df()
                scraper.write_raw_transfers()
            scraper.export_scraped()
            last_item = {k: v[-1] for k, v in scraper.data_dict.items()}
        scraper.save_tracker()

    scraper = scraper_class(config, cnx_prod=cnx_prod, cnx_dev=cnx_dev)
    scraper.get_priority_ids(refresh)
    scraper.load_tracker()
    id_series_sub = scraper.priority_players[
        ~scraper.priority_players.isin(scraper.progress_tracker)
    ]
    start = datetime.now()

    for idx, player in enumerate(id_series_sub):
        try:
            scraper.scrape_player(player)
        except Exception as e:
            # after 5 retries with tenacity, just save whatever we have
            wrap_up(scraper)
            # re-raise the exception so that script actually fails and pipeline doesnt
            # move forward
            raise Exception(str(e))
        # export every 250 observations
        if idx % 250 == 0 and idx > 0:
            wrap_up(scraper)
            sleep(np.random.uniform(1, 5))
            print(idx, "Time elapsed: ", datetime.now() - start)
        if idx % 50 == 0:
            print(idx, player)
    # export one last time
    wrap_up(scraper)
    print(f"Total runtime for {idx} players: {datetime.now()-start}")


def main():
    try:
        refresh = os.environ.get["REFRESH"]
    except:
        refresh = False

    cnx_prod = create_engine(postgres_prod_str)
    cnx_dev = create_engine(postgres_dev_str)
    config = read_config("config.yml")["scraping"]["directories"]
    loop_players(PlayerScraper, config, cnx_prod, cnx_dev, refresh)


if __name__ == "__main__":
    main()
