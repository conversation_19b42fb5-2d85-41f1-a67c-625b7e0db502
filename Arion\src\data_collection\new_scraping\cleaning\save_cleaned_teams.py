from sqlalchemy.engine import create_engine
from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import TeamsPrep
from src.helper_funcs import fast_read_sql
from src.helper_funcs import fast_write_sql

engine = create_engine(postgres_prod_str)
SCHEMA = "meta_scraping"


def main():
    prep = TeamsPrep()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.raw_tm_teams", engine)
    print(postgres_prod_str)
    try:
        fast_write_sql(
            prep.clean_df(df),
            "tm_teams",
            cnx=engine,
            if_exists="replace",
            schema="transfermarkt",
        )
        engine.execute(f"DELETE FROM {SCHEMA}.raw_tm_teams")
    except Exception as e:
        raise (e)


if __name__ == "__main__":
    main()
