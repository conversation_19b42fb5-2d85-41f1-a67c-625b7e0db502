import asyncio
import os

from src.data_collection.wyscout.v2.TransfersUpdater import TransfersUpdater
from src.data_collection.wyscout.v2.TransfersOrchestrator import (
    TransfersOrchestrator,
)
from src.data_collection.wyscout.v2.Updates<PERSON>hecker import Updates<PERSON>hecker


async def main():
    collection_mode = os.environ["COLLECTION_MODE"]
    phantom_objects = None
    if collection_mode == "initial":
        from_scratch = True
    elif collection_mode == "update":
        from_scratch = False
        phantom_objects = ["players"]
    else:
        raise ValueError("Invalid collection mode")

    transfers_checker = UpdatesChecker(
        table_name="transfers_for_collection",
        object_type="transfers",
        id_name="playerId",
        from_scratch=from_scratch,
        all_objects_table="players",
    )
    transfers_updater = TransfersUpdater("transfers")
    orc = TransfersOrchestrator(
        batch_size=2000,
        updates_checker=transfers_checker,
        updater=transfers_updater,
        from_scratch=from_scratch,
        phantom_objects=phantom_objects,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
