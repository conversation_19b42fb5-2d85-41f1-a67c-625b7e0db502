import os
import sys
from datetime import datetime, timedelta
import yaml

from airflow import DAG
from airflow.operators.bash_operator import Bash<PERSON>perator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, db_config, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "match_tm_profiles_w_search_dag",
    "start_date": datetime(2020, 7, 14),
    "schedule_interval": timedelta(days=90),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}
with DAG(**dag_params) as dag:
    update_players_for_matching_table = PostgresOperator(
        task_id="update_players_for_matching_table",
        database="wyscout_raw_production",
        sql=open(
            workdir + "/src/queries/check_unmatched_relevant_tm_players.sql"
        ).read(),
    )
    match_tm_profiles_w_search = BashOperator(
        task_id="match_tm_profiles_w_search",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/data_collection/scraping/scraping/match_tm_profiles_through_search_v2.py""",
    )
    # append_manually_matched_players = PostgresOperator(
    #     task_id="append_manually_matched_players",
    #     database="wyscout_raw_production",
    #     sql=open(
    #         workdir + "/src/queries/manual_player_matching_insert.sql"
    #     ).read(),
    # )

(
    update_players_for_matching_table
    >> match_tm_profiles_w_search
    # >> append_manually_matched_players
)
