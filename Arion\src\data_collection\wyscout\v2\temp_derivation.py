from time import sleep
from multiprocessing import Pool, cpu_count
from typing import List
import os
os.chdir('/airflow/ArionFlow/Arion')

import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from src.helper_funcs import (
    fast_write_sql,
    fast_read_sql,
    get_sql_array_str,
    get_cloud_logger,
    cloud_log_text,
    cloud_log_struct,
    check_existance,
)
from src.data_collection.wyscout.v2.AdvancedStatsPrepper import (
    prep_advanced_stats_prederivation, AdvancedStatsPrepper
)


cnx_prod = create_engine(postgres_prod_str)
batch = [5347502, 5359079, 5359086, 5359087, 5359082, 5359096, 5359089,
       5359092, 5359095, 5359098, 5359091, 5359080, 5359081, 5359094,
       5359093, 5359088, 5359083, 5359085, 5359090, 5359084, 5359097]

matches = fast_read_sql(
            f""" SELECT events FROM wyscout.events WHERE "matchId" IN {tuple(batch)} """,
            cnx_prod,
        )["events"].tolist()

df = prep_advanced_stats_prederivation(matches[0])
ASP = AdvancedStatsPrepper('tmp_table')
output = ASP.prep(matches[0])
df = output['grouped_df']