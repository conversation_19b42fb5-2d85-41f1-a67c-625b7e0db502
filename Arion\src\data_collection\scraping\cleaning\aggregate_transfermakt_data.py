import pandas as pd


def read_data():
    df = pd.read_csv("data/scraped/processed/players_w_team_league.csv")
    return df


def agg_league_level(df, agg_cols, value_col):
    agg = df.groupby(agg_cols)[value_col].median().reset_index()
    return agg


def write_data(data):
    data.to_csv("data/scraped/processed/median_league_value.csv")


def main():
    df = read_data()
    df = agg_league_level(
        df,
        agg_cols=["continent", "league_id"],
        value_col="current_market_value",
    )
    write_data(df)


if __name__ == "__main__":
    main()
