with competitions as (
	select *
	from wyscout.competitions c2 
	where c2."format" = 'Domestic league'
                and POSITION('friend' IN c2."name") = 0
                and gender = 'male'
                and category = 'default'
                and "divisionLevel" > 0
) 
,fq as (
    select distinct "home_teamId" as "teamId",
        "matchId",
        "date",
        "competitionId"
    from wyscout.matches
    where "date" >= minim_date
        and "date" <= max_date
        and "competitionId" in (
            select "competitionId"
            from competitions
        )
    union
    select distinct "away_teamId" as "teamId",
        "matchId",
        "date",
        "competitionId"
    from wyscout.matches
    where "date" >= minim_date
        and "date" <= max_date
        and "competitionId" in (
            select "competitionId"
            from competitions
        )
),
competition_inits as (
select * from config.elo_initializations ei 
)
select distinct on(fq."teamId", fq."matchId") fq."teamId",
    fq."matchId",
    fq."date",
    fq."competitionId",
    sq."mean_player_value"
from fq
    left join competition_inits sq on fq."competitionId" = sq."competitionId"
order by "teamId",
    "matchId"

--select pmw1.*, pmw
--from (select * from player_match_weights pmw1 where mv is not null),
--player_match_weights pmw2

--select * 
--from missing_competitions