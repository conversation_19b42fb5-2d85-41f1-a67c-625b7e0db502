import pandas as pd
from src.data_collection.wyscout.v2.Updater import Updater


class CupsStandingsUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/career"
        self.object_type = "rounds"
        self.if_exists = "replace"
        self.id_name = "seasonId"
        self.write_in_loop = True

    def get_objects_for_collection(self):
        cups_query = """SELECT "seasonId" 
                        FROM wyscout.seasons
                        WHERE 
                        "competitionId" IN (82, 84, 108, 109 )
                        and active = 'false' """
        self.collection_list = pd.read_sql(cups_query, self.cnx_prod)[
            "seasonId"
        ].values

    @staticmethod
    def map_stage_names(x):
        x = x.copy()
        # whole thing is fucked because
        # 2nd round in libertadores is qualifications,
        # while secound round in sudamericana is best of 32
        stage_dict = {
            "8th finals": "Round of 16",
            "16th finals": "Round of 32",
            "quarter-finals": "Quarter-Finals",
            "semi-finals": "Semi-Finals",
            "group stage": "Group Stage",
            "final": "Final",
            "finals": "Final",
        }
        x["roundName"] = stage_dict.get(str(x["roundName"]).lower(), "Other")
        return x

    @staticmethod
    def order_stages(x):
        def inner_order(x):
            order_dict = {
                "Round of 16": 3,
                "Round of 32": 2,
                "Quarter-Finals": 4,
                "Semi-Finals": 5,
                "Group Stage": 1,
                "Final": 6,
                "Finals": 6,
                "Other": 0,
            }
            return order_dict[x]

        return max(x, key=lambda x: inner_order(x))

    def prep(self, results):
        cups_df_list = []
        for rounds in results:
            teams = []
            for group in rounds["groups"]:
                for t in group["teams"]:
                    teams.append(t)
        teams = list(map(self.map_stage_names, teams))
        temp_df = pd.DataFrame(teams)
        best_placement = (
            temp_df.groupby(["teamId", "seasonId"])["roundName"]
            .apply(self.order_stages)
            .reset_index()
        )
        cups_df_list.append(best_placement)
        return pd.concat(cups_df_list)
