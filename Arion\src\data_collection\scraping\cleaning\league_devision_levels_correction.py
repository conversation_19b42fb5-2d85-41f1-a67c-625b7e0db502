# key-value pairs represent competitionId: divisionLevel,
# where divisionLevel is the correct value to be replaced in data table

division_level_dict = {
    130: 2,  # Ligue 2 - Algeria
    150: 4,  # Brisbane - Australia
    157: 2,  # Northern NSW - Australia
    285: 4,  # Tercera A - Chile
    286: 2,  # China League One
    296: 2,  # Primera B - Colombia
    340: 2,  # Primera B - Ecuador
    460: 2,  # HFKA 1st Devision - Hong Kong
    489: 2,  # Liga 2 - Indonesia
    493: 2,  # Azadegan League - Iran
    556: 2,  # Division 1 - Kuwait
    598: 4,  # FAM League Malaysia - this one is 50/50 because it is knockout
    # between 3rd and 4th division teams, using 4 to stay on safe side
    601: 2,  # Premier League - Malaysia
    627: 2,  # Botola 2 - <PERSON><PERSON>
    739: 2,  # Division 1 - Saudi Arabia
    740: 3,  # Division 2 -  Saudi Arabia
    822: 5,  # Liga Interregional - Switzerdland
    1231: 3,  # Segunda Division - Chile
    710: 1,  # LNFPR First Division - Puerto Rico
    632: 1,  # National League - Nepal,
    589: 1,  # Primeira DivisÃ£o - Macao
    548: 1,  # Premier League - Kenya
    326: 1,  # Ligue 1 - Ivory Coast
    43123: 1,  # Canadian Premier League
    536: 4,  # Japan Football League
}
