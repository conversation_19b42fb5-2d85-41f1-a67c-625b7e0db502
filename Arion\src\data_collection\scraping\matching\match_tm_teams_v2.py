from time import time
from sqlalchemy import create_engine
import pandas as pd
import numpy as np
import string
import re
from fuzzywuzzy import process
from settings import postgres_prod_str
from src.helper_funcs import fast_write_sql


class TeamNameMatcher:
    def __init__(self, match_threshold, cnx):
        self.cnx = cnx
        self.match_threshold = match_threshold

    def read_data(self):

        ws_query = """ select *
                    FROM teams """

        tm_query = """ select * from tm_teams"""
        # teams_init = pd.read_sql('''SELECT * FROM competition_teams;''', cnx)
        self.ws_teams = pd.read_sql(ws_query, self.cnx)
        self.tm_teams = pd.read_sql(tm_query, self.cnx)

    @staticmethod
    def handle_youth_teams(s):
        # Under whatever
        Uxx_search1 = re.search(r"u[\d]{2,2}", s)
        Uxx_search2 = re.search(r"under [\d]{2,2}", s)
        Uxx_search3 = re.search(r"under[\d]{2,2}", s)
        youth_search = re.search(
            r"\s*\byth\b\s*|\s*\byouth\b\s*|\s*\bjugend\b\s*", s
        )

        if (
            (Uxx_search1 is not None)
            | (Uxx_search2 is not None)
            | (Uxx_search3 is not None)
        ):
            under_team = 1
        else:
            under_team = 0

        if youth_search is not None:
            youth_team = 1
        else:
            youth_team = 0

        s = re.sub(r"\s*\byth\b\s*|\s*\byouth\b\s*|\s*\bjugend\b\s*", "", s)
        s = re.sub(r"u[\d]{2,2}", "", s)
        s = re.sub(r"under [\d]{2,2}", "", s)
        s = re.sub(r"under[\d]{2,2}", "", s)

        return s, under_team, youth_team

    @staticmethod
    def handle_second_teams(s):
        # Second team
        st_search = re.search(r"\s*\bii\b\s*|\s*\bb\b\s*", s)
        if st_search is not None:
            second_team = 1
        else:
            second_team = 0

        s = re.sub(r"\s*\bii\b\s*|\s*\bb\b\s*", "", s)

        return s, second_team

    def find_abbreviations(self):
        tnames = self.ws_teams["officialName"].str.cat(sep=" ")
        words = re.findall(r"\b[A-Z]{2,3}\b", tnames)
        abb_counts = pd.Series(words).value_counts()
        # abb_percentages = abb_counts / sum(abb_counts)
        self.stop_words = list(abb_counts.index)

    def clean_string(self, s):
        s = re.sub("-", " ", s)
        exclude = set(string.punctuation)
        s = "".join(ch for ch in s if ch not in exclude)
        # s = re.sub(r'[A-Z]{2,5}', '', s) # Regex to match FC etc but won't work bc tm data is lowercase
        s = s.lower().strip()

        s, under_team, youth_team = self.handle_youth_teams(s)
        s, second_team = self.handle_second_teams(s)

        s = re.sub(
            r"{}".format(
                "|".join([rf"\s*\b{ss.lower()}\b\s*" for ss in self.stop_words])
            ),
            " ",
            s,
        )
        s = s.strip()

        output = {
            "fixed_string": s,
            "under_team": under_team,
            "youth_team": youth_team,
            "second_team": second_team,
        }

        return output

    def clean_team_names(self, df, col):
        df = df.copy()
        s_outs = df[col].apply(self.clean_string)
        s_outs = pd.DataFrame(list(s_outs))
        df["clean_names"] = s_outs["fixed_string"]
        df["youth_team"] = s_outs["youth_team"]
        df["under_team"] = s_outs["under_team"]
        df["second_team"] = s_outs["second_team"]
        df = df[df["clean_names"].apply(lambda x: len(str(x))) > 2]
        return df

    def match_datasets(self):
        start = time()
        tm_unique = self.tm_teams.drop_duplicates(subset=["team_name"])
        ws_unique = self.ws_teams.drop_duplicates(subset=["officialName"])
        merged = pd.DataFrame(
            columns=[
                "teamId",
                "transfermakt_team_id",
                "officialName",
                "transfermakt_team_name_full",
                "youth_team",
                "under_team",
                "second_team",
            ]
        )

        for i, row in tm_unique.iterrows():
            # if i==100:
            #     print(time() - start)
            #     return merged
            # just not gonna match dupes, there are very few and we can sort by hand if necessary
            name = row["clean_names"]
            print(name)
            merged.loc[i, "transfermakt_team_id"] = row["team_id"]
            merged.loc[i, "transfermakt_team_name_full"] = row["team_name"]
            merged.loc[i, "youth_team"] = row["youth_team"]
            merged.loc[i, "under_team"] = row["under_team"]
            merged.loc[i, "second_team"] = row["second_team"]

            # Make sure to use only relevant teams
            if (row["youth_team"] == 1 & row["second_team"] == 1) | (
                row["under_team"] == 1 & row["second_team"] == 1
            ):
                row["second_team"] = 0

            ws_subset = ws_unique[
                (ws_unique["youth_team"] == row["youth_team"])
                & (ws_unique["under_team"] == row["under_team"])
                & (ws_unique["second_team"] == row["second_team"])
            ].copy()

            if (
                len(tm_unique[tm_unique["clean_names"] == name]) == 1
                and len(ws_subset[ws_subset["clean_names"] == name]) == 1
            ):
                merged.loc[i, "teamId"] = (
                    ws_subset[ws_subset.clean_names == name]
                    .reset_index()
                    .teamId[0]
                )
                merged.loc[i, "officialName"] = (
                    ws_subset[ws_subset.clean_names == name]
                    .reset_index()
                    .officialName[0]
                )
            else:
                fuzzy_match = process.extractOne(name, ws_subset["clean_names"])
                if (
                    fuzzy_match[1] > self.match_threshold
                    and ws_subset[ws_subset["clean_names"] == fuzzy_match[0]][
                        "teamId"
                    ].nunique()
                    == 1
                ):
                    merged.loc[i, "teamId"] = ws_subset[
                        ws_subset["clean_names"] == fuzzy_match[0]
                    ].reset_index()["teamId"][0]
                    merged.loc[i, "officialName"] = ws_subset[
                        ws_subset["clean_names"] == fuzzy_match[0]
                    ].reset_index()["officialName"][0]

                else:
                    merged.loc[i, "teamId"] = np.nan
                    merged.loc[i, "officialName"] = np.nan
        print(time() - start)
        return merged

    def match_youth_to_all(self):
        pass

    # TODO Separate under whatever matching


def main():

    cnx = create_engine(postgres_prod_str)
    TNM = TeamNameMatcher(90, cnx)
    TNM.read_data()
    TNM.find_abbreviations()
    ws_teams = TNM.clean_team_names(TNM.ws_teams, "officialName")
    tm_teams = TNM.clean_team_names(TNM.tm_teams, "team_name")

    TNM.ws_teams = ws_teams.copy()
    TNM.tm_teams = tm_teams.copy()

    merged_df = TNM.match_datasets()
    fast_write_sql(merged_df, "tm_to_ws_team_ids", cnx)

    # write_data(merged_df)
    # return merged_df


if __name__ == "__main__":
    main()
