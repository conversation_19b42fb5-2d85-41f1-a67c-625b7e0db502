from sqlalchemy import create_engine
from src.models.player_valuation.utils.settings_gcloud import postgres_prod_str, postgres_prod_str_unix
# from settings_gcloud import postgres_prod_str


def main(use_unix = False):

    if use_unix:
        cnx_prod = create_engine(postgres_prod_str_unix)
    else:
        cnx_prod = create_engine(postgres_prod_str)

    return cnx_prod

# if __name__ == "__main__":
#     main()