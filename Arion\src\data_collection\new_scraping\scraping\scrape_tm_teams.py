from multiprocessing import cpu_count, Pool
import time
import aiohttp
import asyncio
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
from pandas.core.frame import DataFrame
from typing import  List
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)
#import sys
#
#policy = asyncio.WindowsSelectorEventLoopPolicy()
#asyncio.set_event_loop_policy(policy)

pd.set_option("display.max_columns", 15)


class TeamsScraper(Scraper):
    async def loop_through_urls(self, urls: list) -> List[pd.DataFrame]:
        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for url in urls:
                html_tasks.append(fetch(session, url))
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> List[pd.DataFrame]:
        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_page, htmls)
        pool.close()
        pool.join()

        teams_df = []

        for data in res:
            if data:
                teams_df.append(data)
        return pd.DataFrame(teams_df)

    def scrape_page(self, tuple):
        text, url = tuple

        teams_dict = {
            "team_id": np.nan,
            "team_name": np.nan,
            "league_name": np.nan,
            "league_country": np.nan,
            "league_tier": np.nan,
            "team_url": np.nan,
        }

        soup = BeautifulSoup(text, "html.parser")

        team_name = soup.find(
            "h1",
            attrs={
                "class": (
                    "data-header__headline-wrapper"
                    " data-header__headline-wrapper--oswald"
                )
            },
        )

        if soup is None:
            print(f"no soup {url}")
            time.sleep(5)
            return {}

        if not team_name:
            print(f"no name {url}")
            time.sleep(1)
            return {}
        teams_dict["team_name"] = team_name.text.strip()
        teams_dict["team_id"] = url.split("/")[-1]
        teams_dict["team_url"] = url

        league_el = soup.find("div", attrs={"class": "data-header__box--big"})
        if league_el:
            data_contents_list = league_el.find_all(
                "span", attrs={"class": "data-header__content"}
            )
            try:
                teams_dict["league_tier"] = (
                    data_contents_list[0].text.split(":")[-1].strip()
                )
            except:
                teams_dict["league_tier"] = np.nan
            try:
                teams_dict["league_country"] = data_contents_list[0].find(
                    "img"
                )["title"]
            except:
                teams_dict["league_country"] = np.nan
            try:
                teams_dict["league_name"] = league_el.find(
                    "span", attrs={"class": "data-header__club"}
                ).text.strip()
            except:
                teams_dict["league_name"] = np.nan

        return teams_dict


if __name__ == "__main__":
    scraper = TeamsScraper()
    ll = asyncio.run(
        scraper.loop_through_urls(
            urls=[
                "https://www.transfermarkt.com/fc-bayern-munchen/startseite/verein/27"
            ]
        )
    )
    print(ll)
