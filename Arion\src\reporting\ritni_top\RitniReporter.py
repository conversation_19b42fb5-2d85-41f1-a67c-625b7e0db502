# from datetime import datetime, timedelta
import pandas as pd
from src.reporting.Reporter import Reporter, ContentType, ReportContent

from src.helper_funcs import fast_read_sql, get_time_stamp


class RitniReporter(Reporter):
    def __init__(self):
        super().__init__()

    def get_latest_active_season(self) -> int:
        ss = fast_read_sql(
            """select "seasonId" from wyscout.seasons ss, wyscout. competitions  cs
                where cs."competitionId" = ss."competitionId" 
                and cs.area_name = 'Bulgaria'
                and cs."divisionLevel" = 1
                and cs.format = 'Domestic league' 
                and "startDate"::date  < now() 
                and "endDate":: date > now()""",
            self.cnx_prod,
        )["seasonId"].to_list()
        if len(ss) != 1:
            raise Exception(
                "There should be only 1 season for the given params"
            )
        return ss[0]

    def get_data(self, seasonId: int) -> pd.DataFrame:
        return fast_read_sql(
            f"""SELECT  
                                    pl."firstName" ||' ' || pl."lastName" as name,
                                    pmi.* FROM wyscout.player_match_info pmi,
                                wyscout.players pl where pmi."playerId" = pl."playerId"
                                and pmi."seasonId" = {seasonId}
                             """,
            self.cnx_prod,
        )

    def get_game_info(self, seasonId: int) -> pd.DataFrame:
        return fast_read_sql(
            f"""select m.label, m.date, m."matchId", 
                case
                when ev."matchId" is not null  then 'YES' 
                when ev."matchId" is null  then 'NO'
                end as "has_stats"
                from wyscout.matches m left join wyscout.events ev using ("matchId")
                where m."seasonId"  = {seasonId}
                order by m.date desc
                """,
            self.cnx_prod,
        )

    def make_report(self):
        sid = self.get_latest_active_season()
        stats = self.get_data(sid)
        game_info = self.get_game_info(sid)

        self.content_list.append(
            ReportContent(
                type=ContentType.plain_text,
                content=(
                    f"Sending all games updated as of {get_time_stamp()}. The"
                    " table below lists game information and data"
                    " availability:  \n"
                ),
            )
        )
        self.content_list.append(
            ReportContent(
                type=ContentType.body_html,
                content=self.make_html_table(game_info)
            )
        )
        self.content_list.append(
            ReportContent(
                type=ContentType.attachment_file,
                content=self.export_csv(stats),
                file_format="csv"
            )
        )