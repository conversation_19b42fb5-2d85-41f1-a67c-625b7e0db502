CREATE TABLE derived_tables.team_segment_bands AS
SELECT
	fin.name,
	fin."teamId",
	COALESCE(fin.team_rank, 4) AS segment
FROM (
	SELECT
		tt.name,
		tt."teamId",
		base.team_rank
	FROM
		wyscout.teams tt
	LEFT JOIN (
		SELECT
			CASE WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) <= 20 THEN
				1
			WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) BETWEEN 21 AND 60 THEN
				2
			WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) BETWEEN 61 AND 600 THEN
				3
			ELSE
				4
			END AS team_rank,
			base."teamId"
		FROM (
			SELECT
				percentile_disc(0.5) WITHIN GROUP (ORDER BY tm.current_value) AS median_player_value,
				t."teamId",
				t."name"
			FROM
				transfermarkt.transfermarkt_data tm,
				wyscout.players pl,
				wyscout.teams t
			WHERE
				pl."currentTeamId" = t."teamId"
				AND tm."playerId" = pl."playerId" GROUP BY
					t."teamId",
					t."name"
				HAVING
					count(*) > 10
				ORDER BY
					percentile_disc(0.5) WITHIN GROUP (ORDER BY tm.current_value) DESC nulls LAST) base) base USING ("teamId")) fin
ORDER BY
	segment;

GRANT ALL ON derived_tables.team_segment_bands TO elvan, kliment;

ALTER TABLE derived_tables.team_segment_bands
	ADD PRIMARY KEY ("teamId");

