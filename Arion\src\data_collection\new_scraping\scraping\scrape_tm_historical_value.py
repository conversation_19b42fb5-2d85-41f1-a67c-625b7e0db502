import asyncio
import re
import aiohttp
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)
from bs4 import BeautifulSoup
import pandas as pd
from multiprocessing import Pool, cpu_count

#policy = asyncio.WindowsSelectorEventLoopPolicy()
asyncio.set_event_loop_policy(policy)
pd.set_option("display.max_columns", 21)


class HistoricalValueScraper(Scraper):
    async def loop_through_urls(
        self, urls: list, ids: list
    ) -> "list[pd.DataFrame]":
        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for url, id in zip(urls, ids):
                html_tasks.append(
                    fetch(
                        session, url.replace("profile", "marktwertverlauf"), id
                    )
                )
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> "pd.DataFrame":

        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_page, htmls)
        pool.close()
        pool.join()

        big_list = []
        for data in res:
            [big_list.append(d) for d in data if d]
        return pd.DataFrame(big_list)

    def scrape_page(self, tuple):  # sourcery no-metrics
        text, url, wyscout_id = tuple

        try:
            soup = BeautifulSoup(text, "html.parser")
            script = soup.find(
                "script", text=re.compile("Highcharts.Chart")
            ).text
            array_of_js_objects = re.search("\[{.+}]", script)
            all_data = array_of_js_objects.group()
        except:
            return [
                {
                    "playerId": wyscout_id,
                    "date_collected": None,
                    "market_value": None,
                }
            ]

        tm_values = []
        collect_data = False

        for data in all_data.split(","):
            try:
                if "'datum_mw'" in data:
                    month, day = data.split(":'")[-1].split("\\x20")
                elif "'mw'" in data:
                    value = data.split("\\u20AC")[-1].split("'")[0]
                elif "\\x2020" in data:
                    year = data.split("'")[0].split("\\x20")[-1]
                    collect_data = True
            except Exception as e:
                print(e)
                break
            if collect_data:
                try:
                    if len(day) == 1:
                        day = f"0{day}"
                    tm_values.append(
                        {
                            "playerId": wyscout_id,
                            "date_collected": f"{month} {day}, {year}",
                            "market_value": value,
                        }
                    )
                    collect_data = False
                except:
                    return [
                        {
                            "playerId": wyscout_id,
                            "date_collected": None,
                            "market_value": None,
                        }
                    ]
        print(wyscout_id)
        return tm_values


if __name__ == "__main__":
    scraper = HistoricalValueScraper()
    ll = asyncio.run(
        scraper.loop_through_urls(
            urls=[
                "https://www.transfermarkt.com/cristiano-ronaldo/marktwertverlauf/spieler/8198"
            ],
            ids=[123],
        )
    )
