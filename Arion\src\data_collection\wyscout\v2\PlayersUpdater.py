import json
from typing import Union, List

import pandas as pd
import numpy as np
from sqlalchemy import types

from src.data_collection.wyscout.v2.Updater import Updater


class PlayersUpdater(Updater):
    def __init__(self):
        super().__init__(None)
        self.base_url = "https://apirest.wyscout.com/v3/players/_ID"
        self.object_type = self.base_url.split("/")[-2]
        self.id_name = "playerId"
        self.if_exists = "append"
        self.prepped_ids_list = []
        self.dtype = {
            "playerId": types.Integer,
            "shortName": types.Text,
            "firstName": types.Text,
            "middleName": types.Text,
            "lastName": types.Text,
            "height": types.Integer,
            "weight": types.Integer,
            "birthDate": types.Text,
            "foot": types.Text,
            "currentTeamId": types.Integer,
            "currentNationalTeamId": types.Integer,
            "gender": types.Text,
            "status": types.Text,
            "birthArea_id": types.Integer,
            "birthArea_alpha2code": types.Text,
            "birthArea_alpha3code": types.Text,
            "birthArea_name": types.Text,
            "passportArea_id": types.Integer,
            "passportArea_alpha2code": types.Text,
            "passportArea_alpha3code": types.Text,
            "passportArea_name": types.Text,
            "role_name": types.Text,
            "role_code2": types.Text,
            "role_code3": types.Text,
        }

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[dict, List[dict]]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        return [json.loads(resp)]

    def prep(self, results):
        df = pd.DataFrame(results)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df = df.rename(columns={"wyId": "playerId"})
        for col in ["firstName", "lastName", "shortName"]:
            df[col] = df[col].str.replace("(\\r|\\n)", "", regex=True)
        for col, col_type in self.dtype.items():
            if col_type == types.Integer:
                df[col] = df[col].replace("", np.nan).astype("Int64")

        return df
