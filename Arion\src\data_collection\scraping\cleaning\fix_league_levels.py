import pandas as pd

from src.data_collection.scraping.cleaning.league_devision_levels_correction import (
    division_level_dict,
)
from src.helper_funcs import read_config


def map_correct_divison_levels(df, map_dict):
    for key in map_dict:
        df.loc[df["competitionId"] == key, "divisionLevel"] = map_dict[key]
    return df


def fix_league_levels(take_df=None, return_df=False):
    config = read_config()
    if take_df is None:
        df_loc = config["scraping"]["directories"][
            "imputed_league_mean_player_values"
        ]
        df = pd.read_csv(df_loc, index_col=0)
    else:
        df = take_df.copy()
    df = map_correct_divison_levels(df, division_level_dict)
    out_path = config["scraping"]["directories"]["ws_fixed_tiers"]
    df.to_csv(out_path)
    if return_df:
        return df


if __name__ == "__main__":
    fix_league_levels()
