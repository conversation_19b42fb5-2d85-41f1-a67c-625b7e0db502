WITH matched_teams AS (
	SELECT
		*
	FROM
		transfermarkt.tm_to_ws_team_ids ttwti
),
tm_teams2 AS (
	SELECT DISTINCT ON (team_id)
		*
	FROM (
		SELECT
			tt.joined_id AS team_id,
			replace(tt.joined_name, 'Sub-', 'U') AS team_name,
			replace(tt.joined_alt, 'Sub-', 'U') AS team_name_alt,
		tt2.league_name,
		tt2.league_country,
		tt2.league_tier,
		tt2.team_url
	FROM
		transfermarkt.tm_transfers tt,
		transfermarkt.tm_teams tt2
	WHERE
		tt.joined_id = tt2.team_id
		AND tt."type" IN ('transfer', 'free transfer', 'loan')
	UNION
	SELECT
		tt.left_id AS team_id,
		tt.left_name AS team_name,
		tt.left_alt AS team_name_alt,
		tt2.league_name,
		tt2.league_country,
		tt2.league_tier,
		tt2.team_url
	FROM
		transfermarkt.tm_transfers tt,
		transfermakrt.tm_teams tt2
	WHERE
		tt.left_id = tt2.team_id
		AND tt."type" IN ('transfer', 'free transfer', 'loan')) iq
ORDER BY
	team_id
),
tm_teams_tmp AS (
	SELECT
		*,
		(
			SELECT
				regexp_matches(lower(tt.team_name), '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})')) AS under_team
		FROM
			tm_teams2 tt
),
tm_teams AS (
	SELECT
		*,
		coalesce((
			SELECT
				regexp_matches(under_team[1], '([\d]{2,2})'))[1]::int, 0) AS under_tm_team
FROM
	tm_teams_tmp
	WHERE
		tm_teams_tmp."team_id" NOT IN (
			SELECT
				transfermarkt_team_id
			FROM
				matched_teams)
			-- and tm_teams_tmp.league_country is not null
),
ws_teams AS (
	SELECT
		*,
		(
			SELECT
				regexp_matches(lower(t.name), '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})')) AS under_team
	FROM
		wyscout.teams t
),
teams AS (
	SELECT
		*,
		coalesce((
			SELECT
				regexp_matches(under_team[1], '([\d]{2,2})'))[1]::int, 0) AS under_ws_team
	FROM
		ws_teams
	WHERE
		"type" = 'club'
		AND "teamId" NOT IN (
			SELECT
				"teamId"
			FROM
				matched_teams)
),
outstanding_teams AS (
	SELECT DISTINCT ON (iq.transfermarkt_team_id)
		transfermarkt_team_id,
		transfermarkt_team_name_full,
		transfermarkt_team_name_alt,
		"teamId",
		"officialName",
		team_name,
		under_tm_team,
		under_ws_team,
		string_dist_official,
		string_dist,
		minimum_string_dist,
		minimum_string_dist::float / least (char_length(transfermarkt_team_name_full), char_length(transfermarkt_team_name_alt)) AS portion_of_name
	FROM (
		SELECT
			team_id AS transfermarkt_team_id,
			team_name AS transfermarkt_team_name_full,
			team_name_alt AS transfermarkt_team_name_alt,
			"teamId",
			"officialName",
			"name" AS team_name,
			under_tm_team,
			under_ws_team,
			levenshtein (team_name, "officialName", 1, 1, 1) AS string_dist_official,
			levenshtein (team_name, "name", 1, 1, 1) AS string_dist,
		max_similarity,
		CASE WHEN least (levenshtein (team_name, "officialName", 1, 1, 1), levenshtein (team_name_alt, "officialName", 1, 1, 1)) < least (levenshtein (team_name, "name", 1, 1, 1), levenshtein (team_name_alt, "name", 1, 1, 1)) THEN
			least (levenshtein (team_name, "officialName", 1, 1, 1), levenshtein (team_name_alt, "officialName", 1, 1, 1))
		ELSE
			least (levenshtein (team_name, "name", 1, 1, 1), levenshtein (team_name_alt, "name", 1, 1, 1))
		END AS minimum_string_dist
	FROM (
		SELECT
			*,
			similarity (iq.team_name, iq."name") AS sim_team,
		similarity (iq.team_name, iq."officialName") AS sim_official,
		CASE WHEN similarity (iq.team_name, iq."name") > similarity (iq.team_name, iq."officialName") THEN
			similarity (iq.team_name, iq."name")
		ELSE
			similarity (iq.team_name, iq."officialName")
		END AS max_similarity
	FROM (
		SELECT
			*
		FROM
			tm_teams tt,
			teams t
			--                            where tt.league_country = t.area_name
) iq) iq
	WHERE
		iq.under_tm_team = iq.under_ws_team) iq
	WHERE
		minimum_string_dist::float / least (char_length(transfermarkt_team_name_full), char_length(transfermarkt_team_name_alt)) <= 0.2
	ORDER BY
		transfermarkt_team_id,
		max_similarity DESC
),
tm_to_ws_team_ids AS (
	SELECT
		*,
		'secondary_postgres_regex' AS matched_through
	FROM
		outstanding_teams
)
SELECT
	*
FROM ( SELECT DISTINCT ON ("transfermarkt_team_id")
		*
	FROM ( SELECT DISTINCT ON ("teamId")
			*
		FROM
			tm_to_ws_team_ids
			--where transfermarkt_team_name_full like '%Arsenal%' or transfermarkt_team_name_alt  like '%Arsenal%'
		ORDER BY
			"teamId",
			minimum_string_dist ASC) qq
	ORDER BY
		transfermarkt_team_id,
		minimum_string_dist ASC) a
