TRUNCATE TABLE derived_tables.likely_expiring_agent_contracts;
INSERT INTO derived_tables.likely_expiring_agent_contracts
select "playerId", case when  now() - INTERVAL '18 MONTHS' > max(date_collected) then 1 else 0 end as likely_agent_contract_expiry ,  max(date_collected) as first_known_date_current_agent from( select "playerId", agent, date_collected, ROW_NUMBER() OVER(PARTITION BY "playerId", agent ORDER BY date_collected asc) as rn
from  transfermarkt.tm_historical_agents ) as a
where rn = 1 and agent is not null 
group by "playerId";

CREATE INDEX IF NOT EXISTS likely_expiring_agent_contracts_player ON derived_tables.likely_expiring_agent_contracts ("playerId");
GRANT ALL ON derived_tables.likely_expiring_agent_contracts TO elvan, kliment, ivan_dimitrov WITH GRANT OPTION;

TRUNCATE TABLE derived_tables.priority_players;
INSERT INTO derived_tables.priority_players
SELECT
	ap."firstName",
	ap."lastName",
	ap."height",
	ap."role_code2",
	ap."birthDate",
	ap."birthArea_name",
	ap."passportArea_name",
	ap."weight",
	ap."playerId",
	ap."foot",
	ti.name as team_name,
	ti.area_name as league_area,
	ti."divisionLevel" AS "divisionLevel",
	tm."agent1",
	tm."contract_expiry",
	tm."current_value",
	tm.highest_value,
	tm.highest_value_date,
	tm.date_joined_current_team,
	tm.player_value_last_update,
	ags.agency_player_count,
	ags.agency_highest_player_value,
	ts.date_of_last_transfer,
	tm.player_url,
	tmi.all_injuries,
	tmi.currently_injured,
	tmi.latest_injury,
	tmi.num_injuries,
	tmi.most_games_missed,
	tmi.longest_duration,
	tmi.total_games_missed,
	tmi.total_days_missed,
	tmi.average_recovery_time,
	pr.primary_position,
	pr.primary_position_percent,
	pr.secondary_position,
	pr.secondary_position_percent,
	pr.third_position,
	pr.third_position_percent,
	tha.likely_agent_contract_expiry,
	tha.first_known_date_current_agent,
	concat_ws(', ', 
	case	when 
		tm.agent1 = 'none'
	OR tm.agent1 = 'no agent'
	OR tm.agent1 = 'not clarified'
	then 'no_agent' else null end,
	case when tm.agent1 = 'Relatives'then 'relatives'else null end,
	case when ags.agency_player_count < 10 or ags.agency_highest_player_value < 10000000 then 'small_agency' else null end,
	case  when ts."playerId" IS null then 'no_transfer' else null end )as priority_reason

FROM (
	SELECT
		ap.*
	FROM
		wyscout.players ap,
		(
			SELECT
				count(pmi.*) AS num_games,
				pmi."playerId"
			FROM
				wyscout.player_match_info pmi,
				wyscout.players ap
			WHERE
				ap."playerId" = pmi."playerId"
				AND ap."gender" = 'male'
				AND ap."birthDate" < (now() - INTERVAL '16 YEARS')::text
				AND ap."birthDate" > (now() - INTERVAL '24 YEARS')::text
				AND ap."birthArea_name" NOT IN ('Russia', 'Ukraine', 'Serbia', 'Turkey', 'Georgia', 'Armenia')
			GROUP BY
				pmi."playerId"
			HAVING
				count(*) > 5) adv
		WHERE
			ap."playerId" = adv."playerId") ap
	LEFT JOIN (
		SELECT
			coalesce(agent, 'none') AS agent1,
			*
		FROM
			transfermarkt.transfermarkt_data) tm ON ap."playerId" = tm."playerId"
	LEFT JOIN derived_tables.player_roles pr ON ap."playerId" = pr."playerId"
	LEFT JOIN derived_tables.likely_expiring_agent_contracts tha ON ap."playerId" = tha."playerId"
	LEFT JOIN (
		SELECT
			count(*) AS agency_player_count,
			max(current_value) AS agency_highest_player_value,
			agent
		FROM
			transfermarkt.transfermarkt_data
		WHERE
			agent IS NOT NULL
			AND agent != 'Relatives'
			AND agent != 'no agent'
			AND agent != 'not clarified'
		GROUP BY
			agent) ags ON tm."agent" = ags."agent"
	LEFT JOIN (
		SELECT
			max("startDate") AS date_of_last_transfer,
			"playerId"
		FROM
			wyscout.transfers
		WHERE
			"type" IN ('Transfer', 'Free Transfer')
			AND "startDate" > (now() - INTERVAL '2 YEARS')::text
		GROUP BY
			"playerId") ts ON ap."playerId" = ts."playerId"
	LEFT JOIN wyscout.team_info ti ON ap."currentTeamId" = ti."teamId"
	LEFT JOIN (
		SELECT
			STRING_AGG(DISTINCT "injury", ', ') AS "all_injuries",
			"playerId",
			SUM(
				CASE WHEN injured_until IS NULL THEN
					1
				ELSE
					0
				END) AS "currently_injured",
			MAX("injured_until") AS "latest_injury",
			COUNT("playerId") AS "num_injuries",
			MAX("games_missed") AS "most_games_missed",
			MAX("duration") AS "longest_duration",
			SUM("games_missed") AS "total_games_missed",
			SUM("duration") AS "total_days_missed",
			AVG("duration") AS "average_recovery_time"
		FROM
			transfermarkt.transfermarkt_injuries
		GROUP BY
			"playerId") tmi ON ap."playerId" = tmi."playerId"
WHERE (tm.agent1 = 'none'
	OR tm.agent1 = 'Relatives'
	OR tm.agent1 = 'no agent'
	OR tm.agent1 = 'not clarified'
	OR ags.agency_player_count < 10
	OR ags.agency_highest_player_value < 10000000
	OR ts."playerId" IS NULL)
AND (ti.area_name IN ('Greenland', 'Gibraltar', 'Luxembourg', 'Czech Republic', 'Sweden', 'Serbia and Montenegro', 'San Marino', 'Macedonia FYR', 'Portugal', 'Malta', 'Albania', 'Ukraine', 'Latvia', 'Northern Ireland', 'Slovakia', 'Israel', 'Cyprus', 'Armenia', 'Scotland', 'Serbia', 'Georgia', 'Belgium', 'Monaco', 'Germany', 'England', 'Slovenia', 'Greece', 'Kosovo', 'Russia', 'Norway', 'Moldova', 'Poland', 'Ireland Republic', 'Montenegro', 'Finland', 'Wales', 'France', 'Iceland', 'Turkey', 'Netherlands', 'Spain', 'Bosnia-Herzegovina', 'Great Britain', 'Italy', 'Liechtenstein', 'Estonia', 'Netherlands antilles', 'Denmark', 'Switzerland', 'Hungary', 'Romania', 'Austria', 'Lithuania', 'Bulgaria', 'Croatia')
	OR ap."birthArea_name" IN ('Greenland', 'Gibraltar', 'Luxembourg', 'Czech Republic', 'Sweden', 'Serbia and Montenegro', 'San Marino', 'Macedonia FYR', 'Portugal', 'Malta', 'Albania', 'Latvia', 'Northern Ireland', 'Slovakia', 'Israel', 'Cyprus', 'Scotland', 'Belgium', 'Monaco', 'Germany', 'England', 'Slovenia', 'Greece', 'Kosovo', 'Norway', 'Moldova', 'Poland', 'Ireland Republic', 'Montenegro', 'Finland', 'Wales', 'France', 'Iceland', 'Netherlands', 'Spain', 'Bosnia-Herzegovina', 'Great Britain', 'Italy', 'Liechtenstein', 'Estonia', 'Netherlands antilles', 'Denmark', 'Switzerland', 'Hungary', 'Romania', 'Austria', 'Lithuania', 'Bulgaria', 'Croatia'));
CREATE INDEX IF NOT EXISTS idx_priority_players_player ON derived_tables.priority_players ("playerId");
GRANT ALL ON derived_tables.priority_players TO elvan, kliment, ivan_dimitrov WITH GRANT OPTION;

