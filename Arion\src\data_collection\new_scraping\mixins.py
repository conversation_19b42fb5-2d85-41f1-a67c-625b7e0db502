import numpy as np
import pandas as pd
from pandas._config.config import set_option
import re
import datetime


class TransferMartkMoneyPrepMixin:
    MONEY_COLUMNS = {
        "current_value",
        "highest_value",
        "fee",
        "mv",
        "total_market_value",
        "average_market_value",
        "market_value"
    }

    def __init__(self) -> None:
        pass

    def clean_money(self, df: pd.DataFrame):
        def conv_str_to_float(x):
            pattern = "\d+[\.\d+]*"
            if "m" in str(x):
                return (
                    float(re.search(pattern, x).group().replace(",", "."))
                    * 10 ** 6
                )
            elif "k" in str(x):
                return (
                    float(re.search(pattern, x).group().replace(",", "."))
                    * 10 ** 3
                )
            elif "bn" in str(x):
                return (
                    float(re.search(pattern, x).group().replace(",", "."))
                    * 10 ** 9
                )
            else:
                np.nan

        for column in self.MONEY_COLUMNS:
            if column in df:
                df[column] = df[column].apply(conv_str_to_float)
        return df


class TransferMartkDateMixin:
    DATE_COLUMNS = {
        "birth_date",
        "joined",
        "date_joined_current_team",
        "last_update",
        "contract_expiry",
        "date",
        "injured_from",
        "injured_until",
        "appointed",
        "in_charge_until",
        "highest_value_date",
        "player_value_last_update",
        "date_collected",
    }

    def __init__(self) -> None:
        pass

    def clean_dates(self, df: pd.DataFrame):
        margin_bottom = datetime.datetime.strptime("1900-01-01", "%Y-%m-%d")
        margin_top = datetime.datetime.strptime("2030-01-01", "%Y-%m-%d")

        def mdy_to_ymd(d):
            try:
                datetime.datetime.strptime(d, "%Y-%m-%d")
                if (
                    margin_bottom
                    <= datetime.datetime.strptime(d, "%Y-%m-%d")
                    <= margin_top
                ):
                    return d
                else:
                    raise Exception("Date out of range")
            except Exception:
                try:
                    d = datetime.datetime.strptime(d, "%b %d, %Y").strftime(
                        "%Y-%m-%d"
                    )
                    if (
                        margin_bottom
                        <= datetime.datetime.strptime(d, "%Y-%m-%d")
                        <= margin_top
                    ):
                        return d
                    else:
                        raise Exception("Date out of range")
                except Exception:
                    return np.nan

        for column in self.DATE_COLUMNS:
            if column in df:
                df[column] = df[column].apply(mdy_to_ymd)
        return df


class TmLeagueTierMixin:
    def __init__(self) -> None:
        pass

    def clean_league(self, df: pd.DataFrame):
        tier = {
            "First Tier": 1,
            "Second Tier": 2,
            "Third Tier": 3,
            "Fourth Tier": 4,
            "Fifth Tier": 5,
            "Sixth Tier": 6,
        }

        def clean_league_tier(x):
            if x in {
                "First Tier",
                "Second Tier",
                "Third Tier",
                "Fourth Tier",
                "Fifth Tier",
                "Sixth Tier",
            }:
                return tier[x]
            else:
                return np.nan

        df["league_tier"] = df["league_tier"].apply(clean_league_tier)

        return df
