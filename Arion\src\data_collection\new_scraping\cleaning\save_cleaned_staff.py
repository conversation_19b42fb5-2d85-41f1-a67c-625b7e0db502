from sqlalchemy.engine import create_engine
from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import StaffPrep
from src.helper_funcs import fast_read_sql
from src.helper_funcs import fast_write_sql

engine = create_engine(postgres_prod_str)
SCHEMA = "meta_scraping"

def main():
    prep = StaffPrep()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.raw_staff_data", engine)

    connection = engine.raw_connection()
    cursor = connection.cursor()
    try:
        fast_write_sql(
            prep.clean_df(df),
            "staff_data",
            cnx=engine,
            if_exists="replace",
            schema="transfermarkt",
            cursor=cursor,
            connection=connection,
            transaction=True
        )
        cursor.execute(""" DELETE FROM transfermarkt.staff_data sd using transfermarkt.staff_data sd1
                        WHERE sd.role = sd1.role and
                        sd.staff_id = sd1.staff_id and
                        sd.appointed = sd1.appointed and
                        sd.appointed is not null and
                        sd.ctid < sd1.ctid """)
        cursor.execute(f"DELETE FROM {SCHEMA}.raw_staff_data")
        connection.commit()
        cursor.close()

    except Exception as e:
        connection.rollback()
        cursor.close()
        raise (e)

if __name__ == "__main__":
    main()