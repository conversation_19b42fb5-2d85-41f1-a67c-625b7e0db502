
import asyncio

from src.data_collection.wyscout.v2.CoachUpdater import <PERSON><PERSON><PERSON>date<PERSON>, InitialCoachUpdater
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.UpdatesChecker import Updates<PERSON><PERSON>cker

async def collect_initial():
    upd = InitialCoachUpdater("coaches")
    upd.get_objects_for_collection()
    await upd.loop()

async def main():
    from_scratch = False  # here this is false because we are never updating teams, players, matches from scratch
    coaches_checker = UpdatesChecker(
        table_name="coaches_for_collection",
        object_type="coaches",
        id_name="coachId",
        from_scratch=from_scratch,
    )
    upd = CoachUpdater("coaches")
    orc = Orchestrator(
        batch_size=1000,
        updates_checker=coaches_checker,
        updater=upd,
        from_scratch=from_scratch,
    )
    await orc.loop()

if __name__ == "__main__":
    asyncio.run(main())
 