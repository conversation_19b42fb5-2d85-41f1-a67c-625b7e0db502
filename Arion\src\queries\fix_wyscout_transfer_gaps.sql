CREATE_TABLE IF NOT 

ALTER TABLE transfers
	ADD COLUMN IF NOT EXISTS from_team_without_gap bigint,
	ADD COLUMN IF NOT EXISTS to_team_without_gap bigint;

WITH transfer_df AS (
	SELECT
		lag("fromTeamId", 1) OVER (PARTITION BY t."playerId" ORDER BY t."startDate") AS prev_team_id,
		lead("toTeamId", 1) OVER (PARTITION BY t."playerId" ORDER BY t."startDate") AS next_team_id,
		lag("fromTeamName", 1) OVER (PARTITION BY t."playerId" ORDER BY t."startDate") AS prev_team_name,
		lead("toTeamName", 1) OVER (PARTITION BY t."playerId" ORDER BY t."startDate") AS next_team_name,
		lead("startDate", 1) OVER (PARTITION BY t."playerId" ORDER BY t."startDate") AS next_start_date,
		t.*,
		ap."firstName",
		ap."lastName"
	FROM
		transfers t,
		all_players ap
	WHERE
		t."playerId" = ap."playerId"
),
tdf AS (
	SELECT
		"playerId",
		"startDate",
		"fromTeamId",
		"toTeamId",
		next_start_date,
		CASE WHEN lower("type") IN ('free transfer', 'free agent')
			AND ("next_start_date" <= "endDate" + '30 days'::interval
				OR "endDate" IS NULL
				OR next_start_date IS NULL)
			AND "fromTeamId" = 0
			AND "toTeamId" != 0 THEN
			prev_team_id
		WHEN "fromTeamId" != 0 THEN
			"fromTeamId"
		ELSE
			0
		END AS from_team,
		CASE WHEN lower("type") IN ('free transfer', 'free agent')
			AND "next_start_date" <= "endDate" + '30 days'::interval
			AND "toTeamId" = 0
			AND "fromTeamId" != 0 THEN
			next_team_id
		WHEN "toTeamId" != 0 THEN
			"toTeamId"
		ELSE
			0
		END AS to_team
	FROM
		transfer_df)
UPDATE
	transfers
SET
	from_team_without_gap = tdf.from_team,
	to_team_without_gap = tdf.to_team
FROM
	tdf
WHERE
	tdf."playerId" = transfers."playerId"
	AND tdf."startDate" = transfers."startDate"
	AND tdf."fromTeamId" = transfers."fromTeamId"
	AND tdf."toTeamId" = transfers."toTeamId";

