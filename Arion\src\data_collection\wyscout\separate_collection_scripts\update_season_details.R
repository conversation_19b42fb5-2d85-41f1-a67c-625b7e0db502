source('src/data_collection/wyscout/00_libs.R')

try({dbDisconnect(con)})
con = make_connection()


competitions = dbReadTable(con, 'competitions')
comp_seasons= dbReadTable(con, 'competition_seasons')
unique_seasons = as.numeric(unique(comp_seasons$seasonId))

seasons = NULL
for(i in 1:length(unique_seasons)){
  Sys.sleep(0.1)
  resp = GET(
    paste0(
      'https://apirest.wyscout.com/v2/seasons/',
      unique_seasons[i],
      '?details=competition'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  
  #try({
  cont = content(resp, as = 'text', encoding = 'UTF-8')
  cont = fromJSON(cont)
  
  if('competition' %in% names(cont)){
    season_comp = cont$competition
    tmp_season = cont[setdiff(names(cont), c('competition'))]
    tmp_season = remove_null(tmp_season)
    season_df = data.frame(tmp_season, stringsAsFactors = F)
    colnames(season_df) = gsub('wyId', 'Id', colnames(season_df), fixed = T)
    season_df$startDate = as.Date(season_df$startDate, format = "%Y-%m-%d")
    season_df$endDate = as.Date(season_df$endDate, format = "%Y-%m-%d")
    colnames(season_df) = paste0('season', colnames(season_df))
    
    if(!is.null(season_comp)){
      season_comp = lapply(season_comp, remove_null)
      season_comp = data.frame(season_comp, stringsAsFactors = F)
      colnames(season_comp) = gsub('.', '_', colnames(season_comp), fixed = T)
      colnames(season_comp) = paste('competition', colnames(season_comp), sep = '_')
      season_comp = season_comp[setdiff(colnames(season_comp), 'competition_wyId')]
      
      season_df = cbind(season_df, season_comp)
    }
    
    seasons = plyr::rbind.fill(seasons, season_df)
  }
  
  #})
  
}

try({dbDisconnect(con)})
con = make_connection()
dbWriteTable(con, 'seasons', seasons, overwrite = T, row.names = F)

dbDisconnect(con)

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'seasons',tbl_qry))
}

dbDisconnect(con)