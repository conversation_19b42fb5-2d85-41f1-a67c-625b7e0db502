with competition_filter as (
	select competitions."competitionId",
		area_name,
		"divisionLevel",
		"name"
	from wyscout.competitions competitions
	where 1 = 1
		and competitions."format" = 'Domestic league'
		and area_name in (
			'England',
			'Germany',
			'France',
			'Spain',
			'Italy',
			'Portugal',
			'Denmark',
			'Sweden',
			'Netherlands',
			'Turkey',
			'Bulgaria',
			'Belgium',
			'Switzerland'
		)
		and gender = 'male'
		and "divisionLevel" = 1
		and "type" = 'club'
		and "category" = 'default' --		and competitions.area_alpha2code = 'EN'
		--	limit 1
),
team_rating as (
	select rating."matchId",
		matches."competitionId",
		matches."date",
		rating."teamId",
		rating.side,
		lag(rating.rating, 1) over (
			partition by "teamId"
			order by rating."matchId"
		) rating,
		lag(rating.smoothed_rating, 1) over (
			partition by "teamId"
			order by rating."matchId"
		) smoothed_rating
	from derived_tables.team_match_rating rating
		left join wyscout.matches matches on rating."matchId" = matches."matchId"
	where 1 = 1 --		
		and matches."competitionId" in (
			select "competitionId"
			from competition_filter
		) -- Filter after feature engineering bc of teams moving up down divisions
),
team_rating_select as (
	select team_1."matchId",
		team_1."competitionId" as "competitionId",
		team_1."date",
		team_1."teamId" as "teamId",
		team_1."rating" as rating,
		team_1."smoothed_rating" as smoothed_rating,
		team_2."teamId" as "opponentId",
		team_2."rating" as opponent_rating,
		team_2."smoothed_rating" as opponent_smoothed_rating
	from (
			select *
			from team_rating
			where side = 'home'
		) team_1
		left join (
			select *
			from team_rating
			where side = 'away'
		) team_2 on team_1."matchId" = team_2."matchId"
		and team_1.side != team_2.side
	where 1 = 1
)

select * from
(select distinct on("matchId") *
from team_rating_select
where rating is not null
	and opponent_rating is not null) iq
order by "date" desc