from src.data_collection.new_scraping.orchestrators.orchestrator import (
    Orchestrator,
)
from src.data_collection.new_scraping.scraping.father_scraper import Scraper
import asyncio
from src.helper_funcs import fast_write_sql, fast_read_sql

# Orchestrator of the whole process
# He will handle the incoming data from the scrapper,
# Validating and preparing data
# Writing to the migration table
# Tracks progress of the already collected and to be collected players


class TMInjOrchestrator(Orchestrator):
    def __init__(
        self, prog_table, raw_data_table, failed_table, engine, scraper: Scraper
    ):
        # Tables
        self.failed_table = failed_table
        self.raw_table = raw_data_table
        super().__init__(prog_table, engine, scraper)

    # Get the IDs of the data you want to scrape for example: Teams, Players etc.
    def get_urls_from_progress(self, limit, url):
        query = (
            f'SELECT {url}, "playerId" FROM'
            f" {self.dbschema}.{self.prog_table} LIMIT {limit}"
        )

        self.tt = fast_read_sql(query, self.engine)
        self.urls = self.tt[url].tolist()
        self.ids = [str(i) for i in self.tt["playerId"].tolist()]
        # Select the players IDs and URLs

    # Scrape the data
    def get_df(self):
        # Scrape the players data
        (self.injuries_df, self.error_ids) = asyncio.run(
            self.scraper.loop_through_urls(self.urls, self.ids)
        )  # players_ids

    # Save to raw table

    # Check progress - Save the new data to migration table and
    def save_scraped_data(self):
        # Save in the raw table
        connection = self.engine.raw_connection()
        cursor = connection.cursor()
        try:
            if len(self.injuries_df) > 0:
                fast_write_sql(
                    self.injuries_df,
                    self.raw_table,
                    cnx=self.engine,
                    if_exists="append",
                    schema=self.dbschema,
                    transaction=True,
                    connection=connection,
                    cursor=cursor,
                )
            if len(self.error_ids) > 0:
                fast_write_sql(
                    self.error_ids,
                    self.failed_table,
                    cnx=self.engine,
                    if_exists="append",
                    schema=self.dbschema,
                    transaction=True,
                    connection=connection,
                    cursor=cursor,
                )

            connection.commit()
        except Exception as e:
            raise (e)
        cursor.close()

    #
    # Remove the already scraped IDs from the progress table
    def update_progress(
        self, prog_id, tm_prog_id, migr_id, fail_id
    ):  # Can be a SQL task
        # Remove the players in progress table based on the players in the migration table
        query = f"""DELETE FROM {self.dbschema}.{self.prog_table} USING {self.dbschema}.{self.raw_table}
         WHERE {self.dbschema}.{self.prog_table}.{prog_id} = CAST({self.dbschema}.{self.raw_table}.{migr_id} AS int)"""

        query_2 = f"""DELETE FROM {self.dbschema}.{self.prog_table} USING {self.dbschema}.{self.failed_table} WHERE
         {self.dbschema}.{self.prog_table}.{tm_prog_id} = CAST({self.dbschema}.{self.failed_table}.{fail_id} AS int)"""

        self.engine.execute(query)
        self.engine.execute(query_2)
