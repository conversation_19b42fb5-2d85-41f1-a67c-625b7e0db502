from datetime import datetime
import pandas as pd

from src.data_collection.wyscout.v2.Updates<PERSON>hecker import <PERSON><PERSON><PERSON>he<PERSON>
from src.helper_funcs import get_wyscout_response, fast_read_sql


class MatchesUpdatesChecker(UpdatesChecker):
    def __init__(
        self,
        table_name: str,
        object_type: str,
        id_name: str,
        from_scratch: bool = False,
        empty_payload: str = "true",
        all_objects_table: str = None,
        only_active: bool = False,
        all_objects_schema: str = "wyscout",
        custom_ids_list: list = None,
    ):
        super().__init__(
            table_name=table_name,
            object_type=object_type,
            id_name=id_name,
            from_scratch=from_scratch,
            empty_payload=empty_payload,
            all_objects_table=all_objects_table,
            only_active=only_active,
            all_objects_schema=all_objects_schema,
            custom_ids_list=custom_ids_list,
        )
        """This class is necessiated by the fact that there are two changed objects endpoints
        related to games: matches and matchevents. To avoid any issues caused by wyscout
        fuckery, we bundle all the changed ids from both endpoints and updated all objects 
        for each id. Therefore we need get_changed_objects and prep_changed_objects that 
        facilitate querying two endpoints return their distinct responses 
        """

    def get_changed_objects(self):
        """
        Gets response from matches and matchevents endpoints and combine in one dict plus games
        that we know from the matches endpoint that have data (i.e. hasDataAvailable = true) but we are missing those
        so we go ahead and collect those as well
        """
        missed_games_w_data_q = """select m."matchId" from 
                            wyscout.matches m 
                            left join (select "matchId" from wyscout.events) ev 
                            using ("matchId") 
                            where m."hasDataAvailable" = true 
                                and ev."matchId" is null 
                            union 
                            select m."matchId" from 
                            wyscout.fixtures m 
                            left join (select "matchId" from wyscout.events) ev 
                            using ("matchId") 
                            where m."hasDataAvailable" = 1 
                                and ev."matchId" is null  """
        missed_games_w_data = fast_read_sql(
            missed_games_w_data_q, self.cnx_prod
        )["matchId"].tolist()
        self.resp = {
            "missed_games_w_data": {x: {} for x in missed_games_w_data}
        }
        for obj_type in ("matches", "matchevents"):
            params = {
                "updated_since": self.last_updated,
                "emptyPayload": self.empty_payload,
                "type": obj_type,
            }

            self.resp.update(get_wyscout_response(self.base_url, params))
        self.last_updated_new = datetime.now(self.wyscout_tz).replace(
            tzinfo=None
        )

    def prep_changed_objects(self):
        """
        Converts it into a table for writing to db
        """
        self.updated_ids = pd.DataFrame()
        for obj_type in ("matches", "matchevents", "missed_games_w_data"):
            self.updated_ids = self.updated_ids.append(
                pd.DataFrame(
                    [x for x in self.resp[obj_type]],
                    columns=[self.id_name],
                )
            )
        self.updated_ids[self.id_name] = self.updated_ids[self.id_name].astype(
            int
        )
