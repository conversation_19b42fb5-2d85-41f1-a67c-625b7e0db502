from datetime import datetime
from bs4 import BeautifulSoup
import pandas as pd
import aiohttp
import asyncio
from src.data_collection.new_scraping.scraping.oddsportal_scraper import (
    ScrapeOddsPortal,
)

class ScrapeUpcomingOdds(ScrapeOddsPortal):

    async def loop_through_urls(self, league_url) -> 'list[pd.DataFrame]':

        # Request to get all the teams in the league
        text = self.get_response(league_url)
        soup = BeautifulSoup(text, "html.parser")

        # Getting the ids, names of teams and dates for matches
        match_ids = []
        date = ""
        for tr in soup.find("table", attrs={"id": "tournamentTable"}).find_all("tr"):
            try:
                span = tr.find("span", attrs={"class": "datet"})
                if span is None:
                    raise Exception
                int_timestamp = int(span["class"][1].split("-")[0].split("t")[-1])
                date = datetime.fromtimestamp(
                    int_timestamp
                )
                if self.timestamp_now < int_timestamp:
                    break
            except Exception as e:
                try:
                    a = tr.find("td", attrs={"class": "table-participant"}).findAll("a")[-1]
                    id = a["href"].split("/")[-2].split("-")[-1]  # Home, Away, match_id
                    home, away = a.text.split(" - ")

                    match_ids.append(
                        (
                            [date, f"https://www.oddsportal.com{a['href']}"],
                            [home, away, id]
                        )
                    )
                except Exception as e:
                    pass

        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for tup in match_ids:
                html_tasks.append(self.extract_data_from_js(tup))
            return await asyncio.gather(*html_tasks)


if __name__ == "__main__":
    pl_odds = ScrapeUpcomingOdds()
    df = asyncio.run(
        pl_odds.loop_through_urls(
            "https://www.oddsportal.com/soccer/england/premier-league/"
        )
    )
