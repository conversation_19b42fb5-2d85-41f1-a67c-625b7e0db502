# # Create Database Structure

source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)

con = make_connection()
areas = dbReadTable(con, 'competition_areas')

competitions = NULL
areas2 = areas$alpha3code
for (area in areas2) {
  Sys.sleep(0.1)
  resp = GET(
    paste0(
      "https://apirest.wyscout.com/v2/competitions?areaId=",
      area
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  cont = content(resp)
  leagues = plyr::ldply(cont$competitions, data.frame, stringsAsFactors = F)
  competitions = rbind(competitions, leagues)
}
colnames(competitions) = gsub('wyId', 'competitionId', colnames(competitions))
colnames(competitions) = gsub('.', '_', colnames(competitions), fixed = T)

if(nrow(competitions) > 0){
  RPostgreSQL::dbWriteTable(
    con,
    'competitions',
    competitions,
    overwrite = T,
    append = F,
    row.names = F,
    rownames = F
  )
}

dbDisconnect(con)

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'compeitions',tbl_qry))
}

dbDisconnect(con)