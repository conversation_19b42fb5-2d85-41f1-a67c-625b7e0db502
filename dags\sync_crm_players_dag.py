from datetime import datetime, timedelta
import time
from airflow import DAG
from airflow.operators.bash_operator import BashOperator


from dag_settings import workdir


dag_params = {
    "dag_id": "sync_crm_players",
    "start_date": datetime(2021, 7, 29, 8),
    "schedule_interval": timedelta(days=7),
    "params": {"workdir": workdir,},
    "dagrun_timeout": timedelta(minutes=480),
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 1,
        "retry_delay": timedelta(minutes=5),
    },
}
with DAG(**dag_params) as dag:

    sync_players = BashOperator(
        task_id="sync_players",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/crm_refresh/sync_players.py """,
    )

    sync_players