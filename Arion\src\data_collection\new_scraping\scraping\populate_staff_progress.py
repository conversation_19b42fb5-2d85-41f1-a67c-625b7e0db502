import pandas as pd
from settings import postgres_prod_str
from sqlalchemy import create_engine

def populate_progress():

    dd = {
        "staff_url": [
            f"https://www.transfermarkt.com/metodi-tomanov/profil/trainer/{i}"
            for i in range(2, 1001)
        ]
    }

    # From 0 to 118000 staff
    for i in range(1, 117):
        for n in range(1, 1001):
            dd["staff_url"].append(
                f"https://www.transfermarkt.com/metodi-tomanov/profil/trainer/{i*1000+n}"
            )
    df = pd.DataFrame(dd)
    cnx = create_engine(postgres_prod_str)
    df.to_sql("staff_progress_table", cnx, schema="meta_scraping",
     if_exists="replace", chunksize=1000, method='multi', index=False)


if __name__ == "__main__":
    populate_progress()
