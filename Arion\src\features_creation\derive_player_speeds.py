
from datetime import datetime

import pandas as pd
import numpy as np
from sqlalchemy import create_engine

from settings import postgres_prod_str, postgres_dev_str

class SpeedDeriver:
    def __init__(self, cnx_prod, cnx_dev, accelerations_table, processed_match_ids_table):
        self.cnx_prod = cnx_prod
        self.cnx_dev = cnx_dev
        self.games_for_processing = None
        self.batches = None
        self.raw_events_df = None
        self.df = None
        self.games_df = None
        self.grouped_df = None
        self.accelerations_table_name = accelerations_table
        self.processed_match_ids_table = processed_match_ids_table
        self.processed_games_series = None

    def read_processed_match_ids(self):
        # read already processed games from production directly, we will not be processing those
        self.processed_games_series = pd.read_sql(f'''SELECT * FROM {self.processed_match_ids_table}''', 
                                            self.cnx_prod)['matchId']

    def get_matches_for_processing(self):
        # read all available games so that we can batch
        # get date as well so that we later join and add to table
        games_query = f'''SELECT DISTINCT "matchId", "date" from advanced_stats_total
                        WHERE "date" IS NOT NULL'''
        self.games_df = pd.read_sql(games_query, self.cnx_prod)
        # remove games we have already processed:
        self.games_for_processing = self.games_df[~self.games_df['matchId'].\
            isin(self.processed_games_series)]['matchId'].tolist()
    
    def get_batches(self, batch_size):
        # batch games through getting quantiles of matchId, 
        # so that we have equal number of games in each batch and also avoid IN sql clause
        self.batches = [tuple(self.games_for_processing[i: i + batch_size]) for i
         in range(0, len(self.games_for_processing), batch_size)]

    def get_raw_events(self, batch_ids):
        # get all the columns of the respective batch:
        events_query = f'''SELECT  "playerId",
                                  "matchId", 
                                  "x_start", 
                                  "y_start", 
                                  "x_end", 
                                  "y_end", 
                                  "matchPeriod",
                                  "eventSec", 
                                  "subEventName"  
                        FROM events 
                        WHERE
                            "matchId" IN {batch_ids} 
 
                        ORDER BY "matchId", "matchPeriod", "eventSec" '''
        self.raw_events_df = pd.read_sql(events_query, self.cnx_dev)

    def prep_events(self):

        self.df = self.raw_events_df.copy() 
        # get indexes of accelerations, select also previous and next row:
        acc_indexes = list(
            self.df[(self.df.subEventName == "Acceleration") & \
            (~self.df.isnull().any(axis=1))].index
            )
        relevant_indeces = acc_indexes + \
            [x + 1 for x in acc_indexes] +\
                [x - 1 for x in acc_indexes]

        # selecting only accelerations, before and after events:
        self.df = self.df[self.df.index.isin(relevant_indeces)]
        # getting start of the next event to mark end of current one:
        self.df['end_sec'] = self.df['eventSec'].shift(-1)
        # getting preceding and following event of accelerations:
        self.df['next_event'] = np.where(self.df['subEventName'] == "Acceleration", 
                self.df['subEventName'].shift(-1), np.nan)
        self.df['prev_event'] = np.where(self.df['subEventName'] == "Acceleration", 
             self.df['subEventName'].shift(1), np.nan)
        # dropping events that are not accelerations:
        self.df = self.df[self.df['subEventName'] == "Acceleration"]
        #calculating duration of the acceleration 
        self.df['duration'] = self.df['end_sec'] - self.df["eventSec"]
        # Euclidian distance, multiplying x*0.9 and y*0.45 to convert from % to meters:
        self.df['distance'] = ((0.9*(self.df.x_end - self.df.x_start))**2 + \
            (0.45*(self.df.y_end - self.df.y_start))**2)**0.5
        # divide distance over duration and multiply by 3.6 to convert from m/se to km/h,
        # calculating only for accelerations:
        self.df['speed'] = np.where(self.df['subEventName'] == "Acceleration",
            (self.df['distance']/self.df['duration'])*3.6, np.nan)

        # merging player and match data
        self.df = pd.merge(self.df, self.games_df, on="matchId", how="left")

    def export_speeds(self):
        # write prepped table to sql 
        self.df.to_sql(self.accelerations_table_name, self.cnx_dev, if_exists='append', index=False)

    def log_match_ids(self, batch_i):
        # select ids of games that were processed in the batch and append them to medata table
        newly_processed_games = pd.DataFrame(batch_i, columns=["matchId"])
        # write processed matches to prod directly
        newly_processed_games.to_sql(self.processed_match_ids_table, self.cnx_prod, if_exists='append', index=False)

    def batch_speeds(self):
        # run the batches: 
        for ind, i in enumerate(self.batches):
            self.get_raw_events(i)
            print(datetime.now(), f"Batch {ind + 1} events collected")
            self.prep_events()
            print(datetime.now(), f"Batch {ind + 1} events prepped")
            self.export_speeds()
            print(datetime.now(), f"Batch {ind + 1} speeds saved")
            self.log_match_ids(i)
            print(datetime.now(), f"Batch {ind + 1} processed match_ids logged in db")

def main():
    cnx_prod = create_engine(postgres_prod_str)
    cnx_dev = create_engine(postgres_dev_str)
    speed = SpeedDeriver(cnx_prod, cnx_dev, 'player_speeds', 'parsed_speed_match_ids')
    try:
        speed.read_processed_match_ids()
    except:
        speed.processed_games_series = pd.Series()
    speed.get_matches_for_processing()
    target_batch = 2000 # arbitrary number based on playing around with the queries and their index usage, 
    # may need to change as we go 
    print("Distinct match_ids collected")
    speed.get_batches(target_batch) 

    speed.batch_speeds()

if __name__ == "__main__":
    main()

    

