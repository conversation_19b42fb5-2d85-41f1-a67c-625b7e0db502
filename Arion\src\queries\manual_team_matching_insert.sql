delete from tm_to_ws_team_ids
where transfermarkt_team_id in (
        select distinct transfermarkt_team_id
        from manually_matched_teams
    );
insert into tm_to_ws_team_ids
select *,
    'manual_matching' as matched_through
from (
        select *
        from (
                select distinct on (transfermarkt_team_id) *,
                    minimum_string_dist::float / least(
                        char_length(transfermark_team_name_full),
                        char_length(transfermark_team_name_alt)
                    ) as portion_of_name
                from (
                        select tt.joined_id as transfermarkt_team_id,
                            tt.joined_name as transfermark_team_name_full,
                            tt.joined_alt as transfermark_team_name_alt,
                            t2."teamId",
                            t2."officialName",
                            t2.name as team_name,
                            coalesce(
                                (
                                    select regexp_matches(
                                            (
                                                array_remove(
                                                    (
                                                        select regexp_matches(
                                                                lower("joined_name"),
                                                                '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})'
                                                            )
                                                    ),
                                                    null
                                                )
                                            ) [1],
                                            '([\d]{2,2})'
                                        )
                                ) [1]::int,
                                0
                            ) under_tm_team,
                            coalesce(
                                (
                                    select regexp_matches(
                                            (
                                                array_remove(
                                                    (
                                                        select regexp_matches(
                                                                lower("officialName"),
                                                                '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})'
                                                            )
                                                    ),
                                                    null
                                                )
                                            ) [1],
                                            '([\d]{2,2})'
                                        )
                                ) [1]::int,
                                0
                            ) under_ws_team,
                            least(
                                levenshtein(joined_name, "officialName", 1, 1, 1),
                                levenshtein(joined_alt, "officialName", 1, 1, 1)
                            ) as string_dist_official,
                            least(
                                levenshtein(joined_name, "name", 1, 1, 1),
                                levenshtein(joined_alt, "name", 1, 1, 1)
                            ) as string_dist,
                            case
                                when least(
                                    levenshtein(joined_name, "officialName", 1, 1, 1),
                                    levenshtein(joined_alt, "officialName", 1, 1, 1)
                                ) < least(
                                    levenshtein(joined_name, "name", 1, 1, 1),
                                    levenshtein(joined_alt, "name", 1, 1, 1)
                                ) then least(
                                    levenshtein(joined_name, "officialName", 1, 1, 1),
                                    levenshtein(joined_alt, "officialName", 1, 1, 1)
                                )
                                else least(
                                    levenshtein(joined_name, "name", 1, 1, 1),
                                    levenshtein(joined_alt, "name", 1, 1, 1)
                                )
                            end as minimum_string_dist
                        from manually_matched_teams ll,
                            (
                                select distinct joined_id,
                                    joined_name,
                                    joined_alt
                                from tm_transfers
                            ) tt,
                            teams t2
                        where ll.transfermarkt_team_id = tt.joined_id
                            and t2."teamId" = ll."teamId"
                    ) joined_q
                order by transfermarkt_team_id,
                    minimum_string_dist
            ) a
        union
        select *
        from (
                select distinct on (transfermarkt_team_id) *,
                    minimum_string_dist::float / least(
                        char_length(transfermark_team_name_full),
                        char_length(transfermark_team_name_alt)
                    ) as portion_of_name
                from (
                        select tt.left_id as transfermarkt_team_id,
                            tt.left_name as transfermark_team_name_full,
                            tt.left_alt as transfermark_team_name_alt,
                            t2."teamId",
                            t2."officialName",
                            t2.name as team_name,
                            coalesce(
                                (
                                    select regexp_matches(
                                            (
                                                array_remove(
                                                    (
                                                        select regexp_matches(
                                                                lower("left_name"),
                                                                '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})'
                                                            )
                                                    ),
                                                    null
                                                )
                                            ) [1],
                                            '([\d]{2,2})'
                                        )
                                ) [1]::int,
                                0
                            ) under_tm_team,
                            coalesce(
                                (
                                    select regexp_matches(
                                            (
                                                array_remove(
                                                    (
                                                        select regexp_matches(
                                                                lower("officialName"),
                                                                '(u[\d]{2,2})|(nder[[:space:]][\d]{2,2})|(under[\d]{2,2})'
                                                            )
                                                    ),
                                                    null
                                                )
                                            ) [1],
                                            '([\d]{2,2})'
                                        )
                                ) [1]::int,
                                0
                            ) under_ws_team,
                            least(
                                levenshtein(left_name, "officialName", 1, 1, 1),
                                levenshtein(left_alt, "officialName", 1, 1, 1)
                            ) as string_dist_official,
                            least(
                                levenshtein(left_name, "name", 1, 1, 1),
                                levenshtein(left_alt, "name", 1, 1, 1)
                            ) as string_dist,
                            case
                                when least(
                                    levenshtein(left_name, "officialName", 1, 1, 1),
                                    levenshtein(left_alt, "officialName", 1, 1, 1)
                                ) < least(
                                    levenshtein(left_name, "name", 1, 1, 1),
                                    levenshtein(left_alt, "name", 1, 1, 1)
                                ) then least(
                                    levenshtein(left_name, "officialName", 1, 1, 1),
                                    levenshtein(left_alt, "officialName", 1, 1, 1)
                                )
                                else least(
                                    levenshtein(left_name, "name", 1, 1, 1),
                                    levenshtein(left_alt, "name", 1, 1, 1)
                                )
                            end as minimum_string_dist
                        from manually_matched_teams ll,
                            (
                                select distinct left_id,
                                    left_name,
                                    left_alt
                                from tm_transfers
                            ) tt,
                            teams t2
                        where ll.transfermarkt_team_id = tt.left_id
                            and t2."teamId" = ll."teamId"
                    ) left_q
                order by transfermarkt_team_id,
                    minimum_string_dist
            ) b
    ) c