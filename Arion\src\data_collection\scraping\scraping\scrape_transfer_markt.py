import re
import requests
from time import sleep, time
import pickle
from bs4 import BeautifulSoup
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from settings import PROXIES
from src.helper_funcs import read_config

BASE = "https://www.transfermarkt.com"
scrape_config = read_config()["scraping"]["transfermarkt"]


def get_soup_from_url(
    url, session=None, header=True, proxies=None, payload=None, method="get"
):
    if proxies is not None:
        proxies = {"https": PROXIES}
    if session is not None:
        req = session.get(url, proxies=proxies)
        text = req.text
    else:
        if header:
            header = {
                "User-Agent": (
                    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML,"
                    " like Gecko) Chrome/49.0.2623.112 Safari/537.36"
                )
            }
            if method == "get":
                req = requests.get(
                    url, headers=header, proxies=proxies, data=payload
                )
            elif method == "post":
                req = requests.post(
                    url, headers=header, proxies=proxies, data=payload
                )
            else:
                raise ValueError("Invalid method type")
            text = req.text
    if req.status_code >= 400:
        print(url)
        return
    soup = BeautifulSoup(text, "html.parser")
    return soup


def sleep_random(sleep_range, verbose=False):
    sleep_time = np.random.randint(sleep_range[0], sleep_range[1])
    if verbose:
        print(sleep_time)
    sleep(sleep_time)


@retry(
    wait=wait_random(
        min=scrape_config["sleep"]["on_fail"][0],
        max=scrape_config["sleep"]["on_fail"][1],
    ),
    stop=stop_after_attempt(5),
)
def scrape_league_list(continent):
    if (
        continent != "afrika"
    ):  # afrika currently has 1 page so it does not have 'last page' button to infer n_pages
        # we start from page 1 and check how many pages there are
        url_init = f"{BASE}/wettbewerbe/{continent}?ajax=yw1&page=1"
        init_soup = get_soup_from_url(url_init, proxies=PROXIES)
        pattern = r"\d+"
        n_pages = int(
            re.search(
                pattern,
                init_soup.find(attrs={"class": "letzte-seite"})["title"],
            ).group()
        )
    else:
        n_pages = 1
    league_urls_dict = {
        "league_name": [],
        "league_code": [],
        "league_url": [],
    }  #'league_country':[]}

    for i in range(1, n_pages + 1):
        temp_soup = None
        url_i = f"{BASE}/wettbewerbe/{continent}?ajax=yw1&page={i}"
        temp_soup = get_soup_from_url(url_i, proxies=PROXIES)
        sleep_random((5, 10))
        print(f"{i} page(s) done")
        # return none if soup is None
        if temp_soup:
            for league in temp_soup.find_all(attrs={"class": "inline-table"}):
                league_url = BASE + league.find_all("a")[1]["href"]
                if (
                    league_url.split("/")[-1]
                    not in league_urls_dict["league_code"]
                ):
                    league_urls_dict["league_url"].append(league_url)
                    league_urls_dict["league_code"].append(
                        league_url.split("/")[-1]
                    )
                    league_urls_dict["league_name"].append(
                        league.find_all("a")[1].text
                    )
                    # league_urls_dict['league_country'].append(
                    #         league.find(attrs={'class': 'miniflagge'})['title'])

        league_urls_df = pd.DataFrame(league_urls_dict)
        league_urls_df["continent"] = continent
        return league_urls_df


@retry(
    wait=wait_random(
        min=scrape_config["sleep"]["on_fail"][0],
        max=scrape_config["sleep"]["on_fail"][1],
    ),
    stop=stop_after_attempt(5),
)
def scrape_team_list(league_url):
    team_urls_dict = {"team_name": [], "team_id": [], "team_url": []}
    temp_soup = None
    # url_l = f'https://www.transfermarkt.com/ekstraklasa/startseite/wettbewerb/{league_code}'
    temp_soup = get_soup_from_url(league_url, proxies=PROXIES)
    # print(f"{league_url.split('/')[-1]} league loaded")
    # return none if soup is None
    if temp_soup:
        # for bug testing to see if cup or championship
        print(temp_soup.find("table").td.text)
        # although they have the same info box, cups pages have different html structure this check  checks whether we
        # are looking at league, playoffs, or cups - cups we do not take as we cannot assign lower-tier teams to a
        # league and we also risk overwriting some of the squads previously scraped from upper leagues
        if (
            any(
                x in temp_soup.find("table").td.text.lower()
                for x in ["cup", "play-off"]
            )
            or temp_soup.find(attrs={"class": "dataValue"}) is not None
        ):
            return None
        for team in temp_soup.find(
            attrs={"class": "responsive-table"}
        ).find_all(attrs={"class": "vereinprofil_tooltip"}):
            team_name = team.text
            if len(team_name) > 0 and team_name not in team_urls_dict:
                team_urls_dict["team_name"].append(team_name)
                team_urls_dict["team_id"].append(team["id"])
                team_urls_dict["team_url"].append(BASE + team["href"])

        team_urls_df = pd.DataFrame(team_urls_dict)
        team_urls_df["league"] = league_url.split("/")[-1]
        return team_urls_df


@retry(
    wait=wait_random(
        min=scrape_config["sleep"]["on_fail"][0],
        max=scrape_config["sleep"]["on_fail"][1],
    ),
    stop=stop_after_attempt(5),
)
def scrape_players(team_url):
    player_dict = {
        "player_name": [],
        "player_position": [],
        "player_id": [],
        "nationality": [],
        "player_birth_date": [],
        "current_market_value": [],
        "previous_market_value": [],
        "player_url": [],
    }
    temp_soup = None
    temp_soup = get_soup_from_url(team_url, proxies=PROXIES)
    # print(f"{team_url.split('/')[-3]} team loaded")
    if temp_soup:
        all_rows = ["odd", "even"]
        for row in all_rows:
            if len(temp_soup.find_all(attrs={"class": row})) > 0:
                for player in temp_soup.find_all(attrs={"class": row}):
                    try:
                        player_dict["player_name"].append(
                            player.find(attrs={"class": "spielprofil_tooltip"})[
                                "title"
                            ]
                        )
                    except:
                        player_dict["player_name"].append(np.nan)
                    try:
                        player_dict["player_id"].append(
                            player.find(attrs={"class": "spielprofil_tooltip"})[
                                "id"
                            ]
                        )
                    except:
                        player_dict["player_id"].append(np.nan)
                    zentriert_len = len(
                        player.find_all(attrs={"class": "zentriert"})
                    )
                    try:
                        player_dict["player_birth_date"].append(
                            player.find_all(attrs={"class": "zentriert"})[
                                zentriert_len - 2
                            ].text
                        )
                    except:
                        player_dict["player_birth_date"].append(np.nan)
                    try:
                        player_dict["nationality"].append(
                            player.find_all(attrs={"class": "zentriert"})[
                                zentriert_len - 1
                            ].find("img")["title"]
                        )
                    except:
                        player_dict["nationality"].append(np.nan)
                    try:
                        player_dict["player_position"].append(
                            player.td["title"]
                        )
                    except:
                        player_dict["player_position"].append(np.nan)
                    try:
                        player_dict["current_market_value"].append(
                            player.find(
                                attrs={"class": "rechts hauptlink"}
                            ).text.strip()
                        )
                    except:
                        player_dict["current_market_value"].append(np.nan)
                    try:
                        player_dict["previous_market_value"].append(
                            player.find(attrs={"class": "rechts hauptlink"})
                            .span["title"]
                            .split(":")[1]
                        )
                    except:
                        player_dict["previous_market_value"].append(np.nan)
                    try:
                        player_dict["player_url"].append(
                            BASE
                            + player.find_all(
                                "a", attrs={"class": "spielprofil_tooltip"}
                            )[0]["href"]
                        )
                    except:
                        player_dict["player_url"].append(np.nan)
        player_df = pd.DataFrame(player_dict)
        player_df["team"] = team_url.split("/")[-3]
        return player_df


class ProgressTrackerManager:
    def __init__(self, progress_tracker_path):
        self.progress_tracker_path = progress_tracker_path
        self.progress_tracker = None

    def load_tracker(self):
        if self.progress_tracker is None:
            try:
                with open(self.progress_tracker_path, "rb") as track_in:
                    self.progress_tracker = pickle.load(track_in)
            except:
                self.progress_tracker = {
                    "continents": [],
                    "leagues": [],
                    "teams": [],
                }

    def save_tracker(self):
        if self.progress_tracker is not None:
            try:
                with open(self.progress_tracker_path, "wb") as track_out:
                    self.progress_tracker = pickle.dump(
                        self.progress_tracker, track_out
                    )
            except Exception as e:
                print(e)


def main():
    start = time()
    cf = read_config("config.yml")
    config = cf["scraping"]["directories"]

    progress_track = ProgressTrackerManager(
        cf["scraping"]["directories"]["progress_trackers"]["transfermakt"]
    )
    progress_track.load_tracker()
    teams_league_df, leagues_continent_df, players_team_df = None, None, None
    try:
        # continents:
        continent_list = read_config("config.yml")["scraping"]["transfermarkt"][
            "continents"
        ]
        continents_to_scrape = [
            cont
            for cont in continent_list
            if cont not in progress_track.progress_tracker["continents"]
        ]
        for continent in continents_to_scrape:
            leagues_continent_df = scrape_league_list(continent=continent)
            if leagues_continent_df is not None:
                leagues_continent_df.to_csv(
                    f"{config['scraped_continents']}/{continent}_leagues_list.csv"
                )
            sleep_random(scrape_config["sleep"]["on_new_continent"])

            # leagues:
            leagues_to_scrape = [
                lg
                for lg in leagues_continent_df["league_url"].unique()
                if lg not in progress_track.progress_tracker["leagues"]
            ]
            for league_url in leagues_to_scrape:
                sleep_random(scrape_config["sleep"]["on_new_league"])
                teams_league_df = scrape_team_list(league_url=league_url)
                if teams_league_df is not None:
                    teams_league_df.to_csv(
                        f"{config['scraped_leagues']}/{league_url.split('/')[-1]}_teams_list.csv"
                    )
                    print(
                        f"{league_url.split('/')[-1]} list of teams scraped,"
                        f" {time() - start} seconds elapsed"
                    )
                # teams:
                if teams_league_df is not None:
                    teams_to_scrape = [
                        tm
                        for tm in teams_league_df["team_url"].unique()
                        if tm not in progress_track.progress_tracker["teams"]
                    ]
                    for team_url in teams_to_scrape:
                        sleep_random(scrape_config["sleep"]["on_new_team"])
                        players_team_df = scrape_players(team_url=team_url)
                        players_team_df.to_csv(
                            f"{config['scraped_teams']}/{team_url.split('/')[-3]}_players_list.csv"
                        )
                        progress_track.progress_tracker["teams"].append(
                            team_url
                        )
                        # print(
                        #     f"{team_url.split('/')[-3]} list of players scraped, {time() - start} seconds elapsed")
                # adding league as complete after we have looped through all teams
                progress_track.progress_tracker["leagues"].append(league_url)
            # adding continent as complete after we have looped through all leagues
            progress_track.progress_tracker["continents"].append(continent)

    # saving our tracker and then we re-raise
    except Exception as e:
        print(e)
        progress_track.save_tracker()
        raise Exception(e)

    # saving the whole tracker after we are done, in theory it should include everything
    #  doing this just in case, we are archiving and ditching at the end of the airflow pipeline
    progress_track.save_tracker()
    print(f"Total time elapsed: {time() - start} seconds.")


if __name__ == "__main__":
    main()
