import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "refresh_agents_info_dag",
    "start_date": datetime(2022, 1, 8),
    "schedule_interval": timedelta(days=30),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    rescrape_agents_info = BashOperator(
        task_id="rescrape_agents_info",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/rescrape_agents_info.py
                           """,
    )

    check_if_agents_info_is_ready_for_migration = BashOperator(
        task_id="check_if_agents_info_is_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_agents_info_before_migration.py
                           """,
    )

    save_cleaned_agents_info = BashOperator(
        task_id="save_cleaned_agents_info",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_agents_info.py
                           """,
    )


(rescrape_agents_info >> [check_if_agents_info_is_ready_for_migration])

(check_if_agents_info_is_ready_for_migration >> save_cleaned_agents_info)
