import requests
import pandas as pd
from bs4 import BeautifulSoup
from sqlalchemy import create_engine

from settings import postgres_prod_str, PROXIES


print(PROXIES)


def main():
    cnx_prod = create_engine(postgres_prod_str)
    url = "https://www.fifa.com/api/ranking-overview?locale=en&dateId=id13505"

    payload = {}
    headers = {
        "authority": "www.fifa.com",
        "cache-control": "max-age=0",
        "sec-ch-ua": (
            '" Not;A Brand";v="99", "Google Chrome";v="97", "Chromium";v="97"'
        ),
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": '"Android"',
        "upgrade-insecure-requests": "1",
        "user-agent": (
            "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N)"
            " AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Mobile"
            " Safari/537.36"
        ),
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        "sec-fetch-site": "none",
        "sec-fetch-mode": "navigate",
        "sec-fetch-user": "?1",
        "sec-fetch-dest": "document",
        "accept-language": "en-US,en;q=0.9",
        "cookie": (
            "ai_user=ExiyyMaxUcb1nGnZlWZfVm|2022-02-08T21:06:11.735Z;"
            " AMCVS_2F2827E253DAF0E10A490D4E%40AdobeOrg=1;"
            " AMCV_2F2827E253DAF0E10A490D4E%40AdobeOrg=-1124106680%7CMCIDTS%7C19032%7CMCMID%7C56467936768957228800089609151677235452%7CMCAAMLH-1644959173%7C6%7CMCAAMB-1644959173%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1644361573s%7CNONE%7CvVersion%7C5.2.0;"
            " o_ic_persist=; o_ec_persist=; o_sc_persist=; s_cc=true;"
            " OptanonAlertBoxClosed=2022-02-08T21:06:16.016Z;"
            " _cc_id=ad6ade0aa9e5255d2485eb4200aaf575;"
            " o_ot_persist=%2C1%2C2%2C4%2C3%2C;"
            " TEAL=v:317edb2990d58824333338970944686853a7e16da$t:1644356241435$s:1644354441431%3Bexp-sess$sn:1$en:1;"
            " s_sq=%5B%5BB%5D%5D;"
            " OptanonConsent=isGpcEnabled=0&datestamp=Wed+Feb+09+2022+01%3A07%3A22+GMT%2B0400+(Georgia+Standard+Time)&version=6.19.0&isIABGlobal=false&hosts=&consentId=f4fd91a7-b456-473f-8cca-fb822f29aaf5&interactionCount=1&landingPath=NotLandingPage&groups=1%3A1%2C2%3A1%2C4%3A1%2C3%3A1&geolocation=BG%3B&AwaitingReconsent=false;"
            " panoramaId_expiry=1644440844912"
        ),
    }

    resp = requests.request(
        "GET", url, headers=headers, data=payload, proxies={"https": PROXIES}
    )
    rnks = [x["rankingItem"] for x in resp.json()["rankings"]]
    df = pd.DataFrame(rnks)
    df = df[["rank", "name", "totalPoints", "countryCode"]]
    df.columns = ["rank", "country", "total_pts", "country_code"]

    df.to_sql(
        "fifa_rankings",
        cnx_prod,
        if_exists="replace",
        schema="derived_tables",
        index=False,
    )
    cnx_prod.execute('grant all on derived_tables.fifa_rankings to elvan, brexit_service')


if __name__ == "__main__":
    main()
