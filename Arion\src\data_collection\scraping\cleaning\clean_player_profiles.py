import os
from glob import glob
import re
import numpy as np
import pandas as pd
from src.helper_funcs import read_config


def combine_all_scraped_profiles(path):
    # make sure we are concating only time-stamped files
    files_list = [
        x
        for x in glob(os.path.join(path, "*.csv"))
        if "player_details.csv" not in x
    ]
    # python csv read engine to handle so gnarly scraped cases:
    concat_df = pd.concat(
        [
            pd.read_csv(
                df, encoding="utf-16", error_bad_lines=False, engine="python"
            )
            for df in files_list
        ]
    )
    concat_df = concat_df.reset_index(drop=True)
    return concat_df


def conv_str_to_float_col(x):
    pattern = r"\d+[.|,]?\d{0,2}"
    # try:
    value_str = str(x).lower()
    if "m" in value_str and "may" not in value_str and "mar" not in value_str:
        return float(re.search(pattern, x).group().replace(",", ".")) * 10 ** 6
    elif "th" in value_str or "k" in value_str:
        return float(re.search(pattern, x).group().replace(",", ".")) * 10 ** 3
    else:
        try:
            return x if isinstance(x, float) else float(x.replace(",", "."))
        except:
            return np.nan


def conv_height_to_float(x):
    pattern = r"\d,\d{2}"
    try:
        return float(re.search(pattern, x).group().replace(",", ".")) * 100
    except:
        return np.nan


def clean_evil(x):
    try:
        return x.encode("latin-1").decode("utf-8")
    except:
        if isinstance(x, str):
            return (
                x.replace("ÃŸ", "ß")
                .replace("Ã‰", "É")
                .replace("Ã‡", "Ç")
                .replace("Ã–", "Ö")
            )
        return x


def clean_profiles(df):

    tier_dict = {
        "First Tier": 1,
        "Second Tier": 2,
        "Third Tier": 3,
        "Fourth Tier": 4,
        "Fifth Tier": 5,
        "Sixth Tier": 6,
    }

    for col in df.columns:
        if df[col].dtype == "object":
            df[col] = (
                df[col].apply(lambda x: str(x).strip()).replace("nan", np.nan)
            )

    for name_col in ["name", "full_name", "name_in_home_country"]:
        # cleaning latin-1 encoding crap
        df[name_col] = df[name_col].apply(lambda x: clean_evil(x))

    # df['name']  = df['player_url'].apply(lambda x: x.split('/')[3].replace('-', ' '))
    df["league_tier"] = df["league_tier"].map(tier_dict)
    for m_col in ["highest_value", "current_value"]:
        df[m_col] = df[m_col].replace("-", np.nan)
        df[m_col] = df[m_col].apply(conv_str_to_float_col).astype(float)
    df["height"] = df["height"].apply(lambda x: conv_height_to_float(x))
    df["current_club_id"] = df["current_club_id"].fillna(0).astype(int)
    date_cols = [
        "birth_date",
        "joined",
        "last_update",
        "highest_value_date",
        "contract_expiry",
    ]
    # df['contract_expiry'] = pd.to_datetime(df['contract_expiry'],
    #                                     format='%d.%m.%Y', errors='coerce')
    for d_col in date_cols:
        df[d_col] = pd.to_datetime(
            df[d_col], format="%b %d, %Y", errors="coerce"
        )
    return df


def main():
    config = read_config("config.yml")["scraping"]["directories"]
    df = combine_all_scraped_profiles(config["scraped_player_details"])
    df = clean_profiles(df)
    df.to_csv(config["cleaned_player_profiles"], encoding="utf-16")


if __name__ == "__main__":
    main()
