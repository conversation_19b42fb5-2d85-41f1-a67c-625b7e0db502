from abc import abstractmethod, ABC
from typing import List, Optional, Union
import io
from pydantic import BaseModel, root_validator, validator
from enum import Enum

from sqlalchemy import create_engine
import pandas as pd

from settings import postgres_prod_str


class ContentType(Enum):
    plain_text = 0
    body_html = 1
    attachment_file = 2


class ReportContent(BaseModel):
    type: ContentType
    content: Union[str, bytes]
    file_format: Optional[str]

    @validator("file_format")
    def check_file_format(cls, v):
        if v is not None and v not in {"pdf", "xlsx", "csv"}:
            raise ValueError("Invalid format type")
        return v

    @root_validator
    def check_file_has_format(cls, values):
        if (
            values.get("type") == ContentType.attachment_file
            and values.get("file_format") is None
        ):
            raise ValueError(
                "If passing attachment file, file_format must be specified"
            )
        return values


class Reporter(ABC):
    def __init__(self):
        self.content_list: List[ReportContent] = []
        self.cnx_prod = create_engine(postgres_prod_str)

    @staticmethod
    def export_csv(df: pd.DataFrame) -> str:
        with io.StringIO() as buffer:
            df.to_csv(buffer)
            return buffer.getvalue()

    @staticmethod
    def make_html_table(df: pd.DataFrame) -> str:
        return (
            f"<html>  <body> <br><br> {df.to_html(index=False)} </body></html>"
        )

    @abstractmethod
    def get_data(self):
        pass

    @abstractmethod
    def make_report(self) -> List[ReportContent]:
        pass
