import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

# from dag_settings import workdir_potential
workdir_potential = '/home/<USER>/Projects/working_branches/sorare'

sys.path.append(workdir_potential)


dag_params = {
    "dag_id": "create_xgb_data",
    "start_date": datetime(2022, 5, 3, 14),
    "schedule_interval": None,
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_potential,
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    # sorare_experiment1_nowknd = BashOperator(
    #     task_id="create_potential_mv",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name1}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 0
    #                        """,
    # )

    # create_potential_mv = PostgresOperator(
    #     task_id="create_potential_mv",
    #     database="wyscout_raw_production",
    #     sql=open(
    #         "/home/<USER>/Projects/player-potential/src/queries/create_training_mv.sql"
    #     ).read(),
    # )

    create_xgb_df = PostgresOperator(
        task_id="create_xgb_df",
        database="wyscout_raw_production",
        sql=open(
            "/home/<USER>/Projects/working_branches/sorare/src/queries/xgboost_feature_read.sql"
        ).read(),
    )

    # create_train_valid_test_sets = PostgresOperator(
    #     task_id = "split_train_valid_test_sets",
    #     database="wyscout_raw_production",
    #     sql=open('/home/<USER>/Projects/player-potential/src/queries/split_train_validation_test.sql').read()
    # )
    

    # [create_potential_mv, create_potential_tabular_mv] >> create_train_valid_test_sets

    create_xgb_df
    
