drop table if exists derived_tables.shots;
 create table derived_tables.shots as (
    select "matchId",
        --	arr.events, 
        arr.events->'player'->>'id' as "playerId",
        arr.events->'team'->>'id' as "teamId",
        arr.events->'opponentTeam'->>'id' as "opponentId",
        arr.events->'type'->>'primary' as event_type,
        arr.events->'location'->>'x' as "x",
        arr.events->'location'->>'y' as "y",
        arr.events->>'matchPeriod' as match_period,
        arr.events->>'minute' as "minute",
        arr.events->>'second' as "second",
        extract(
            epoch
            from (replace(arr.events->>'matchTimestamp', '-', ''))::time
        ) as "match_timestamp",
        arr.events->>'videoTimestamp' as "video_timestamp",
        arr.events->'shot'->>'bodyPart' as "body_part",
        arr.events->'shot'->>'isGoal' as "is_goal",
        arr.events->'shot'->>'onTarget' as "on_target",
        arr.events->'shot'->>'xg' as "xg",
        arr.events->'shot'->>'postShotXg' as "post_shot_xg",
        arr.events->'shot'->>'goalZone' as "goal_zone",
        arr.events->'shot'->'goalkeeper'->>'id' as "goalkeeper"
    from wyscout.events e,
        json_array_elements(events) with ordinality arr(events, position)
    where arr.events->'type'->>'primary' = 'shot'
)