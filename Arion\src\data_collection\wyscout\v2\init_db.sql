CREATE TABLE "players" (
  "playerId" integer PRIMARY KEY,
  "shortName" text,
  "firstName" text,
  "middleName" text,
  "lastName" text,
  "height" integer,
  "weight" integer,
  "birthDate" text,
  "foot" text,
  "currentTeamId" integer,
  "currentNationalTeamId" integer,
  "gender" text,
  "status" text,
  "birthArea_id" integer,
  "birthArea_alpha2code" text,
  "birthArea_alpha3code" text,
  "birthArea_name" text,
  "passportArea_id" integer,
  "passportArea_alpha2code" text,
  "passportArea_alpha3code" text,
  "passportArea_name" text,
  "role_name" text,
  "role_code2" text,
  "role_code3" text
);

CREATE TABLE "seasons_players" (
  "playerId" integer,
  "seasonId" integer
);

CREATE TABLE "seasons_matches" (
  "seasonId" integer,
  "matchId" integer
);

CREATE TABLE "seasons_teams" (
  "seasonId" integer,
  "teamId" integer
);

CREATE TABLE "seasons" (
  "seasonId" integer PRIMARY KEY,
  "seasonname" text,
  "seasonstartDate" date,
  "seasonendDate" date,
  "seasonactive" boolean,
  "competitionId" integer
);

CREATE TABLE "competiions" (
  "competitionId" integer PRIMARY KEY,
  "name" text,
  "area_id" integer,
  "area_alpha2code" text,
  "area_alpha3code" text,
  "area_name" text,
  "format" text,
  "type" text,
  "category" text,
  "gender" text,
  "divisionLevel" integer
);

CREATE TABLE "team_matches_lineups" (
  "playerId" integer,
  "shirtNumber" integer,
  "starting_lineup" boolean,
  "teamId" integer,
  "substitute" boolean,
  "matchId" integer,
  "side" text
);

CREATE TABLE "formations" (
  "playerId" integer,
  "position" text,
  "scheme" text,
  "matchPeriod" integer,
  "startSec" integer,
  "teamId" integer,
  "matchId" integer
);

CREATE TABLE "player_match_positions" (
  "playerId" integer,
  "matchId" integer,
  "code" text,
  "percent" integer
);

CREATE TABLE "events" (
  "matchId" integer,
  "events" jsonb
);

CREATE TABLE "teams" (
  "teamId" integer PRIMARY KEY,
  "name" text,
  "officialName" text,
  "city" text,
  "area_id" integer,
  "area_alpha2code" text,
  "area_alpha3code" text,
  "area_name" text,
  "type" text,
  "category" text,
  "gender" text,
  "parent_teamId" integer,
  "parent_name" text
);

CREATE TABLE "matches" (
  "matchId" integer PRIMARY KEY,
  "label" text,
  "date" text,
  "status" text,
  "competitionId" integer,
  "seasonId" integer,
  "roundId" integer,
  "gameweek" integer,
  "home_teamId" text,
  "away_teamId" text,
  "home_score" integer,
  "away_score" integer,
  "home_win" boolean,
  "draw" boolean
);

CREATE TABLE "tm_to_ws_ids" (
  "playerId" bigint,
  "tm_player_id" bigint PRIMARY KEY,
  "tm_player_url" text
);

CREATE TABLE "transfermarkt_data" (
  "agent" text,
  "contract_expiry" date,
  "current_value" float,
  "highest_value" float,
  "highest_value_date" date,
  "date_joined_current_team" date,
  "strong_foot" text,
  "player_value_last_update" date,
  "playerId" bigint,
  "player_url" text
);

CREATE TABLE "transfermarkt_injuries" (
  "season" text,
  "injury" text,
  "injured_from" date,
  "injured_until" date,
  "duration" integer,
  "games_missed" integer,
  "playerId" bigint,
  "player_injuries_url" text
);

CREATE TABLE "scaling_attributres" (
  "date" timestamp,
  "playerId" integer,
  "matchId" integer,
  "teamId" integer,
  "team_rating" float,
  "enemy_rating" float,
  "competitionId" integer,
  "minutesTagged" integer,
  "league_weight" float,
  "mean_player_value" float,
  "avg_competition_rating" numeric
);

CREATE TABLE "tm_transfers" (
  "season" text,
  "date" timestamp,
  "left" text,
  "joined" text,
  "mv" float,
  "fee" float,
  "tm_player_id" integer,
  "type" text
);

CREATE TABLE "tm_raw_transfers" (
  "season" text,
  "date" timestamp,
  "left" text,
  "joined" text,
  "mv" float,
  "fee" float,
  "tm_player_id" integer
);

CREATE TABLE "transfers" (
  "transferId" integer PRIMARY KEY,
  "fromTeamId" integer,
  "fromTeamName" text,
  "toTeamId" integer,
  "toTeamName" text,
  "active" integer,
  "startDate" date,
  "endDate" date,
  "type" text,
  "value" integer,
  "currency" text,
  "announceDate" date,
  "playerId" integer
);

CREATE TABLE "advanced_stats" (
  "matchId" integer,
  "playerId" integer,
  "minutesTagged" integer,
  "passes" integer,
  "successfulPasses" float
);

CREATE TABLE "tm_historical_agents" (
  "playerId" integer,
  "agent" text,
  "date_collected" date
);

CREATE TABLE "player_speed_rating" (
  "playerId" integer,
  "median_speed" float,
  "speed_count" integer,
  "speed_rating" integer
);

CREATE TABLE "competition_areas" (
  "id" integer PRIMARY KEY,
  "birthArea_alpha2code" text,
  "birthArea_alpha3code" text,
  "birthArea_name" text
);

CREATE TABLE "team_match_rating" (
  "teamId" integer,
  "matchId" integer,
  "rating" float
);

CREATE TABLE "transfers_for_collection" (
  "playerId" integer
);

CREATE TABLE "matches_for_collection" (
  "matchId" integer
);

CREATE TABLE "unmatched_tm_players" (
  "playerId" integer
);

CREATE TABLE "ws_tm_unmatchable_players" (
  "playerId" integer
);

-- ALTER TABLE "competition_areas" ADD FOREIGN KEY ("id") REFERENCES "players" ("birthArea_id");

-- ALTER TABLE "competition_areas" ADD FOREIGN KEY ("id") REFERENCES "players" ("passportArea_id");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "seasons_players" ("playerId");

-- ALTER TABLE "seasons" ADD FOREIGN KEY ("seasonId") REFERENCES "seasons_players" ("seasonId");

-- ALTER TABLE "seasons" ADD FOREIGN KEY ("seasonId") REFERENCES "seasons_matches" ("seasonId");

-- ALTER TABLE "seasons" ADD FOREIGN KEY ("seasonId") REFERENCES "seasons_teams" ("seasonId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "seasons_teams" ("teamId");

-- ALTER TABLE "competiions" ADD FOREIGN KEY ("competitionId") REFERENCES "seasons" ("competitionId");

-- ALTER TABLE "competition_areas" ADD FOREIGN KEY ("id") REFERENCES "competiions" ("area_id");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "team_matches_lineups" ("playerId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "team_matches_lineups" ("teamId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "team_matches_lineups" ("matchId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "formations" ("playerId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "formations" ("teamId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "formations" ("matchId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "player_match_positions" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "player_match_positions" ("matchId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "events" ("matchId");

-- ALTER TABLE "competition_areas" ADD FOREIGN KEY ("id") REFERENCES "teams" ("area_id");

-- ALTER TABLE "competiions" ADD FOREIGN KEY ("competitionId") REFERENCES "matches" ("competitionId");

-- ALTER TABLE "seasons" ADD FOREIGN KEY ("seasonId") REFERENCES "matches" ("seasonId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "matches" ("home_teamId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "matches" ("away_teamId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "tm_to_ws_ids" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "transfermarkt_data" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "transfermarkt_injuries" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "scaling_attributres" ("playerId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "scaling_attributres" ("matchId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "scaling_attributres" ("teamId");

-- ALTER TABLE "tm_to_ws_ids" ADD FOREIGN KEY ("tm_player_id") REFERENCES "tm_transfers" ("tm_player_id");

-- ALTER TABLE "tm_to_ws_ids" ADD FOREIGN KEY ("tm_player_id") REFERENCES "tm_raw_transfers" ("tm_player_id");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "transfers" ("fromTeamId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "transfers" ("toTeamId");

-- ALTER TABLE "transfers" ADD FOREIGN KEY ("playerId") REFERENCES "players" ("playerId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "advanced_stats" ("matchId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "advanced_stats" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "tm_historical_agents" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "player_speed_rating" ("playerId");

-- ALTER TABLE "teams" ADD FOREIGN KEY ("teamId") REFERENCES "team_match_rating" ("teamId");

-- ALTER TABLE "matches" ADD FOREIGN KEY ("matchId") REFERENCES "team_match_rating" ("matchId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "transfers_for_collection" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "unmatched_tm_players" ("playerId");

-- ALTER TABLE "players" ADD FOREIGN KEY ("playerId") REFERENCES "ws_tm_unmatchable_players" ("playerId");

-- player_match_positions
CREATE INDEX IF NOT EXISTS idx_positions_player ON player_match_positions("playerId");

CREATE INDEX IF NOT EXISTS idx_positions_match ON player_match_positions("matchId");

CREATE INDEX IF NOT EXISTS idx_positions_code ON player_match_positions("code");

CREATE INDEX IF NOT EXISTS idx_positions_percent ON player_match_positions("percent");

-- advanced_stats

CREATE INDEX IF NOT EXISTS idx_adv_player ON advanced_stats_total("playerId");

CREATE INDEX IF NOT EXISTS idx_adv_match ON advanced_stats_total("matchId");


-- formations
CREATE INDEX IF NOT EXISTS idx_formations_player ON formations("playerId");

CREATE INDEX IF NOT EXISTS idx_formations_match ON formations("matchId");

--players
CREATE INDEX IF NOT EXISTS idx_players_player ON players("playerId");

CREATE INDEX IF NOT EXISTS idx_players_team ON players("currentTeamId");

CREATE INDEX   IF NOT EXISTS idx_players_birth ON players("birthDate");

CREATE INDEX  IF NOT EXISTS idx_players_birth_area ON players("birthArea_name");

CREATE OR REPLACE FUNCTION public.f_unaccent(text)
  RETURNS text AS
$func$
SELECT public.unaccent('public.unaccent', $1)  -- schema-qualify function and dictionary
$func$  LANGUAGE sql IMMUTABLE PARALLEL SAFE STRICT;

CREATE INDEX IF NOT EXISTS idx_trgm_players_full_name ON players 
USING GIST ((f_unaccent("firstName") || ' ' || f_unaccent("lastName")) gist_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_trgm_players_last_name ON players 
USING GIST (f_unaccent("lastName") gist_trgm_ops);


--competitions
CREATE INDEX IF NOT EXISTS idx_competitions_competition ON competitions("competitionId");

CREATE INDEX IF NOT EXISTS idx_competitions_country ON competitions("area_name");

CREATE INDEX IF NOT EXISTS idx_competitions_division ON competitions("divisionLevel");

CREATE INDEX IF NOT EXISTS idx_competitions_category ON competitions("category");

CREATE INDEX IF NOT EXISTS idx_competitions_type ON competitions("type");

CREATE INDEX IF NOT EXISTS idx_competitions_gender ON competitions("gender");

--seasons
CREATE INDEX IF NOT EXISTS idx_seasons_season ON seasons("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_start_date ON seasons("seasonstartDate");

CREATE INDEX IF NOT EXISTS idx_seasons_end_date ON seasons("seasonendDate");


--seasons_teams
CREATE INDEX IF NOT EXISTS idx_seasons_teams_season ON seasons_teams("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_teams_team ON seasons_teams("teamId");

--seasons_players
CREATE INDEX IF NOT EXISTS idx_seasons_players_season ON seasons_players("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_players_player ON seasons_players("playerId");

--seasons_matches
CREATE INDEX IF NOT EXISTS idx_seasons_matches_season ON seasons_matches("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_matches_match ON seasons_matches("matchId");

--transfermarkt_data
CREATE INDEX IF NOT EXISTS idx_tm_player ON transfermarkt_data("playerId");

CREATE INDEX IF NOT EXISTS idx_tm_agent ON transfermarkt_data("agent");

--tm_injuries
CREATE INDEX IF NOT EXISTS idx_tm_injuries_player ON transfermarkt_injuries("playerId");

--team_matches_lineups
CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_player ON team_matches_lineups("playerId");

CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_match ON team_matches_lineups("matchId");

CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_team ON team_matches_lineups("teamId");

CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_minutes ON team_matches_lineups("minutesTagged");


--leagues_standings
CREATE INDEX IF NOT EXISTS ids_leagues_standings_season ON leagues_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_team ON leagues_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_placement ON leagues_standings("placement");

--cups_standings
CREATE INDEX IF NOT EXISTS ids_cups_standings_season ON cups_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_team ON cups_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_round_name ON cups_standings("roundName");

--player_roles
CREATE INDEX IF NOT EXISTS idx_player_roles_player ON player_roles("playerId");

--player_match_roles
CREATE INDEX IF NOT EXISTS idx_player_match_roles_player ON player_match_roles("playerId");

CREATE INDEX IF NOT EXISTS idx_player_match_roles_match ON player_match_roles("matchId");

--transfers 
CREATE INDEX IF NOT EXISTS idx_transfers_player ON transfers("playerId");

CREATE INDEX IF NOT EXISTS idx_transfers_type ON transfers("type");

-- tm_transfers 

CREATE INDEX IF NOT EXISTS idx_tm_transfers_player ON tm_transfers("tm_player_id");


--scaling_attributes

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_player ON scaling_attributes("playerId");

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_match ON scaling_attributes("matchId");



