import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

# from dag_settings import workdir_potential
workdir_potential = '/home/<USER>/Projects/player-potential'

sys.path.append(workdir_potential)


dag_params = {
    "dag_id": "optimize_potential_hyperparams",
    "start_date": datetime(2022, 5, 18),
    "schedule_interval": None,
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_potential,
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    # sorare_experiment1_nowknd = BashOperator(
    #     task_id="create_potential_mv",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_tweet_thresholds_backtest.py {{params.experiment_name1}} sorare_mvp_tweets sorare_tweet_thresholds_backtest sorare_test 0
    #                        """,
    # )

    # model_training = BashOperator(
    #     task_id="model_training",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/build_final_models.py """,
    # )

    optimize_hyperparams = BashOperator(
        task_id="optimize_hyperparams",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/pipeline/optimize_hyperparams.py """,
    )

    optimize_hyperparams
