DROP TABLE IF EXISTS derived_tables.scaling_attributes;
create table derived_tables.scaling_attributes as

with fq as (
    select distinct "home_teamId" as "teamId",
        "matchId",
        "date",
        "competitionId"
    from wyscout.matches
    union
    select distinct "away_teamId" as "teamId",
        "matchId",
        "date",
        "competitionId"
    from wyscout.matches
        ),
player_team as (
    select distinct on (tm_player_id) tm_player_id,
        joined_id,
        mv
    from transfermarkt.tm_transfers tt
    where "date" < '2019-01-01'
        and mv is not null -- Fixed date bc lack of data prior to 2015
    order by tm_player_id,
        "date" desc
),
team_season as (
    select distinct on (st."teamId") s2."competitionId",
        st."teamId"
    from wyscout.seasons s2,
        wyscout.seasons_teams st
    where s2."seasonId" = st."seasonId"
        and "startDate" > '2019-01-01' -- Fixed date bc lack of data prior to 2015 - Check after table is populated
        and "competitionId" in (
            select "competitionId"
            from wyscout.competitions c2
            where c2."format" = 'Domestic league'
                and POSITION('friend' IN c2."name") = 0
        )
    order by "teamId",
        "startDate" nulls last
),
team_value as (
    select *, coalesce("competitionId", -1) as "competitionId_new"
    from player_team
    left join 
        (
            select transfermarkt_team_id,
                "teamId"
            from transfermarkt.tm_to_ws_team_ids
        ) ttwti 
        on ttwti."transfermarkt_team_id" = player_team.joined_id
        left join team_season on team_season."teamId" = ttwti."teamId"
),
player_match_weights_tmp as (
    select "competitionId_new" as "competitionId",
        AVG(mv) as mean_player_value
    from team_value
    group by "competitionId_new"
),
player_match_weights as (
	select *,
        POWER(mean_player_value, 0.3333) / (select max(POWER(mean_player_value, 0.3333)) from player_match_weights_tmp) as league_weight 
	from player_match_weights_tmp
),
team_match_rating as (
	select tmr.*, m."competitionId", avg(smoothed_rating) over(partition by "competitionId" order by tmr."matchId") avg_competition_rating
	from 
	derived_tables.team_match_rating tmr, wyscout.matches m
	where m."matchId" = tmr."matchId"
	
)

select iq."date",
      tml."playerId",
      tml."matchId",
      iq."teamId",
      -- iq."rating" as team_rating,
      -- iq."enemy_rating" enemy_rating,
      iq."smoothed_rating" as team_rating,
      iq."enemy_smoothed_rating" enemy_rating,
	  iq."competitionId",
      tml."minutesTagged",
      coalesce(pmw."league_weight", (select "league_weight" from player_match_weights where "competitionId" = -1)) as league_weight,
      coalesce(pmw.mean_player_value, (select "mean_player_value" from player_match_weights where "competitionId" = -1)) as mean_player_value,
      iq.avg_competition_rating from
wyscout.lineups tml 

left join
(select tmr.*, tmr2."teamId" opponent_id, tmr2."rating" enemy_rating, tmr2."rating" enemy_smoothed_rating
from 
    	team_match_rating tmr,
      	team_match_rating  tmr2
where tmr."matchId" = tmr2."matchId"
      and tmr."teamId" != tmr2."teamId") iq
on tml."teamId" = iq."teamId"
and tml."matchId" = iq."matchId"


left join
player_match_weights pmw
on iq."competitionId" = pmw."competitionId";


GRANT all ON  derived_tables.scaling_attributes TO elvan, daniel, kliment, airflow, playervaluation, ranks_api_usr;
ALTER TABLE derived_tables.scaling_attributes OWNER TO kliment;
