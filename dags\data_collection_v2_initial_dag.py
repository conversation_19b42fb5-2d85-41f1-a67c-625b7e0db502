from datetime import datetime, timedelta
import schedule
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

from dag_settings import workdir  # TODO change the v2 workdir once done testing

COLLECTION_MODE = "initial"


dag_params = {
    "dag_id": "data_collection_v2_initial_dag",
    "start_date": datetime(2020, 7, 16),
    "schedule_interval": None,
    "catchup": False,
    "params": {
        "workdir": workdir,
        "COLLECTION_MODE": COLLECTION_MODE,
    },  # TODO change back workdir
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>"
        ],  # , "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    create_meta_tables = PostgresOperator(
        task_id="create_updates_ts_table",
        database="wyscout_raw_production",
        sql="""CREATE TABLE IF NOT EXISTS meta.updates_timestamps(
                object_type text,
                last_updated timestamp
        );
        CREATE TABLE IF NOT EXISTS meta.matches_collection_status(
            status integer
        );
        INSERT INTO meta.matches_collection_status VALUES(0);""",
    )

    update_areas = BashOperator(
        task_id="update_areas",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_areas.py """,
    )

    update_competitions = BashOperator(
        task_id="update_competitions",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_competitions.py """,
    )

    update_seasons = BashOperator(
        task_id="update_seasons",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons.py """,
    )

    update_seasons_teams = BashOperator(
        task_id="update_seasons_teams",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_teams.py """,
    )

    update_seasons_players = BashOperator(
        task_id="update_seasons_players",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_players.py """,
    )

    update_seasons_matches = BashOperator(
        task_id="update_seasons_matches",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_seasons_matches.py """,
    )

    update_transfers = BashOperator(
        task_id="update_transfers",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_transfers.py """,
    )

    update_match_objects = BashOperator(
        task_id="update_match_objects",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_match_objects.py """,
    )

    derive_advanced_stats = BashOperator(
        task_id="derive_advanced_stats",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/derive_advanced_stats.py""",
    )

    collect_match_info_for_empty_games = BashOperator(
        task_id="collect_match_info_for_empty_games",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/collect_match_info_for_empty_games.py""",
    )

    update_leagues_standings = BashOperator(
        task_id="update_leagues_standings",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_leagues_standings.py""",
    )
    update_cups_standings = BashOperator(
        task_id="update_cups_standings",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/update_cups_standings.py""",
    )
    # TODO put those back in when done testing
    # update_historical_elo = BashOperator(
    #     task_id="update_historical_elo",
    #     bash_command=""" cd {{ params.workdir }}
    #                     Rscript src/features_creation/calculate_team_strengths.R """,
    # )

    # update_scaling_attributes = PostgresOperator(
    #     task_id="update_scaling_attributes",
    #     database="wyscout_raw_development",
    #     sql=open(workdir + "/src/queries/create_scaling_attributes.sql").read(),
    # )

    def migration_scheduler():
        def job_that_executes_once():
            return schedule.CancelJob

        schedule.every().day.at("02:00").do(job_that_executes_once)

        while len(schedule.jobs) > 0:
            schedule.run_pending()
            time.sleep(60)

    create_indexes = PostgresOperator(
        task_id="create_indexes",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_indexes_v2.sql").read(),
    )

    create_agg_stats_mv = PostgresOperator(
        task_id="create_agg_stats_mv",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_agg_stats_mv.sql").read(),
    )

    report_data_availability = BashOperator(
        task_id="report_data_availability",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/output_reporting/report_data_availability.py""",
    )
    # TODO put back when done testing
    # score_roles = BashOperator(
    #     task_id="score_roles",
    #     bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                         python3 src/features_creation/score_roles.py""",
    # )

    fill_gaps = BashOperator(
        task_id="fill_gaps",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/fill_gaps.py """,
    )
    create_missing_records = BashOperator(
        task_id="create_missing_records",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        export COLLECTION_MODE="{{params.COLLECTION_MODE}}"
                        cd {{ params.workdir }}
                        python3 src/data_collection/wyscout/v2/scripts_for_dag/create_missing_records.py """,
    )
    create_keys = PostgresOperator(
        task_id="create_keys",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_keys.sql").read(),
    )

    refresh_player_match_info_mv = PostgresOperator(
        task_id="refresh_player_match_info_mv",
        database="wyscout_raw_production",
        sql=open(
            workdir + "/src/queries/create_player_match_info_mv.sql"
        ).read(),
    )

    grouper = DummyOperator(task_id="grouper")
    grouper2 = DummyOperator(task_id="grouper2")

    grant_list = []
    for db in ["wyscout_raw_production", "wyscout_raw_development"]:
        grant_list.append(
            PostgresOperator(
                task_id="grant_all_to_all_" + db,
                database=db,
                sql=open(workdir + "/src/queries/grant_all_to_all.sql").read(),
            )
        )
    grant_to_service_accs = PostgresOperator(
        task_id="grant_to_service_accs",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/grant_services_accs.sql").read(),
    )

    (
        create_meta_tables
        >> update_areas
        >> update_competitions
        >> update_seasons
        >> update_seasons_teams
        >> update_seasons_matches
        >> update_seasons_players
        >> update_transfers
        >> grouper2
        >> [
            update_match_objects,
            derive_advanced_stats,
            
        ]
        >> collect_match_info_for_empty_games
        >> refresh_player_match_info_mv
        >> update_leagues_standings
        >> update_cups_standings
        >> grouper
    )
    # >> [
    # update_historical_elo,
    # update_scaling_attributes,
    # score_roles,    ]

    (
        grouper
        >> create_indexes
        >> fill_gaps
        >> create_missing_records
        >> create_keys
        >> grant_list
        >> grant_to_service_accs
        >> report_data_availability
        >> create_agg_stats_mv
    )
