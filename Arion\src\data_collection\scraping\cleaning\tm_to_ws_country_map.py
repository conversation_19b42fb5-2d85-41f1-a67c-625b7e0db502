def map_countries(x):
    country_map = {
        "Korea, North": "Korea DPR",
        "Korea, South": "Korea Republic",
        "Cote d'Ivoire": "Côte d'Ivoire",
        "North Macedonia": "Macedonia FYR",
        "DR Congo": "Congo DR",
        "Botsuana": "Botswana",
        "The Gambia": "Gambia",
        "China": "China PR",
        "Ireland": "Ireland Republic",
        "Cape Verde": "Cape Verde Islands",
        "Serbia and Montenegro": "Montenegro",
        "Macedonia": "Macedonia FYR",
        "Osttimor": "East Timor",
        "St. Kitts & Nevis": "St. Kitts and Nevis",
        "Palästina": "Palestine",
        "Eswatini": "Swazilend",
        "Southern Sudan": "South Sudan",
        #  'Guernsey',
        "Neukaledonien": "New Caledonia",
        "Cookinseln": "Cook Islands",
        "Cayman-Inseln": "Cayman Islands",
    }
    return x if x not in country_map else country_map[x]
