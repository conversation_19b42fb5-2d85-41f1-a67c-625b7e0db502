import unittest
from src.data_collection.new_scraping.scraping.scrape_tm_player_profiles import (
    PlayerProfilesScraper,
)


class TestScraper(unittest.TestCase):
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like"
            " Gecko) Chrome/47.0.2526.106 Safari/537.36"
        )
    }
    scraper = PlayerProfilesScraper(headers)
    test_ll = [
        "https://www.transfermarkt.com/muhammad-isa/profil/spieler/722691",
        "https://www.transfermarkt.com/andy-carroll/profil/spieler/48066",
        "https://www.transfermarkt.com/pouria-aria-kia/profil/spieler/469925",
    ]
    df = scraper.loop_through_urls(test_ll)

    def test_player_name(self):
        self.assertEqual(self.df["name"][0], "<PERSON>")
        self.assertEqual(len(self.df["name"]), 3)

    def test_birth_date(self):
        self.assertEqual(self.df["birth_date"][0], "Sep 4, 1994")
        self.assertEqual(len(self.df["name"]), 3)

    def test_data_frame_size(self):
        self.assertEqual(len(self.df), 3)
        self.assertEqual(len(self.df.columns), 20)
