from datetime import datetime

import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from ratelimit import limits, sleep_and_retry
from tenacity import retry, wait_random, stop_after_attempt

from src.helper_funcs import get_wyscout_response
from settings import postgres_prod_str, postgres_dev_str


class ApiCollector:
    def __init__(
        self,
        ids,
        base_url="https://apirest.wyscout.com/v2/players/_ID/transfers",
    ):

        self.ids = tuple(ids) if not isinstance(ids, tuple) else ids
        self.base_url = base_url
        self.df = pd.DataFrame()
        self.df_list = []

    @retry(wait=wait_random(), stop=stop_after_attempt(2))
    @sleep_and_retry
    @limits(calls=8, period=1)
    def collect(self, playerId):
        """
        Collect API response for a given playerId
        Parameters:
        playerId(int): id of player
        """
        return get_wyscout_response(self.base_url.replace("_ID", str(playerId)))

    def prep(self, resp):
        """
        Prep response from API
        Parameters:
        resp: wyscout response
        """
        if "transfer" in resp:
            return pd.DataFrame(resp["transfer"])

    def loop(self):
        """
        Loop through all ids, collect from API, transform and concat
        """
        for playerId in self.ids:
            resp = self.collect(playerId)
            self.df_list.append(self.prep(resp))
        self.df = pd.concat(self.df_list)
        self.df = self.df.replace("", np.nan).replace("0000-00-00", np.nan)


class IO:
    def __init__(self, cnx_dev, cnx_prod, transfers_table, tocollect_table):
        self.cnx_dev = cnx_dev
        self.cnx_prod = cnx_prod
        self.tocollect_table = tocollect_table
        self.transfers_table = transfers_table
        self.transfers_written = None
        self.ids_for_collection = None
        self.collected_transfers = None

    def get_outstanding_ids(self):
        """
        Read ids that need to be looped
        """
        # TODO remove limit once tested
        df = pd.read_sql(f"SELECT * FROM {self.tocollect_table}", self.cnx_prod)
        self.ids_for_collection = tuple(df["playerId"].unique())

    def get_collected_transfers(self):
        """
        Read collected transfers
        """
        df = pd.read_sql(
            f'SELECT "transferId" FROM {self.transfers_table}', self.cnx_dev
        )
        self.collected_transfers = df["transferId"]

    def write_transfers(self, transfers_df):
        """
        Write collected tranfers
        """
        if len(transfers_df) == 0:
            raise ValueError("Transfers df is empty")
        transfers_df.to_sql(
            self.transfers_table, self.cnx_dev, if_exists="append", index=False
        )
        self.transfers_written = True

    def clear_collected_ids(self, ids):
        """
        Remove collected and written ids from the table for
        outstanding transfers to be collected
        """
        if self.transfers_written is None:
            raise ValueError(
                "Transfers must be written first before clearing their ids"
            )
        query = f"""DELETE FROM {self.tocollect_table}
                    WHERE "playerId" IN {ids} """
        self.cnx_prod.execute(query)


class Orchestrator:
    def __init__(self, io, batch_size):
        """
        Loop through all outstanding players: read, collect, prep and write
        """
        self.io = io
        self.batch_size = batch_size

    def orchestrate(self):
        """
        Do the whole thing
        """
        self.io.get_outstanding_ids()
        self.io.get_collected_transfers()
        ids = self.io.ids_for_collection
        print(f"{np.ceil(len(ids) / self.batch_size)} batches for collection")
        for i in range(0, len(ids), self.batch_size):
            batch = ids[i : i + self.batch_size]
            collector = ApiCollector(batch)
            collector.loop()
            df = collector.df
            df = df[~df.transferId.isin(self.io.collected_transfers)]
            self.io.write_transfers(df)
            print(datetime.now(), f"{len(df)} transfers written")
            self.io.clear_collected_ids(batch)
            print(datetime.now(), f"Batch {i/self.batch_size +1} done")


def main():
    batch_size = 1000
    transfers_table, tocollect_table = "transfers", "transfers_for_collection"
    cnx_dev, cnx_prod = create_engine(postgres_dev_str), create_engine(
        postgres_prod_str
    )
    io = IO(cnx_dev, cnx_prod, transfers_table, tocollect_table)
    orch = Orchestrator(io, batch_size)
    orch.orchestrate()


if __name__ == "__main__":
    main()
