from datetime import datetime
import asyncio
import aiohttp
import numpy as np
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)
from bs4 import BeautifulSoup
import pandas as pd
from multiprocessing import Pool, cpu_count

#policy = asyncio.WindowsSelectorEventLoopPolicy()
#asyncio.set_event_loop_policy(policy)
pd.set_option("display.max_columns", 21)


class PlayerProfilesScraper(Scraper):
    async def loop_through_urls(
        self, urls: list, ids: list
    ) -> "list[pd.DataFrame]":
        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for url, id in zip(urls, ids):
                html_tasks.append(fetch(session, url, id))
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> "list[pd.DataFrame]":

        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_page, htmls)
        pool.close()
        pool.join()

        df = []
        error_ids = []
        transfer_df = []
        agent_df = []
        contract_df = []
        for data in res:
            if data["info_dict"] is not None:
                df.append(data["info_dict"])
                [transfer_df.append(tr) for tr in data["transfers_list"]]
                agent_df.append(data["agent_history"])
                contract_df.append(data["contract_history"])
            if data["error_ids"] is not None:
                error_ids.append(data["error_ids"])

        return [
            pd.DataFrame(df),
            pd.DataFrame(error_ids),
            pd.DataFrame(transfer_df),
            pd.DataFrame(agent_df),
            pd.DataFrame(contract_df),
        ]

    def scrape_page(self, tuple):  # sourcery no-metrics
        text, url, wyscout_id = tuple
        print(url)

        result = {
            "info_dict": None,
            "agent_history": None,
            "transfers_list": None,
            "error_ids": None,
        }

        # Initializing dicts
        info_dict = {
            # Basic
            "player_url": np.nan,  #
            "playerId": np.nan,  #
            "name": np.nan,  #
            "birth_date": np.nan,  #
            "age": np.nan,  #
            "height": np.nan,  #
            "citizenship": np.nan,  #
            "position": np.nan,  #
            # "agent": [], #
            "current_club": np.nan,  #
            "current_club_id": np.nan,  # specific
            "strong_foot": np.nan,  #
            "date_joined_current_team": np.nan,  #
            "contract_expiry": np.nan,  #
            # dataZusatzDaten
            "league_tier": np.nan,  #
            "league_country": np.nan,  #
            # auflistung
            "current_value": np.nan,  #
            "player_value_last_update": np.nan,  #
            "highest_value": np.nan,  #
            "highest_value_date": np.nan,  #
            "agent": np.nan,
            "agent_id": np.nan,  #
            "status": np.nan,  #
        }
        error_ids = {
            "tm_player_id": np.nan,
            "status": np.nan,
            "date_collected": np.nan,
        }

        agent_history = {
            "playerId": np.nan,
            "agent": np.nan,
            "date_collected": np.nan,
            "agent_id": np.nan,
        }
        contract_history = {
            "playerId": np.nan,
            "club_at_time": np.nan,
            "contract_expiry": np.nan,
            "is_on_loan": False,
            "date_collected": np.nan,
        }

        soup = BeautifulSoup(text, "html.parser")

        id = url.split("/")[-1]
        if soup is None:
            print(f"no soup {url}")
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Error"
            error_ids["date_collected"] = str(datetime.today().date())
            result["error_ids"] = error_ids
            return result
        if not soup.find(
            "h1", attrs={"class": "data-header__headline-wrapper"}
        ):
            print(f"no name {url}")
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Error"
            error_ids["date_collected"] = str(datetime.today().date())
            result["error_ids"] = error_ids
            return result
        status = "active"
        club_name = soup.find("span", attrs={"class": "data-header__club"})
        if club_name:
            if club_name.text.strip() == "Retired":
                status = "Retired"
        if soup.find("div", attrs={"class": "dataRibbonRIP"}):
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Dead"
            error_ids["date_collected"] = str(datetime.today().date())
            result["error_ids"] = error_ids
            return result
        info_dict["name"] = (
            soup.find("h1", attrs={"class": "data-header__headline-wrapper"})
            .text.split("\n")[-1]
            .strip()
        )

        curr_player_id = id
        # Profile
        info_dict["player_url"] = url
        info_dict["playerId"] = wyscout_id
        info_dict["status"] = status

        # Agent history
        agent_history["playerId"] = wyscout_id
        agent_history["date_collected"] = str(datetime.today().date())

        # Contract history
        contract_history["playerId"] = wyscout_id
        contract_history["date_collected"] = str(datetime.today().date())

        # Getting leaguer tier and league country
        try:
            league_el = soup.find(
                attrs={"class": "data-header__club-info"}
            ).find(attrs={"class": "flaggenrahmen"})
            info_dict["league_tier"] = league_el.parent.text.strip()
            info_dict["league_country"] = league_el["title"]

        except:
            pass

        # Getting player current value, last update, highest value, highest value date
        try:
            market_value_el = soup.find(
                attrs={
                    "class": (
                        "tm-player-market-value-development__current-and-max"
                    )
                }
            )
            info_dict["current_value"] = market_value_el.find(
                attrs={
                    "class": "tm-player-market-value-development__current-value"
                }
            ).text.strip()
            info_dict["highest_value"] = market_value_el.find(
                attrs={"class": "tm-player-market-value-development__max-value"}
            ).text.strip()
            info_dict["highest_value_date"] = (
                market_value_el.find(
                    attrs={"class": "tm-player-market-value-development__max"}
                )
                .findAll("div")[-1]
                .text.strip()
            )
            info_dict["player_value_last_update"] = (
                soup.find(
                    attrs={
                        "class": "tm-player-market-value-development__update"
                    }
                )
                .text.split(":")[-1]
                .strip()
            )
        except:
            pass

        # Getting all the data from the player data box
        mapper = {
            "Age:": "age",
            "Date of birth:": "birth_date",
            "Height:": "height",
            "Citizenship:": "citizenship",
            "Position:": "position",
            "Foot:": "strong_foot",
            "Joined:": "date_joined_current_team",
            "Current club:": "current_club",
            "Contract expires:": "contract_expiry",
        }
        try:
            player_data_box_el = soup.find(
                attrs={"class": "info-table--right-space"}
            )
            keys = [
                k.text.strip()
                for k in player_data_box_el.find_all(
                    attrs={
                        "class": (
                            "info-table__content info-table__content--regular"
                        )
                    }
                )
            ]
            values = player_data_box_el.find_all(
                attrs={"class": "info-table__content--bold"}
            )
            for (key, value) in zip(keys, values):
                if key in mapper:
                    info_dict[mapper[key]] = value.text.strip()

                    if key == "Current club:":
                        info_dict["current_club_id"] = value.find("a")[
                            "href"
                        ].split("/")[-1]
                        contract_history["club_at_time"] = value.text.strip()
                    elif key == "Contract expires:":
                        contract_history["contract_expiry"] = value.text.strip()
                if key == "On loan from:":
                    contract_history["is_on_loan"] = True
                if key == "Player agent:":
                    agent_history["agent"] = value.text.strip()
                    info_dict["agent"] = value.text.strip()
                    try:
                        agent_id_str = value.find("a")["href"].split("/")[-1]
                        agent_id = (
                            agent_id_str if agent_id_str.isdigit() else np.nan
                        )
                        info_dict["agent_id"] = agent_id
                        agent_history["agent_id"] = agent_id
                    except:
                        pass
                # Assigning nan to keys which aren't present in the keys list
        except:
            pass
        result["info_dict"] = info_dict
        result["agent_history"] = agent_history
        result["contract_history"] = contract_history
        transfers_list = self.extract_transfers(soup, curr_player_id)
        result["transfers_list"] = transfers_list
        return result

    def extract_transfers(self, soup, curr_player_id):
        transfer_dict = {
            "season": np.nan,
            "date": np.nan,
            "left_team_season_transfers_url": np.nan,
            "left_id": np.nan,
            "left_country": np.nan,
            "left_alt": np.nan,
            "left_name": np.nan,
            "joined_team_season_transfers_url": np.nan,
            "joined_id": np.nan,
            "joined_country": np.nan,
            "joined_alt": np.nan,
            "joined_name": np.nan,
            "mv": np.nan,
            "fee": np.nan,
            "tm_player_id": np.nan,
            "type": np.nan,
        }
        transfer_list = []
        try:
            divs = soup.find_all(
                "div", attrs={"class": "tm-player-transfer-history-grid"}
            )[1:-1]
        except:
            print(f"No transfer box {curr_player_id}")
            return transfer_list
        for col in divs:
            if len(col.find_all("div")) == 6:
                rows = col.find_all("div")
            else:
                continue

            transfer_dict["season"] = rows[0].text.strip()
            transfer_dict["date"] = rows[1].text.strip()

            for key, value in {2: "left", 3: "joined"}.items():
                try:
                    transfer_dict[f"{value}_team_season_transfers_url"] = rows[
                        key
                    ].a["href"]
                except:
                    transfer_dict[f"{value}_team_season_transfers_url"] = np.nan
                try:
                    transfer_dict[f"{value}_id"] = (
                        rows[key].a["href"].split("/")[4]
                    )
                except:
                    transfer_dict[f"{value}_id"] = np.nan
                try:
                    transfer_dict[f"{value}_alt"] = (
                        rows[key].a.img["alt"].strip()
                    )
                except:
                    transfer_dict[f"{value}_alt"] = np.nan
                try:
                    transfer_dict[f"{value}_country"] = rows[key].find(
                        "img",
                        attrs={
                            "class": "tm-player-transfer-history-grid__flag"
                        },
                    )["alt"]
                except:
                    transfer_dict[f"{value}_country"] = np.nan
                try:
                    transfer_dict[f"{value}_name"] = (
                        rows[key]
                        .find(
                            "a",
                            attrs={
                                "class": (
                                    "tm-player-transfer-history-grid__club-link"
                                )
                            },
                        )
                        .text.strip()
                    )
                except:
                    transfer_dict[f"{value}_name"] = np.nan
            transfer_dict["mv"] = rows[4].text.strip()

            fee_text = rows[5].text
            type, fee = (np.nan, np.nan)
            if "€" in fee_text:
                if ":" in fee_text:
                    type, fee = fee_text.split(":")
                else:
                    fee = fee_text.strip()
                    type = "transfer"
            else:
                type = fee_text
            transfer_dict["fee"] = fee
            transfer_dict["type"] = type.strip()

            transfer_dict["tm_player_id"] = curr_player_id
            transfer_list.append(transfer_dict.copy())
        return transfer_list


if __name__ == "__main__":
    scraper = PlayerProfilesScraper()
    ll = asyncio.run(
        scraper.loop_through_urls(
            urls=[
                "https://www.transfermarkt.com/marcus-rashford/profil/spieler/258923"
            ],
            ids=[123],
        )
    )
    print(ll)
