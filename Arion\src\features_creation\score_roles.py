from typing import Dict, ItemsView, List, Optional
import requests
import json
import asyncio
import itertools
import traceback 

import aiohttp
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from tenacity import retry, wait_fixed, stop_after_attempt

from src.data_collection.wyscout.v2.Updater import Updater

from src.helper_funcs import (
    fast_read_sql,
    fast_write_sql,
    dedupe_table,
    get_cloud_logger,
    cloud_log_struct,
    cloud_log_text,
    AuthProvider,
)
from settings import (
    postgres_prod_str,
    PLAYER_ROLE_API_USR,
    PLAYER_ROLE_API_PASS,
)


class RolesUpdater(Updater):
    def __init__(self):
        super().__init__(
            None,
            auth_provider=AuthProvider(
                PLAYER_ROLE_API_USR, PLAYER_ROLE_API_PASS
            ),
        )
        self.cnx_prod = create_engine(postgres_prod_str)
        self.base_url: str = "https://player-role-aolebn4toq-ew.a.run.app/role/"
        self.games_for_collection: Optional[Dict[int, List[int]]] = None
        self.source_schema: str = "wyscout"
        self.target_schema: str = "derived_tables"
        self.df: Optional[pd.DataFrame] = None
        self.gk_df: Optional[pd.DataFrame] = None
        self.rows_list: List = []
        self.start_date = "2018-01-01"
        self.player_role_table_name: str = "player_match_roles"
        self.aggregated_roles_table_name: str = "player_roles"
        self.player_role_cols: List[str] = [
            "agg_cb",
            "attacking_winger",
            "cam",
            "cdm",
            "classic_winger",
            "fb",
            "holding_mid",
            "linkup_striker",
            "regista",
            "speedy_striker",
            "stable_cb",
            "tank",
            "wb",
            "gk",
            "matchId",
            "playerId",
        ]
        self.logger = get_cloud_logger()
        self.batch_size: int = 1000

    def prep_to_write(self, table):
        temp = pd.DataFrame(columns=self.player_role_cols)
        return pd.concat([temp, table]).fillna(0)

    @staticmethod
    def chunked(it: ItemsView, size: int):
        it_temp = iter(it)
        while True:
            p = tuple(itertools.islice(it_temp, size))
            if not p:
                break
            yield p

    def write_gks(self):
        """Write goalkeeper profiles"""
        if self.gk_df is None:
            raise ValueError("Goalkeepers are not prepped yet")

        if len(self.gk_df) > 0:
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "action": "writing gk games",
                    "id_count": len(self.gk_df),
                },
            )

            fast_write_sql(
                self.gk_df,
                self.player_role_table_name,
                self.cnx_prod,
                schema=self.target_schema,
                if_exists="append",
            )

    def get_games_for_scoring(self):
        """Get all player-match pairs where we have adv/events data but no scored roles"""

        # get p-m pairs with data from prod
        df = fast_read_sql(
            f"""SELECT adv."playerId", adv."matchId", ap."role_code2"
                                FROM 
                                {self.source_schema}.player_match_info adv,
                                {self.source_schema}.players ap,
                                (SELECT "playerId", "matchId"
                                FROM 
                                {self.source_schema}.formations
                                GROUP BY "playerId", "matchId") fm
                                WHERE 
                                    adv."matchId" = fm."matchId"
                                    AND adv."playerId" = fm."playerId"
                                    AND adv."date">'{self.start_date}'
                                    AND adv."playerId" = ap."playerId"
                                    AND adv."minutesTagged" >= 30
                                    AND ap."role_code2" != ''
                                """,
            self.cnx_prod,
        )
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "loaded_scoring_games",
                "id_count": len(df),
            },
        )
        # get already scored:
        already_scored = fast_read_sql(
            f"""SELECT "playerId", "matchId", gk FROM {self.target_schema}.{self.player_role_table_name}""",
            self.cnx_prod,
        )
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "loaded_scored_games",
            },
        )

        # merge and we see nulls aka unscored games
        df = pd.merge(
            df, already_scored, on=["playerId", "matchId"], how="left"
        )
        df = df[df.gk.isnull()].drop(columns=["gk"])
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "games_for_scoring",
                "id_count": len(df),
            },
        )
        # subset unscored goalkeeper games
        gk_df = df[df.role_code2 == "GK"].drop(columns=["role_code2"])
        # append usual columns and fill nans 0:
        gk_df = self.prep_to_write(gk_df)
        # set gk role prob to 1
        gk_df["gk"] = 1
        self.gk_df = gk_df
        # separate the non-gk dudes:
        non_gks = df[df.role_code2 != "GK"].drop(columns=["role_code2"])
        # transform to a list of the form {match: [players]}:
        self.games_for_collection = (
            non_gks.groupby("matchId")["playerId"].apply(list).to_dict()
        )
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "games_for_scoring",
                "id_count": len(self.games_for_collection),
            },
        )
    def dedupe_roles_table(self):
        dedupe_table(
            self.cnx_prod,
            self.player_role_table_name,
            self.target_schema,
            ["matchId", "playerId"],
        )
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "player_match_roles table deduped",
            },
        )

    @retry(wait=wait_fixed(5), stop=stop_after_attempt(2))
    def get_role_request(self, params):
        resp = requests.get(
            self.base_url,
            params=params,
            auth=requests.auth.HTTPBasicAuth(
                PLAYER_ROLE_API_USR, PLAYER_ROLE_API_PASS
            ),
        )
        if resp.status_code == 200:
            return json.loads(resp.text)
        # if we have issue with request params, we move on the next one:
        elif resp.status_code not in {429, 500}:
            return None
        else:
            cloud_log_text(self.logger, f"internal server error for {params}")
            raise Exception(resp.text)

    def wrap_up(self, counter: int = 0):
        df = pd.DataFrame(self.rows_list)
        df = self.prep_to_write(df)
        fast_write_sql(
            df,
            self.player_role_table_name,
            self.cnx_prod,
            schema=self.target_schema,
            if_exists="append",
        )
        self.rows_list = []
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "action": "player-match pairs written",
                "batch": counter,
            },
        )

    def loop_players(self):
        """Loop through outstanding p-m pairs, score and write to db"""

        counter = 1
        for match, players in self.games_for_collection.items():
            try:
                row = self.get_role_request(
                    {"matchId": match, "playerId": players}
                )
            except Exception as e:
                # if we are raising an exception, wrap before we raise
                self.wrap_up()
                raise (e)

            if row is not None:
                self.rows_list += row
                counter += 1
            else:
                cloud_log_text(
                    self.logger, f"Error for match {match}, players {players}"
                )
            # export every 10k rows and reset list:
            if counter % 1000 == 0:
                cloud_log_text(self.logger, f" player role scored {counter}")
            if counter % self.batch_size == 0:
                self.wrap_up(counter)

        self.wrap_up(counter)

    async def async_loop_players(self):
        counter = 0
        for batch in self.chunked(
            self.games_for_collection.items(), self.batch_size
        ):
            tasks = []
            async with aiohttp.ClientSession() as session:
                for match, players in batch:
                    tasks.append(
                        asyncio.create_task(
                            self.process_response(
                                session,
                                0,
                                {"matchId": match, "playerId": players},
                            )
                        )
                    )
                # collect results form tasks:
                results = await asyncio.gather(*tasks)
                # filter for None responses (i.e. empty/broken responses)
                results = list(filter(lambda x: x is not None, results))
            counter += self.batch_size
            if results:
                self.rows_list = list(np.concatenate(results))
                self.self.wrap_up(counter)

    def aggregate_player_roles(self):
        grp_str_list = [
            f'round(avg("{col}"::numeric)*100, 2) as "{col}" '
            for col in self.player_role_cols
            if col not in ["playerId", "matchId"]
        ]
        grp_str = ", ".join(grp_str_list)

        grouped_df = fast_read_sql(
            f"""SELECT
                                        {grp_str},
                                        "playerId"
                                        from {self.target_schema}.{self.player_role_table_name}
                                        GROUP BY "playerId"
                                           """,
            self.cnx_prod,
        )
        top3_names = pd.DataFrame(
            grouped_df.columns.values[
                np.argsort(-grouped_df.iloc[:, :-1].values, axis=1)[:, :3]
            ],
            index=grouped_df["playerId"],
            columns=[
                "primary_position",
                "secondary_position",
                "third_position",
            ],
        ).reset_index()
        top3_values = (
            grouped_df.iloc[:, :-1]
            .apply(np.sort, axis=1)
            .apply(lambda x: x[-3:])
            .apply(pd.Series)
        )
        top3_values.columns = [
            "third_position_percent",
            "secondary_position_percent",
            "primary_position_percent",
        ]
        top3 = pd.concat([top3_values, top3_names], axis=1)
        top3 = top3[
            [
                "playerId",
                "primary_position",
                "primary_position_percent",
                "secondary_position",
                "secondary_position_percent",
                "third_position",
                "third_position_percent",
            ]
        ]
        for col in [x for x in top3_names.columns if x != "playerId"]:
            top3[col] = np.where(top3[f"{col}_percent"] > 0, top3[col], np.nan)

        #ghetto fix so that we dont drop and have to cascade mvs:
        connection = self.cnx_prod.raw_connection()
        cursor = connection.cursor()
        try:
            cursor.execute(f"DELETE FROM {self.target_schema}.{self.aggregated_roles_table_name};")
            fast_write_sql(
                top3,
                self.aggregated_roles_table_name,
                self.cnx_prod,
                if_exists="append",
                schema=self.target_schema,
                transaction=True,
                connection=connection,
                cursor=cursor,
            )
            connection.commit()
            cursor.close()
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "action": "aggregate_player_roles_written",
                },
            )
        except:
            connection.rollback()
            cursor.close()
            print(traceback.format_exc())
            raise Exception()

def main():
    ru = RolesUpdater()
    ru.get_games_for_scoring()
    ru.write_gks()
    ru.loop_players()
    ru.dedupe_roles_table()
    ru.aggregate_player_roles()


if __name__ == "__main__":
    main()
