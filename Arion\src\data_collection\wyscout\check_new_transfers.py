from datetime import datetime, timedelta

import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from ratelimit import limits, sleep_and_retry
from tenacity import retry, wait_random, stop_after_attempt

from src.helper_funcs import get_wyscout_response
from settings import postgres_prod_str, postgres_dev_str


class UpdatesChecker:
    def __init__(
        self,
        cnx_prod,
        base_url="https://apirest.wyscout.com/v2/updatedobjects",
        table_name="transfers_for_collection",
        obj_type="transfers",
        days_back=6,
        empty_payload="true",
    ):
        self.cnx_prod = cnx_prod
        self.base_url = base_url
        self.obj_type = obj_type
        self.days_back = days_back
        self.empty_payload = empty_payload
        self.table_name = table_name
        self.resp = None
        self.updated_ids = None

    def get_changes(self):
        """
        Gets response from ws changedo objects api endpoint
        """
        timestamp = (datetime.now() - timedelta(days=self.days_back)).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        params = {
            "updated_since": timestamp,
            "emptyPayload": self.empty_payload,
            "type": self.obj_type,
        }
        self.resp = get_wyscout_response(self.base_url, params)

    def prep_changes(self):
        """
        Converts it into a table for writing to db
        """
        pass
        self.updated_ids = pd.DataFrame(
            [x for x in self.resp["transfers"]], columns=["playerId"]
        )

    def write_to_db(self):
        """
        Writes updated df to db
        """
        self.updated_ids.to_sql(
            self.table_name, self.cnx_prod, if_exists="append", index=False
        )


def main():

    cnx_prod = create_engine(postgres_prod_str)
    checker = UpdatesChecker(cnx_prod)
    checker.get_changes()
    checker.prep_changes()
    checker.write_to_db()


if __name__ == "__main__":
    main()
