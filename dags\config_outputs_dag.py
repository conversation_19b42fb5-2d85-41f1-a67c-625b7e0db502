from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

dag_params = {
    "dag_id": "compute_config_dag",
    "start_date": datetime(2020, 7, 26),
    "schedule_interval": None,
    "params": {"workdir": "/home/<USER>/Projects/Arion"},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": True,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    compute_outputs = BashOperator(
        task_id="compute_outputs",
        bash_command=""" export PYTHONPATH="/home/<USER>/airflow/Arion:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/tests/validation/generate_outputs_for_scales.py """,
    )

    move_raw_df_to_db = BashOperator(
        task_id="move_raw_df_to_db",
        bash_command=""" cd {{ params.workdir }}
                        Rscript src/tests/validation/move_raw_data_to_DB.R """,
    )

    compute_outputs >> move_raw_df_to_db
