from time import time
from sqlalchemy import create_engine
import pandas as pd
import numpy as np
import string
import re
from fuzzywuzzy import process
from Levenshtein import distance as levenshtein_distance
from settings import postgres_prod_str
from src.helper_funcs import fast_write_sql


class TeamNameMatcher:
    def __init__(self, match_threshold, substring_threshold, cnx):
        self.cnx = cnx
        self.match_threshold = match_threshold
        self.substring_threshold = substring_threshold

    def read_data(self):

        ws_query = """ select *
                    FROM teams """

        tm_query = """ select distinct on(team_id) *
        from (
                select tt.joined_id as team_id,
                    tt.joined_name as team_name,
                    tt2.league_name,
                    tt2.league_country,
                    tt2.league_tier,
                    tt2.team_url
                from tm_transfers tt,
                    tm_teams tt2
                where tt.joined_id = tt2.team_id
                    and tt."type" in ('transfer', 'free transfer', 'loan')
                union
                select tt.left_id as team_id,
                    tt.left_name as team_name,
                    tt2.league_name,
                    tt2.league_country,
                    tt2.league_tier,
                    tt2.team_url
                from tm_transfers tt,
                    tm_teams tt2
                where tt.left_id = tt2.team_id
                    and tt."type" in ('transfer', 'free transfer', 'loan')
            ) iq
        order by team_id """
        # teams_init = pd.read_sql('''SELECT * FROM competition_teams;''', cnx)
        self.ws_teams = pd.read_sql(ws_query, self.cnx)
        self.tm_teams = pd.read_sql(tm_query, self.cnx)
        sql_file = open("src/queries/merge_tm_ws_latest_teams.sql")
        self.merged_df = pd.read_sql(sql_file.read(), self.cnx)
        self.ws_teams = self.ws_teams[
            ~self.ws_teams["teamId"].isin(self.merged_df["teamId"])
        ].copy()
        self.tm_teams = self.tm_teams[
            ~self.tm_teams["team_id"].isin(
                self.merged_df["transfermarkt_team_id"]
            )
        ].copy()

    @staticmethod
    def handle_youth_teams(s):
        # Under whatever
        Uxx_search1 = re.search(r"u[\d]{2,2}", s)
        Uxx_search2 = re.search(r"under [\d]{2,2}", s)
        Uxx_search3 = re.search(r"under[\d]{2,2}", s)
        youth_search = re.search(
            r"\s*\byth\b\s*|\s*\byouth\b\s*|\s*\bjugend\b\s*", s
        )

        if (
            (Uxx_search1 is not None)
            | (Uxx_search2 is not None)
            | (Uxx_search3 is not None)
        ):
            under_team = 1
        else:
            under_team = 0

        if youth_search is not None:
            youth_team = 1
        else:
            youth_team = 0

        s = re.sub(r"\s*\byth\b\s*|\s*\byouth\b\s*|\s*\bjugend\b\s*", "", s)
        s = re.sub(r"u[\d]{2,2}", "", s)
        s = re.sub(r"under [\d]{2,2}", "", s)
        s = re.sub(r"under[\d]{2,2}", "", s)

        return s, under_team, youth_team

    @staticmethod
    def handle_second_teams(s):
        # Second team
        st_search = re.search(r"\s*\bii\b\s*|\s*\bb\b\s*", s)
        st_search2 = re.search(r"\s*\b2\b\s*|\s*\bb\b\s*", s)
        if (st_search is not None) | (st_search2 is not None):
            second_team = 1
        else:
            second_team = 0

        s = re.sub(r"\s*\bii\b\s*|\s*\bb\b\s*", "", s)

        return s, second_team

    def find_abbreviations(self):
        tnames = self.ws_teams["officialName"].str.cat(sep=" ")
        words = re.findall(r"\b[A-Z]{2,3}\b", tnames)
        abb_counts = pd.Series(words).value_counts()
        # abb_percentages = abb_counts / sum(abb_counts)
        self.stop_words = list(abb_counts.index)

    def clean_string(self, s):
        s = re.sub("-", " ", s)
        exclude = set(string.punctuation)
        s = "".join(ch for ch in s if ch not in exclude)
        # s = re.sub(r'[A-Z]{2,5}', '', s) # Regex to match FC etc but won't work bc tm data is lowercase
        s = s.lower().strip()

        s, under_team, youth_team = self.handle_youth_teams(s)
        s, second_team = self.handle_second_teams(s)

        s = re.sub(
            r"{}".format(
                "|".join([rf"\s*\b{ss.lower()}\b\s*" for ss in self.stop_words])
            ),
            " ",
            s,
        )
        s = s.strip()

        output = {
            "fixed_string": s,
            "under_team": under_team,
            "youth_team": youth_team,
            "second_team": second_team,
        }

        return output

    def clean_team_names(self, team_df, col):
        df = team_df.copy().reset_index()
        s_outs = df[col].apply(self.clean_string)
        s_outs = pd.DataFrame(list(s_outs))
        s_outs = s_outs.reset_index()
        df["clean_names"] = s_outs["fixed_string"]
        df["youth_team"] = s_outs["youth_team"]
        df["under_team"] = s_outs["under_team"]
        df["second_team"] = s_outs["second_team"]
        df = df[df["clean_names"].apply(lambda x: len(str(x))) > 2]
        return df

    def extract_largest_substring(self, s):
        """
        Extract the largest substring in a team's name
        """
        s_outs = self.clean_string(s)
        s = s_outs["fixed_string"].lower()
        s_split = s.split(" ")
        s_lengths = pd.Series(s_split).apply(len)
        largest_substr = s_split[s_lengths.idxmax()]
        if isinstance(largest_substr, str):
            largest_substr = [largest_substr]
        return largest_substr

    def compare_tm_ws_teams(self, df, tm_col, ws_col):
        """
        Find the minimum distance of largest substring to all substrings in the target (WS)
        """

        tm_substrs = df[tm_col].apply(self.extract_largest_substring)
        ws_substrs = df[ws_col].str.lower().str.split(" ")
        min_substr_distance = []
        for tm_item, ws_item in zip(tm_substrs, ws_substrs):
            # print(tm_item[0])
            # print(ws_item)
            dst = pd.Series(
                [
                    levenshtein_distance(tm_item[0], ws_substr)
                    for ws_substr in ws_item
                ]
            )
            # print(dst)
            if len(dst) > 0:
                min_dst = dst.min()
            else:
                min_dst = np.inf
            min_substr_distance.append(min_dst)

        df["minimum_substring_distance"] = min_substr_distance
        df = df[df.minimum_substring_distance < self.substring_threshold].copy()
        df = df.sort_values("minimum_substring_distance").drop_duplicates(
            "transfermarkt_team_id"
        )
        return df

    def match_datasets(self):
        """
        For those that have not been matched through players' latest teams, perform fuzzy string matching.

        Logic:
        1. Take a loot at players' latest teams in TM and WS
        2. Compare the date joined in TM and in WS--If join date is +- 15 days keep the data point
        3. Compute the distance of the largest TM substring to WS substring in merged latest teams
            a. If similarity is above threshold, the team is matched
            b. If there are multiple similar teams above threshold, take the highest similarity one
            b. If not, that instance is incorrect
        4. Save the matched teams into database
        5. For the unmatched teams (i.e. ones that have not been saved to db), run fuzzy matching
        6. Append the matches into same table
        """
        start = time()
        rel_cols = [
            "teamId",
            "transfermarkt_team_id",
            "officialName",
            "transfermarkt_team_name_full",
            "youth_team",
            "under_team",
            "second_team",
        ]

        # df = self.compare_tm_ws_teams(self.merged_df, 'transfermarkt_team_name_full', 'officialName')
        self.merged_df = self.clean_team_names(self.merged_df, "officialName")
        self.merged_df = self.merged_df.drop(columns="index")
        fast_write_sql(
            self.merged_df, "tm_to_ws_team_ids", self.cnx, if_exists="replace"
        )
        self.cnx.execute(
            "grant all on tm_to_ws_team_ids TO kliment, elvan, ivan_dimitrov,"
            " airflow, playervaluation"
        )

        self.ws_teams = self.clean_team_names(
            self.ws_teams, "officialName"
        ).drop(columns="index")
        self.tm_teams = self.clean_team_names(self.tm_teams, "team_name").drop(
            columns="index"
        )

        tm_unique = self.tm_teams.drop_duplicates(
            subset=["team_id"]
        ).reset_index()
        ws_unique = self.ws_teams.drop_duplicates(
            subset=["teamId"]
        ).reset_index()
        merged = pd.DataFrame(columns=self.merged_df.columns)

        print(
            f"{self.merged_df.shape[0]} teams matched and written to DB based"
            " on players current teams"
        )
        print("Starting matching of outstanding teams")
        for i, row in tm_unique.iterrows():
            if i == 100:
                print(time() - start)

            # just not gonna match dupes, there are very few and we can sort by hand if necessary
            name = row["clean_names"]
            merged.loc[i, "transfermarkt_team_id"] = row["team_id"]
            merged.loc[i, "transfermarkt_team_name_full"] = row["team_name"]
            merged.loc[i, "youth_team"] = row["youth_team"]
            merged.loc[i, "under_team"] = row["under_team"]
            merged.loc[i, "second_team"] = row["second_team"]
            merged.loc[i, "clean_names"] = name

            # Make sure to use only relevant teams
            if (row["youth_team"] == 1 & row["second_team"] == 1) | (
                row["under_team"] == 1 & row["second_team"] == 1
            ):
                row["second_team"] = 0

            ws_subset = ws_unique[
                (ws_unique["youth_team"] == row["youth_team"])
                & (ws_unique["under_team"] == row["under_team"])
                & (ws_unique["second_team"] == row["second_team"])
            ].copy()

            if (
                len(tm_unique[tm_unique["clean_names"] == name]) == 1
                and len(ws_subset[ws_subset["clean_names"] == name]) == 1
            ):
                merged.loc[i, "teamId"] = (
                    ws_subset[ws_subset.clean_names == name]
                    .reset_index()
                    .teamId[0]
                )
                merged.loc[i, "officialName"] = (
                    ws_subset[ws_subset.clean_names == name]
                    .reset_index()
                    .officialName[0]
                )
                # merged.loc[i, 'str_similarity'] = 0
            else:
                fuzzy_match = process.extractOne(name, ws_subset["clean_names"])
                print(f"Matching score: {fuzzy_match[1]}")
                if (
                    fuzzy_match[1] > self.match_threshold
                    and ws_subset[ws_subset["clean_names"] == fuzzy_match[0]][
                        "teamId"
                    ].nunique()
                    == 1
                ):
                    merged.loc[i, "teamId"] = ws_subset[
                        ws_subset["clean_names"] == fuzzy_match[0]
                    ].reset_index()["teamId"][0]
                    merged.loc[i, "officialName"] = ws_subset[
                        ws_subset["clean_names"] == fuzzy_match[0]
                    ].reset_index()["officialName"][0]
                    # merged.loc[i, 'str_similarity'] = fuzzy_match[1]

                else:
                    merged.loc[i, "teamId"] = np.nan
                    merged.loc[i, "officialName"] = np.nan

            if ((i % 1000 == 0) and (i > 0)) or i == (tm_unique.shape[0] - 1):
                # print(name)
                print(f"{i}th Iteration complete, writing to DB")
                merged = merged.dropna(subset=["teamId"])
                fast_write_sql(
                    merged, "tm_to_ws_team_ids", self.cnx, if_exists="append"
                )
                merged = pd.DataFrame(columns=rel_cols)
        print(time() - start)

    def match_youth_to_all(self):
        pass


def main():

    cnx = create_engine(postgres_prod_str)
    TNM = TeamNameMatcher(90, 3, cnx)
    TNM.read_data()
    TNM.find_abbreviations()
    merged_df = TNM.match_datasets()
    # fast_write_sql(merged_df, 'tm_to_ws_team_ids', cnx, if_exists='append')

    # write_data(merged_df)
    # return merged_df


if __name__ == "__main__":
    main()
