import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from datetime import datetime

cnx = create_engine(postgres_prod_str)
pd.set_option('max_columns', None)

# Dates you want to get close values for
dates = [
    '2020-01-01',
    '2020-07-01',
    '2021-01-01',
    '2021-07-01',
    '2022-01-01',
    '2022-07-01',
    '2023-01-01',
    '2023-03-30'
]

# Convert the date strings to datetime objects
date_objs = [datetime.strptime(date, '%Y-%m-%d').date() for date in dates]

# Define the query
query = """
select td2."name", pvp.date as date_collected, pvp."values" as market_value from player_valuation.player_value_predictions pvp
join transfermarkt.transfermarkt_data td2 on td2."playerId" = pvp."playerId" 
where tm_value_used = true and root_transformed = true
and pvp."playerId" in (select "playerId" from transfermarkt.transfermarkt_data td where td.player_url in ('https://www.transfermarkt.com/stijn-spierings/profil/spieler/241501', 'https://www.transfermarkt.com/goncalo-ramos/profil/spieler/550550', 'https://www.transfermarkt.com/goncalo-borges/profil/spieler/479649',
'https://www.transfermarkt.com/johann-lepenant/profil/spieler/585973', 'https://www.transfermarkt.com/ze-leite/profil/spieler/468338',
'https://www.transfermarkt.com/hugo-siquet/profil/spieler/578680', 'https://www.transfermarkt.com/richard-odada/profil/spieler/675238', 'https://www.transfermarkt.com/zidan-sertdemir/profil/spieler/788445',
'https://www.transfermarkt.com/martin-smolenski/profil/spieler/653707', 'https://www.transfermarkt.com/anton-nedyalkov/profil/spieler/218441', 'https://www.transfermarkt.com/andrej-bajovi%%C4%%87/profil/spieler/724662',
'https://www.transfermarkt.com/abdul-yusif/profil/spieler/543494')) and date >= '2020-01-01' and date <= '2023-03-30'"""

# Fetch the data from the database
with cnx.connect() as connection:
    result = connection.execute(query)
    rows = result.fetchall()

# Create a DataFrame from the fetched data
data = pd.DataFrame(rows, columns=['name', 'date_collected', 'market_value'])

# Create a DataFrame to store the final data
result_df = pd.DataFrame(index=dates)

# Iterate over each name
for name in data['name'].unique():
    temp_df = data[data['name'] == name].copy()
    name_data = []
    
    
    # Find the closest previous date for each target date
    for date_obj in date_objs:
        previous_dates = temp_df[pd.to_datetime(temp_df['date_collected']) <= pd.to_datetime(date_obj)]
        if not previous_dates.empty:
            closest_previous_date = previous_dates.iloc[(pd.to_datetime(previous_dates['date_collected']) - pd.to_datetime(date_obj)).abs().argsort()[:1]]
            name_data.append(closest_previous_date.iloc[0]['market_value'])
        else:
            # If there is no previous date, find the closest future date
            future_dates = temp_df[pd.to_datetime(temp_df['date_collected']) > pd.to_datetime(date_obj)]
            if not future_dates.empty:
                closest_future_date = future_dates.iloc[(pd.to_datetime(future_dates['date_collected']) - pd.to_datetime(date_obj)).abs().argsort()[:1]]
                name_data.append(closest_future_date.iloc[0]['market_value'])
            else:
                name_data.append(None)

    result_df[name] = name_data

# Reset index name
result_df.index.name = 'date'

# Print the result DataFrame
print(result_df)

result_df.to_csv('prediction_values.csv',  index=False)