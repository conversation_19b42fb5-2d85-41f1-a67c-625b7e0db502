CREATE OR REPLACE VIEW wyscout.team_info AS
SELECT
	t."teamId",
	t.name,
	t."officialName",
	cs."divisionLevel",
	t.area_name,
	coalesce(tsb.segment, 4) as segment, 
	t.category,
	t.gender,
	tr.smoothed_rating
FROM
	wyscout.teams t
	LEFT JOIN ( SELECT DISTINCT ON (st."teamId")
			*
		FROM
			wyscout.seasons_teams st,
			wyscout.seasons ss,
			wyscout.competitions cs
		WHERE
			st."seasonId" = ss."seasonId"
			AND ss."competitionId" = cs."competitionId"
			AND cs."divisionLevel" > 0
			AND cs.format = 'Domestic league'
			AND ss.active = TRUE
		ORDER BY
			st."teamId",
			ss."endDate" DESC) cs ON (cs."teamId" = t."teamId")
	LEFT JOIN derived_tables.team_segment_bands tsb ON tsb."teamId" = t."teamId"
	LEFT JOIN derived_tables.team_rating tr ON tr."teamId" = t."teamId"
WHERE
	t.gender = 'male'
