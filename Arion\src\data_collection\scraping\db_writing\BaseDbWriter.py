from abc import ABC, abstractmethod
import pandas as pd


class BaseDbWriter(ABC):
    def __init__(self, write_db_table, tm_to_ws_ids_table, file_read_path, cnx):
        self.write_db_table = write_db_table
        self.cnx = cnx
        self.file_read_path = file_read_path
        self.tm_to_ws_ids_table = tm_to_ws_ids_table
        self.df_to_match = None
        self.tm_to_ws_df = None
        self.merged_df = None
        self.dtypes = None

    def read_tm_ws_ids(self):
        self.tm_to_ws_df = pd.read_sql(
            f"""SELECT "playerId", "tm_player_id" 
        FROM {self.tm_to_ws_ids_table}""",
            self.cnx,
        )

    def read_df_to_match(self):
        self.df_to_match = pd.read_csv(self.file_read_path, encoding="utf-16")
        if (
            "player_id" in self.df_to_match.columns
            and "tm_player_id" not in self.df_to_match.columns
        ):
            self.df_to_match = self.df_to_match.rename(
                columns={"player_id": "tm_player_id"}
            )

    def merge_tables(self):
        self.merged_df = pd.merge(
            self.df_to_match, self.tm_to_ws_df, how="inner", on="tm_player_id"
        )

    @abstractmethod
    def prep_for_writing(self):
        # each db writer should specify sqlalchemy column types for new table
        pass

    def write_to_db(self):
        self.merged_df.to_sql(
            self.write_db_table,
            self.cnx,
            index=False,
            if_exists="replace",
            dtype=self.dtypes,
            method="multi",
        )
