from typing import List, Optional

import pandas as pd
from sqlalchemy import create_engine

from src.helper_funcs import fast_read_sql, fast_write_sql
from settings import postgres_prod_str


class MaxMaker:
    def __init__(self, percentile: float):
        self.cnx_prod = create_engine(postgres_prod_str)
        self.table_name: str = 'advanced_stats'
        self.schema: str = "wyscout"
        self.var_list: Optional[List[str]] = None
        self.grouped_df: pd.DataFrame = None
        self.max_df: pd.DataFrame = None
        self.derived_schema: str = "derived_tables"

        self.percentile = percentile
    
    def get_var_names(self):
        df = pd.read_sql(f"""SELECT * from {self.schema}.{self.table_name} limit 1""", self.cnx_prod)
        self.var_list = [x for x in df.columns if x not in {"matchId", "playerId"}]

    def get_grouped_stats(self):
        agg_query = ', '.join(f"""AVG("{x}") AS "{x}" """ for x in self.var_list)
        query = f"""SELECT {agg_query}, "playerId" 
                    FROM {self.schema}.{self.table_name}
                    GROUP BY "playerId"
                    HAVING COUNT(*) > 10 """
        self.grouped_df = fast_read_sql(query, self.cnx_prod)
    
    def calc_maxes(self):
        self.max_df = self.grouped_df.drop(columns=["playerId"]).quantile(self.quantile).reset_index()
        self.max_df.columns = ["variable", "var_999_quantile"]
    
    def write_maxes(self):
        fast_write_sql(self.max_df, "var_999_quantiles", self.cnx_prod, if_exists='replace', schema=self.derived_schema)


def main():
    mm = MaxMaker(0.999)    
    mm.get_var_names()
    mm.get_grouped_stats()
    mm.calc_maxes()
    mm.write_maxes()

    
if __name__ == "__main__":
    main()