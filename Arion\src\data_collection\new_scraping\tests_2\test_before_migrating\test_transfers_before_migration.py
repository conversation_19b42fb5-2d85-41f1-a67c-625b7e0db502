import unittest
import pandas as pd
from sqlalchemy.engine import create_engine

from src.helper_funcs import fast_read_sql


class TestTransferBeforeMigration(unittest.TestCase):
    engine = create_engine(
        "***********************************************************/wyscout_raw_production"
    )
    with engine.begin() as con:
        df = fast_read_sql(
            """Select * from meta_scraping.raw_transfers_table""", engine
        )

    # def test_transfer_season(self):
    #    player_ids = [i for i in self.df['season'] if pd.isnull(i)]
    #    self.assertEqual(0, len(player_ids),
    #    f"There are {len(player_ids)} season/s with NULL id/s")

    def test_player_ids(self):
        player_ids = [i for i in self.df["tm_player_id"] if pd.isnull(i)]
        self.assertEqual(
            0,
            len(player_ids),
            f"There are {len(player_ids)} player/s with NULL id/s",
        )
