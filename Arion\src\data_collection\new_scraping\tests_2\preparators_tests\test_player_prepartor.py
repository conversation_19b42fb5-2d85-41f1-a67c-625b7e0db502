import unittest
from src.data_collection.new_scraping.scraping.scrape_tm_player_profiles import (
    PlayerProfilesScraper,
)
from src.data_collection.new_scraping.cleaning.validation import PlayerPrep


class TestScraper(unittest.TestCase):
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like"
            " Gecko) Chrome/47.0.2526.106 Safari/537.36"
        )
    }
    scraper = PlayerProfilesScraper(headers)
    test_ll = [
        "https://www.transfermarkt.com/muhammad-isa/profil/spieler/722691",
        "https://www.transfermarkt.com/andy-carroll/profil/spieler/48066",
        "https://www.transfermarkt.com/pouria-aria-kia/profil/spieler/469925",
    ]
    df = scraper.loop_through_urls(test_ll)
    prep = PlayerPrep()

    def test_player_height(self):
        self.assertEqual(self.prep.clean_df(self.df)["height"][1], "1,93")
        self.assertEqual(self.prep.clean_df(self.df)["height"][0], "1,76")
