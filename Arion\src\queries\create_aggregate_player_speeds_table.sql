DROP TABLE IF EXISTS player_speed_rating;

CREATE TABLE player_speed_rating AS
SELECT
    "playerId",
    percentile_cont(0.5) WITHIN GROUP (
        ORDER BY
            "model_speed"
    ) AS "median_speed",
    COUNT('speed') as "speed_count"
FROM
    player_model_speeds
WHERE
    TO_TIMESTAMP("date", 'YYYY-MM-DD HH24:MI:SS') > '2021-02-12'::date - interval '2 years'
GROUP BY
    "playerId"
HAVING
    COUNT("speed") > 7;

ALTER TABLE
    player_speed_rating
ADD
    COLUMN speed_rating INTEGER;

UPDATE
    player_speed_rating
SET
    "speed_rating" = quantiles.speed_rating
FROM
    (
        SELECT
            "playerId",
            median_speed,
            NTILE(20) OVER(
                ORDER BY
                    median_speed
            ) as speed_rating
        from
            player_speed_rating
    ) quantiles
WHERE
    player_speed_rating."playerId" = quantiles."playerId";

GRANT ALL ON player_speed_rating TO elvan, kliment, airflow;
