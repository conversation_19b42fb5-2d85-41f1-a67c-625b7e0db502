from sqlalchemy.engine import create_engine
from sqlalchemy.types import Date

from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import PlayerPrep
from src.helper_funcs import fast_read_sql
from src.helper_funcs import fast_write_sql


engine = create_engine(postgres_prod_str)
SCHEMA = "meta_scraping"


def main():
    prep = PlayerPrep()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.raw_data", engine)

    try:
        fast_write_sql(
            prep.clean_df(df),
            "player_migration_table",
            cnx=engine,
            if_exists="replace",
            schema=SCHEMA,
            dtype={x: Date for x in prep.DATE_COLUMNS},
        )
        engine.execute(
            """ALTER TABLE meta_scraping.player_migration_table add PRIMARY KEY ("playerId") """
        )
    except Exception as e:
        raise (e)


if __name__ == "__main__":
    main()
