from sqlalchemy import engine
import sqlalchemy
from sqlalchemy.engine.base import Engine
from src.data_collection.new_scraping.scraping.father_scraper import Scraper
from src.data_collection.new_scraping.cleaning.validation import Prep
import asyncio
from src.helper_funcs import fast_write_sql

# Orchestrator of the whole process
# He will handle the incoming data from the scrapper,
# Validating and preparing data
# Writing to the migration table
# Tracks progress of the already collected and to be collected players


class Orchestrator:
    def __init__(
        self,
        initial_table,
        prog_table,
        migr_table,
        exis_table,
        failed_table,
        raw_data_table,
        transfers_table,
        agent_history_table,
        engine,
        scraper: Scraper,
    ):
        # SQL Alchemy
        self.engine = engine

        # Tables
        self.initial_table = initial_table
        self.prog_table = prog_table
        self.migr_table = migr_table
        self.exis_table = exis_table
        self.failed_table = failed_table
        self.raw_table = raw_data_table
        self.transfers_table = transfers_table
        self.agent_history_table = agent_history_table

        # Scraper
        self.scraper = scraper()

    # SQL task populate the progress table with the data from the initial table

    # Get the IDs of the data you want to scrape for example: Teams, Players etc.
    def get_ids_from_progress(self, limit, id):
        query = (
            f"SELECT {id} FROM {self.dbschema}.{self.prog_table} LIMIT {limit}"
        )
        # Select the players IDs
        self.ids = [row[0] for row in self.engine.execute(query)]

    # Scrape the data
    def get_df(self):
        # Scrape the players data
        (
            self.profiles_df,
            self.error_ids,
            self.transfer_df,
            self.agent_df,
        ) = asyncio.run(
            self.scraper.loop_through_urls(self.ids)
        )  # players_ids

    # Save to raw table

    # Check progress - Save the new data to migration table and
    def save_scraped_data(self, Prep: Prep, AdditionalPrep=None):
        # Save in the migration table
        prep = Prep()
        add_prep = AdditionalPrep()

        connection = self.engine.raw_connection()
        cursor = connection.cursor()
        try:
            fast_write_sql(
                self.profiles_df,
                self.raw_table,
                cnx=self.engine,
                if_exists="append",
                transaction=True,
                connection=connection,
                cursor=cursor,
            )

            fast_write_sql(
                prep.clean_df(self.profiles_df),
                self.migr_table,
                cnx=self.engine,
                if_exists="append",
                transaction=True,
                connection=connection,
                cursor=cursor,
            )

            fast_write_sql(
                add_prep.clean_df(self.transfer_df),
                self.transfers_table,
                cnx=self.engine,
                if_exists="append",
                transaction=True,
                connection=connection,
                cursor=cursor,
            )

            fast_write_sql(
                self.agent_df,
                self.agent_history_table,
                cnx=self.engine,
                if_exists="append",
                transaction=True,
                connection=connection,
                cursor=cursor,
            )

            # fast_write_sql( self.error_ids, self.failed_table, cnx=self.engine,
            # if_exists="append", transaction=True, connection=connection, cursor=cursor)

            connection.commit()
        except Exception as e:
            raise (e)
            connection.rollback()
        cursor.close()

    # Remove the already scraped IDs from the progress table
    def update_progress(self, prog_id, migr_id, fail_id):  # Can be a SQL task
        # Remove the players in progress table based on the players in the migration table
        query = f"""DELETE FROM {self.prog_table} USING {self.migr_table}
         WHERE {self.prog_table}.{prog_id} = CAST({self.migr_table}.{migr_id} AS int)"""

        query_2 = f"""DELETE FROM {self.prog_table} USING {self.failed_table} WHERE
         {self.prog_table}.{prog_id} = CAST({self.failed_table}.{fail_id} AS INT)"""
        self.engine.execute(query)
        self.engine.execute(query_2)

    # When the migration table is full, move all the data to the existing players table
