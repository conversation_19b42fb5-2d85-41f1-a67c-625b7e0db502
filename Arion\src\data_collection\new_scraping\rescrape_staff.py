# It will be used in a DAG in the future
# Running an example script:
# Collecting data for given players and populating the required tables
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
from src.data_collection.new_scraping.orchestrators.tm_staff_orchestrator import (
    TMStaffOrchestrator,
)
from src.data_collection.new_scraping.scraping.scrape_tm_staff import (
    TmStaffScraper,
)

SCHEMA = "meta_scraping"
PROG_TABLE = "staff_progress_table"
RAW_DATA_TABLE = "raw_staff_data"

engine = create_engine(postgres_prod_str)
# asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def run(limit):
    # Get players IDs from initial table
    orchestrator.get_urls_from_progress(limit, "staff_url")  # Add limit

    # Second
    # Scrape the players data
    orchestrator.get_df()

    # Third
    # Save the collected players data in the raw table.
    orchestrator.save_scraped_data()
    # Update the progress table
    orchestrator.update_progress("staff_url")

    return bool(orchestrator.check_progress())



if __name__ == "__main__":
    orchestrator = TMStaffOrchestrator(
        PROG_TABLE, RAW_DATA_TABLE, engine=engine, scraper=TmStaffScraper
    )

    # Scrape players
    while True:
        if not run(limit=1000):
            break
