import re
import requests
from time import sleep, time
from bs4 import BeautifulSoup
from urllib import request
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt

from settings import PROXIES
from src.helper_funcs import read_config
from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)


@retry(
    wait=wait_random(
        min=scrape_config["sleep"]["on_fail"][0],
        max=scrape_config["sleep"]["on_fail"][1],
    ),
    stop=stop_after_attempt(2),
)
def scrape_league_info(league_url):
    temp_soup = None
    temp_soup = get_soup_from_url(league_url, proxies=PROXIES)
    info_dict = {}
    print(f"{league_url.split('/')[-1]} league loaded")
    if temp_soup:
        if temp_soup.find(attrs={"class": "miniflagge"}) is None:
            return None
        for table in temp_soup.find_all(attrs={"class": "profilheader"}):
            for row in table.find_all("tr"):
                info_dict[row.th.text] = row.td.text
        info_dict["league_country"] = temp_soup.find(
            attrs={"class": "miniflagge"}
        )["title"]
        info_dict["names"] = temp_soup.find(
            attrs={"class": "spielername-profil"}
        ).text
    return info_dict


def scrape_all_leagues(return_df=False):
    start = time()
    continent_list = read_config()["scraping"]["transfermarkt"]["continents"]
    league_info_dict = {}
    for continent in continent_list:
        if (
            continent != "afrika"
        ):  # afrika currently has 1 page so it does not have 'last page' button to infer n_pages
            # we start from page 1 and check how many pages there are
            url_init = f"https://www.transfermarkt.com/wettbewerbe/{continent}?ajax=yw1&page=1"
            init_soup = get_soup_from_url(url_init, proxies=PROXIES)
            pattern = r"\d+"
            n_pages = int(
                re.search(
                    pattern,
                    init_soup.find(attrs={"class": "letzte-seite"})["title"],
                ).group()
            )
        else:
            n_pages = 1

        for i in range(1, n_pages + 1):
            temp_soup = None
            url_i = f"https://www.transfermarkt.com/wettbewerbe/{continent}?ajax=yw1&page={i}"
            temp_soup = get_soup_from_url(url_i, proxies=PROXIES)
            print(f"{i} page(s) done")
            if temp_soup:
                for league in temp_soup.find_all(
                    attrs={"class": "inline-table"}
                ):
                    league_url = BASE + league.find_all("a")[1]["href"]
                    league_code = league_url.split("/")[-1]
                    league_info_dict[league_code] = scrape_league_info(
                        league_url
                    )
                    sleep_random((1, 3))
            df = pd.DataFrame(league_info_dict).T
            df.to_csv("data/raw/scraped_update/league_details.csv")
    print(f"Total time elapsed: {time() - start} seconds.")
    if return_df:
        return df
