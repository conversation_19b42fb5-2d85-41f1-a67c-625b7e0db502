import asyncio
import sys
import os

import pandas as pd
from src.data_collection.wyscout.v2.MatchObjectsOrchestrator import (
    MatchObjectsOrchestrator,
)
from src.data_collection.wyscout.v2.MatchObjectsUpdater import (
    MatchObjectsUpdater,
)
from src.data_collection.wyscout.v2.MatchesUpdatesChecker import (
    MatchesUpdatesChecker,
)

from settings import postgres_prod_str
from sqlalchemy import create_engine


async def main():
    query = open(
        "src/data_collection/wyscout/v2/adv_stats_eda/get_eda_sample_ids.sql"
    ).read()
    cnx = create_engine(postgres_prod_str)
    ids = pd.read_sql(query, cnx)["matchId"].tolist()
    from_scratch = True
    matches_checker = MatchesUpdatesChecker(
        table_name="matches_for_collection_eda",
        object_type="matches",
        id_name="matchId",
        custom_ids_list=ids,
    )
    matches_updater = MatchObjectsUpdater()
    orc = MatchObjectsOrchestrator(
        batch_size=20,
        updates_checker=matches_checker,
        updater=matches_updater,
        from_scratch=from_scratch,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
