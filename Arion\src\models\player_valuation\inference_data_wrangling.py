from pv.data_wrangling_inference import ValueInferenceWrangler
import src.models.player_valuation.utils.database_connect as dbconnect
from src.models.player_valuation.utils.config import *
from src.models.player_valuation.utils.helper_funcs import fast_write_sql

def main():

    cnx_prod = dbconnect.main()
    PV = ValueInferenceWrangler(cnx_prod, num_injury_categories, read_mappings, rank_api_url,
        min_games, min_pct, min_tagged, time_decay, game_q_scaling)
    
    PV.read_fact_tables()
    PV.read_transfer_tables()
    PV.get_historical_player_transfers(wrangling_type = 'scoring')
    PV.read_injuries()
    PV.read_player_area_info()
    PV.read_player_role_pct()
    # PV.get_player_popularity()
    PV.merge_metadata_into_transfers()
    PV.transform_transfers(compare_to_player_appearance_avg = True, write_to_db = False)
    # PV.get_player_performance()
    PV.get_modelling_df(write_to_db=False, table_name='tmp_valuation_df_inference')
    fast_write_sql(PV.modelling_df, 'tmp_valuation_df_inference', 
        cnx_prod, schema='player_valuation')
    PV.clean_feature_families()
    PV.clean_tmp_player_table()

if __name__ == "__main__":
    main()