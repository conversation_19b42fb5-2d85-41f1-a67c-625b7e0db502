import pandas as pd
import numpy as np
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
from src.helper_funcs import fast_write_sql


def change_team_id(x):
    if x.isnumeric():
        return x
    else:
        return np.nan


SCHEMA = "meta_scraping"


def main():
    cnx_prod = create_engine(postgres_prod_str)

    base_url = "https://www.transfermarkt.com/_TEAM_NAME/startseite/verein/_ID"

    df = pd.read_sql(
        f"SELECT left_team_season_transfers_url,"
        f" joined_team_season_transfers_url FROM transfermarkt.tm_transfers",
        cnx_prod,
    )
    urls = np.unique(df.dropna())

    team_dict = {
        "team_url": [
            base_url.replace("_TEAM_NAME", url.split("/")[1]).replace(
                "_ID", url.split("/")[-3]
            )
            for url in urls
        ],
        "team_id": [url.split("/")[-3] for url in urls],
    }
    teams_df = pd.DataFrame(team_dict)
    teams_df["team_id"] = teams_df["team_id"].apply(change_team_id)
    teams_df = teams_df.dropna()
    teams_df = teams_df.drop_duplicates(subset=["team_id"])
    fast_write_sql(
        teams_df,
        "tm_teams_urls",
        cnx_prod,
        schema=SCHEMA,
        if_exists="replace",
    )


if __name__ == "__main__":
    main()
