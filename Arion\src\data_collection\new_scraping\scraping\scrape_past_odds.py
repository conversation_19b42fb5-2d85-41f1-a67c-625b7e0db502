from datetime import datetime
import json
import re
import pandas as pd
from bs4 import BeautifulSoup
import aiohttp
import asyncio
from src.data_collection.new_scraping.scraping.oddsportal_scraper import (
    ScrapeOddsPortal,
)

class ScrapePastOdds(ScrapeOddsPortal):

    def __init__(self):
        super().__init__()

    async def scrape_past_seasons(self, league_url):
        if league_url[-1] == '/':
            league_url = league_url[:-1]
        #seasons = {'2020-2021', '2019-2020', '2018-2019', '2017-2018', '2016-2017'
        #'2015-2016'}
        seasons = {'2016-2017'}
        urls = [f"{league_url}-{season}/results/" for season in seasons]
        #urls.append(f"{league_url}/results/")

        result_ll = []
        for url_ll in urls:
            for i in range(1,9):
                text = self.get_response(url_ll)
                tournament_id = json.loads(re.findall(r"PageTournament\(({.*})\)", text)[0])['id']
                result_ll.append(await self.loop_through_urls(
                f"https://fb.oddsportal.com/ajax-sport-country-tournament-archive/1/{tournament_id}/X0/1/2/{i}/")
            )
        return result_ll

    async def loop_through_urls(self, league_url) -> 'list[pd.DataFrame]':

        ajax_country_archive = self.get_response(league_url)
        table_data = json.loads(re.findall(r"\s({.*})", ajax_country_archive)[0])['d']['html']
        soup = BeautifulSoup(table_data, "html.parser")

        # Getting the ids, names of teams and dates for matches
        match_ids = []
        date = ""
        for tr in soup.find("table", attrs={"id": "tournamentTable"}).find_all("tr"):
            try:
                span = tr.find("span", attrs={"class": "datet"})
                if span is None:
                    raise Exception
                int_timestamp = int(span["class"][1].split("-")[0].split("t")[-1])
                date = datetime.fromtimestamp(
                    int_timestamp
                )
            except Exception as e:
                try:
                    a = tr.find("td", attrs={"class": "table-participant"}).find("a")
                    id = a["href"].split("/")[-2].split("-")[-1]  # Home, Away, match_id
                    home, away = a.text.split(" - ")

                    match_ids.append(
                        (
                            [date, f"https://www.oddsportal.com{a['href']}"],
                            [home, away, id]
                        )
                    )
                except Exception as e:
                    pass

        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for tup in match_ids:
                html_tasks.append(self.extract_data_from_js(tup))
            return await asyncio.gather(*html_tasks)


if __name__ == "__main__":
    pl_odds = ScrapePastOdds()
    df = asyncio.run(
        pl_odds.loop_through_urls(
            "https://www.oddsportal.com/soccer/england/premier-league"
        )
    )
