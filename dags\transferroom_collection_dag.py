from datetime import datetime, timedelta
import schedule
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

workdir = '/home/<USER>/Projects/automated-requests'

dag_params = {
    "dag_id": "transferroom_collection_dag",

    "start_date": datetime(2023, 4, 1),
    "schedule_interval": timedelta(days=14),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    collect_transferroom = BashOperator(
        task_id="collect_transferroom",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/misc/collect_transferroom.py """,
    )

    # src/scoring/group_speeds.py

    match_clubs = PostgresOperator(
        task_id="match_clubs",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/transferroom_team_matching.sql").read(),
    )

    collect_transferroom >> match_clubs