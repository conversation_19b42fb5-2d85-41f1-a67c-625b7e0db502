select
    "matchId"
from
    seasons_matches sm,
    (
        select
            *
        from
            competitions
        where
            gender = 'male'
            and category = 'default'
            and type = 'club'
            and (
                (
                    area_name = 'France'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'France'
                    and "divisionLevel" = 2
                )
                or (
                    area_name = 'France'
                    and "divisionLevel" = 3
                )
                or (
                    area_name = 'Netherlands'
                    and "divisionLevel" = 2
                )
                or (
                    area_name = 'Portugal'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Portugal'
                    and "divisionLevel" = 2
                )
                or (
                    area_name = 'Germany'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'England'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Italy'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Netherlands'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Russia'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Turkey'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Vietnam'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Egypt'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'United States'
                    and "divisionLevel" = 1
                )
                or (
                    area_name = 'Brazil'
                    and "divisionLevel" = 1
                )
            )
    ) comps
where
    sm."competitionId" = comps."competitionId"
    and sm.date > '2021-01-01'