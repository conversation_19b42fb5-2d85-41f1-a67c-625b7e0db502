import json

import pandas as pd
import numpy as np
from typing import Tuple, List

from src.data_collection.wyscout.v2.Updater import Updater


class AdvancedStatsPrepper(Updater):
    def __init__(self, table_name=None):
        super().__init__(table_name)

    @staticmethod
    def derive_additional_logic(df: pd.DataFrame) -> pd.DataFrame:
        """Derive a set of pieces of logic to be reused by different vars

        Args:
            df (pd.DataFrame): df

        Returns:
            pd.DataFrame: df with derived logic
        """

        df["centrality_y"] = abs(50 - df["location_y"]) * 2

        df["own_third"] = np.where(df["location_x"].between(1, 33), 1, 0)
        df["midfield"] = np.where(df["location_x"].between(34, 66), 1, 0)
        df["final_third"] = np.where(df["location_x"].between(67, 99), 1, 0)
        df["opponet_half"] = np.where(df["location_x"].between(50, 99), 1, 0)
        df["y_central"] = np.where(df["location_y"].between(19, 81), 1, 0)
        df["opponent_penalty"] = np.where(
            (df["location_x"].between(84, 100)) & df["y_central"] == 1, 1, 0
        )
        df["wing_shot"] = np.where(
            (df["location_x"].between(74, 94))
            & (
                df["location_y"].between(63, 99)
                | df["location_y"].between(1, 37)
            ),
            1,
            0,
        )
        df["pass_final_third"] = np.where(
            df["pass_endLocation_x"].between(67, 99), 1, 0
        )
        df["inplay"] = np.where(
            df["type_primary"].isin(
                ["free_kick", "throw_in", "penalty", "corner"]
            ),
            0,
            1,
        )

        df["long_aerial_pass"] = np.where(
            (df["pass_height"] == "high") & (df["pass_length"] > 25), 1, 0
        )
        df["long_aerial_diagonal_pass"] = np.where(
            df["pass_angle"].abs().between(15, 75)
            & (df["long_aerial_pass"] == 1),
            1,
            0,
        )
        # kept possession is defined as next event is by a teammate
        # and it is not a duel, interruption, infraction, or offide
        # OR if the next event is ground duel the teammate is on offense,
        # i.e. he has received the ball before being challenged
        df["kept_possession"] = np.where(
            (
                (df["team_id_next_1"] == df["team_id"])
                & (
                    ~df["type_primary_next_1"].isin(
                        ["duel", "game_interruption", "infraction", "offside"]
                    )
                )
            )
            | (
                (df["type_primary_next_1"] == "duel")
                & (
                    (df["team_id_next_1"] == df["team_id"])
                    & (
                        df["type_secondary_next_1"].str.contains(
                            "offensive_duel"
                        )
                    )
                )
            )
            | (
                (
                    df["team_id_next_1"] == df["opponentTeam_id"]
                )  # NOTE we specify opponent team instead != team because it can be also nan
                & (df["type_secondary_next_1"].str.contains("defensive_duel"))
            ),
            1,
            0,
        )
        df["successful_long_aerial_pass"] = np.where(
            (df["long_aerial_pass"] == 1) & (df["kept_possession"] == 1),
            1,
            0,
        )
        df["interceptions"] = np.where(
            (df.type_primary == "interception")
            | (df.type_secondary.str.contains("interception")),
            1,
            0,
        )
        df["danger_zone_next_1"] = np.where(
            (
                (df["location_x_next_1"].between(84, 94))
                & (df["location_y_next_1"].between(19, 81))
            )
            | (
                (df["location_x_next_1"].between(84, 100))
                & (df["location_y_next_1"].between(37, 63))
            ),
            1,
            0,
        )
        return df

    @staticmethod
    def shift_vars(df: pd.DataFrame, shift_dict: dict) -> pd.DataFrame:
        """Creates columns shifted forwards, backwards

        Args:
            df (pd.DataFrame): input df
            shift_dict (dict): dictionary of the type {var: [list of shifts, e.g. 1,-1,2]}

        Returns:
            pd.DataFrame: same df changed in place with additional columns
        """

        def custom_shift(
            df: pd.DataFrame, var_name: str, periods: int
        ) -> pd.Series:
            """Handles cases where a duel has two counterparts so if we want
            real values of previous event, we need to shift two events back
            so that we dont end up with situations where shift(1) on the second
            event in duel results in the first duel counterpart, which is not
            the correct previous event. Note this logic works for shifting all the way up to 2 events fwd/bck,
            if we want more, the logic needs to be adjusted
            Args:
                df (pd.DataFrame): [description]
                var_name (str): name of the variable we want to shift
                periods (int):  period we want to shift

            Returns:
                pd.Series: [description]
            """
            if abs(periods) > 2:
                raise ValueError("Currently only lags up to 2 are supported")
            if periods == 0:
                return df[var_name]
            else:
                shift_ref = 1 if periods > 0 else -1
            if abs(periods) == 1:
                return np.where(
                    df["matchTimestamp"].shift(shift_ref)
                    != df["matchTimestamp"],
                    df[var_name].shift(periods),
                    df[var_name].shift((abs(periods) + 1) * shift_ref),
                )
            else:
                return np.where(
                    (
                        df["matchTimestamp"].shift(shift_ref)
                        != df["matchTimestamp"]
                    )
                    & (
                        df["matchTimestamp"].shift(shift_ref * 2)
                        != df["matchTimestamp"].shift(shift_ref)
                    ),
                    df[var_name].shift(periods),
                    np.where(
                        (
                            df["matchTimestamp"].shift(shift_ref)
                            == df["matchTimestamp"]
                        )
                        & (
                            df["matchTimestamp"].shift(shift_ref * 2)
                            == df["matchTimestamp"].shift(shift_ref * 3)
                        ),
                        df[var_name].shift((abs(periods) + 2) * shift_ref),
                        df[var_name].shift((abs(periods) + 1) * shift_ref),
                    ),
                )

        for col, lags in shift_dict.items():
            for lag in lags:
                var_name = (
                    f"{col}_next_{abs(lag)}" if lag < 0 else f"{col}_prev_{lag}"
                )
                # df[var_name] = df[col].shift(lag)
                df[var_name] = custom_shift(df, col, lag)
        return df

    @staticmethod
    def derive_vars(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[dict]]:
        simple_primary_events = [
            "pass",
            # "interception", no interception from primary, since they exist in secondary tags as well
            # and we are deriving custom logic
            "shot",
            "acceleration",
            "goalkeeper_exit",
            "shot_against",
            "free_kick",
            "corner",
            "penalty",
        ]

        simple_secondary_events = [
            "back_pass",
            "hand_pass",
            "head_pass",
            "lateral_pass",
            "forward_pass",
            "linkup_play",
            "progressive_pass",
            "through_pass",
            "pass_to_final_third",
            "pass_to_penalty_area",
            "long_pass",
            "cross",
            "ground_duel",
            "loose_ball_duel",
            "offensive_duel",
            "dribble",
            "dribbled_past_attempt",
            "free_kick_cross",
            "free_kick_shot",
            "counterpressing_recovery",
            "foul",
            "foul_suffered",
            "penalty_conceded_goal",
            "penalty_foul",
            "recovery",
            "shot_block",
            "conceded_goal",
            "save",
            "save_with_reflex",
            "goal",
            "penalty_goal",
            "head_shot",
            "shot_after_corner",
            "shot_after_free_kick",
            "shot_after_throw_in",
            "assist",
            "second_assist",
            "shot_assist",
            "third_assist",
            "loss",
            "progressive_run",
            "yellow_card",
            "red_card",
        ]

        shots = [
            {
                "name": "shots_on_target",
                "condition": df["shot_onTarget"] & True,
            },
            {
                "name": "shots_outside_penalty",
                "condition": (df["type_primary"] == "shot")
                & (df["opponent_penalty"] == 0),
            },
            {
                "name": "shots_inside_penalty",
                "condition": (df["type_primary"] == "shot")
                & (df["opponent_penalty"] == 1),
            },
            {
                "name": "left_foot_shots",
                "condition": (df["shot_bodyPart"] == "left_foot"),
            },
            {
                "name": "right_foot_shots",
                "condition": (df["shot_bodyPart"] == "right_foot"),
            },
            {
                "name": "shots_from_the_wing",
                "condition": (df["wing_shot"] == 1)
                & (df["type_primary"] == "shot"),
            },
            {
                "name": "shots_after_cross",
                "condition": (
                    df["type_secondary_prev_1"].str.contains("'cross'")
                )
                & (df["type_primary"] == "shot"),
            },
            {
                "name": "shots_after_set_piece",
                "condition": (df["inplay_prev_1"] == 0)
                & (df["type_primary"] == "shot"),
            },
            {
                "name": "shots_after_inplay_cross",
                "condition": (df["inplay_prev_1"] == 1)
                & (df["type_primary"] == "shot")
                & (df["type_secondary_prev_1"].str.contains("'cross'")),
            },
            {
                "name": "shots_after_through_pass",
                "condition": (df["type_primary"] == "shot")
                & (df["type_secondary_prev_1"].str.contains("through_pass")),
            },
        ]
        passes = [
            {
                "name": "ground_forward_passes_final_third",
                "condition": (
                    df["type_secondary"].str.contains("'forward_pass'")
                )
                & (df["pass_final_third"] == 1)
                & (df["pass_height"] != "high"),
            },
            {
                "name": "forward_ground_short_passes_opposition_half",
                "condition": (
                    df["type_secondary"].str.contains("'forward_pass'")
                )
                & (df["pass_endLocation_x"] > 50)
                & (df["pass_height"] != "high")
                & (df["pass_length"] <= 10),
            },
        ]
        assists = [
            {
                "name": "shot_assists_penalty_area",
                "condition": (df["type_secondary"].str.contains("shot_assist"))
                & (df["opponent_penalty"] == 1),
            },
            {
                "name": "inplay_shot_assists",
                "condition": (df["inplay"] == 1)
                & (df["type_secondary"].str.contains("shot_assist")),
            },
            {
                "name": "set_piece_shot_assists",
                "condition": (df["inplay"] == 0)
                & (df["type_secondary"].str.contains("shot_assist")),
            },
            {
                "name": "second_shot_assist",
                "condition": (
                    df["type_secondary_next_1"].str.contains("shot_assist")
                )
                & (df["type_primary"] == "pass")
                & (df["team_id"] == df["team_id_next_1"]),
            },
            {
                "name": "third_shot_assist",
                "condition": (
                    df["type_secondary_next_2"].str.contains("shot_assist")
                )
                & (df["type_primary"] == "pass")
                & (df["type_primary_next_1"] == "pass")
                & (df["team_id"] == df["team_id_next_1"])
                & (df["team_id"] == df["team_id_next_2"]),
            },
        ]

        defensive_duels = [
            {
                "name": "defensive_duels",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                ),
            },
            {
                "name": "defensive_duels_own_third",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["own_third"] == 1),
            },
            {
                "name": "defensive_duels_midfield",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["midfield"] == 1),
            },
            {
                "name": "defensive_duels_final_third",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["final_third"] == 1),
            },
            {
                "name": "defensive_duels_central_own_third",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["own_third"] == 1)
                & (df["y_central"] == 1),
            },
            {
                "name": "defensive_duels_wing_own_third",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["own_third"] == 1)
                & (df["y_central"] == 0),
            },
            {
                "name": "defensive_duels_after_throw_in",
                "condition": (
                    df["type_secondary"].str.contains("defensive_duel")
                )
                & (df["type_primary_prev_1"] == "throw_in"),
            },
        ]
        aerial_duels = [
            {
                "name": "aerial_duels",
                "condition": (df["type_secondary"].str.contains("aerial_duel")),
            },
            {
                "name": "aerial_duels_own_third",
                "condition": (df["type_secondary"].str.contains("aerial_duel"))
                & (df["own_third"] == 1),
            },
            {
                "name": "aerial_duels_midfield",
                "condition": (df["type_secondary"].str.contains("aerial_duel"))
                & (df["midfield"] == 1),
            },
            {
                "name": "aerial_duels_final_third",
                "condition": (df["type_secondary"].str.contains("aerial_duel"))
                & (df["final_third"] == 1),
            },
        ]
        other_duels = [
            {
                "name": "defensive_duels_stopped_progress",
                "condition": (df["groundDuel_stoppedProgress"] & True),
            }
        ]
        gk = [
            {
                "name": "gk_free_kick_saves",
                "condition": (df["type_secondary"].str.contains("save"))
                & (
                    df["type_secondary_prev_1"].str.contains("'free_kick_shot'")
                ),
            },
            {
                "name": "gk_penalty_saves",
                "condition": (df["type_secondary"].str.contains("save"))
                & (df["type_primary_prev_1"] == "penalty"),
            },
            {
                "name": "gk_saves_inplay_inside_penalty",
                "condition": (df["type_secondary"].str.contains("save"))
                & (df["type_primary_prev_1"] == "shot")
                & (df["opponent_penalty_prev_1"] == 1)
                & (df["inplay_prev_1"] == 1),
            },
            {
                "name": "gk_saves_inplay_outside_penalty",
                "condition": (df["type_secondary"].str.contains("save"))
                & (df["type_primary_prev_1"] == "shot")
                & (df["opponent_penalty_prev_1"] == 0)
                & (df["inplay_prev_1"] == 1),
            },
            {
                "name": "gk_bad_saves",
                "condition": (df["type_secondary"].str.contains("save"))
                & (df["danger_zone_next_1"] == 1)
                & (~df["type_secondary"].str.contains("ball_out")),
            },
        ]

        long_aerial_passes = [
            {
                "name": "long_aerial_backward_pass",
                "condition": (df["long_aerial_pass"] == 1)
                & (df["type_secondary"].str.contains("'back_pass'")),
            },
            {
                "name": "long_aerial_lateral_pass",
                "condition": (df["long_aerial_pass"] == 1)
                & (df["type_secondary"].str.contains("'lateral_pass'")),
            },
            {
                "name": "long_aerial_diagonal_passes_opposition_half",
                "condition": (df["long_aerial_diagonal_pass"] == 1)
                & (df["location_x"] > 50),
            },
            {
                "name": "long_aerial_diagonal_passes_final_third",
                "condition": (df["long_aerial_diagonal_pass"] == 1)
                & (df["final_third"] == 1),
            },
            {
                "name": "long_aerial_passes",
                "condition": (df["long_aerial_pass"] == 1),
            },
            {
                "name": "long_aerial_passes_not_clearance",
                "condition": (df["long_aerial_pass"] == 1)
                & (df["type_primary"] != "clearance")
                & (df["possession_eventIndex"] > 0),
            },
        ]
        other = [
            # {"name": "interceptions", "condition": df["interceptions"] == 1},
            {
                "name": "interception_accurate_passes",
                "condition": (df["interceptions"] == 1)
                & (
                    (df["pass_accurate"] & True)
                    | (
                        (df["player_id"] == df["player_id_next_1"])
                        & (df["pass_accurate_next_1"] & True)
                    )
                ),
            },
            {
                "name": "duel_fouls",
                "condition": (df["infraction_type"] == "regular_foul"),
            },
            {
                "name": "missed_penalties",
                "condition": (df["type_primary"] == "penalty")
                & ~(
                    df["shot_onTarget"] & True
                ),  # since the columns is already true/false/nan we use kleene logical operators to subset
            },
            {
                "name": "inplay_ground_interceptions",
                "condition": (df["inplay_prev_1"] == 1)
                & (df["interceptions"] == 1)
                & (df["pass_height_prev_1"] != "high"),
            },
            {
                "name": "inplay_midfield_ground_interceptions",
                "condition": (df["inplay_prev_1"] == 1)
                & (df["interceptions"] == 1)
                & (df["pass_height_prev_1"] != "high")
                & (df["midfield"] == 1),
            },
            {
                "name": "inplay_final_third_ground_interceptions",
                "condition": (df["inplay_prev_1"] == 1)
                & (df["interceptions"] == 1)
                & (df["pass_height_prev_1"] != "high")
                & (df["final_third"] == 1),
            },
            {
                "name": "fouls_leading_to_direct_goal",
                "condition": (df["type_secondary"].str.contains("'foul'"))
                & (
                    (df["type_secondary_next_1"].str.contains("free_kick_shot"))
                    & (df["type_secondary_next_1"].str.contains("goal"))
                    | (df["type_secondary_next_1"].str.contains("penalty_goal"))
                ),
            },
            {
                "name": "active_recoveries",
                "condition": (df["type_secondary"].str.contains("recovery"))
                & (df["type_primary"].isin(["duel", "interceptions"])),
            },
            {
                "name": "positional_recoveries",
                "condition": (df["type_secondary"].str.contains("recovery"))
                & (~df["type_primary"].isin(["duel", "interceptions"])),
            },
            {
                "name": "non_clearance_interceptions",
                "condition": (df["interceptions"] == 1)
                & (df["kept_possession"] == 1),
            },
            {
                "name": "actions_under_pressure",
                "condition": (
                    df["type_secondary"].str.contains("under_pressure")
                )
                & (df["type_primary"].isin(["duel", "pass", "shot"])),
            },
            {
                "name": "successful_actions_under_pressure",
                "condition": (
                    df["type_secondary"].str.contains("under_pressure")
                )
                & (
                    (df["pass_accurate"] & True)
                    | (df["shot_onTarget"] & True)
                    | (df["groundDuel_recoveredPossession"] & True)
                    | (df["aerialDuel_firstTouch"] & True)
                    | (df["groundDuel_progressedWithBall"] & True)
                ),
            },
            {
                "name": "aerial_recoveries",
                "condition": (df["type_secondary"].str.contains("recovery"))
                & (df["pass_height_prev_1"] == "high"),
            },
            {
                "name": "ground_recoveries",
                "condition": (df["type_secondary"].str.contains("recovery"))
                & (df["pass_height_prev_1"] != "high"),
            },
            {
                "name": "reactions_after_loss",
                "condition": (
                    (
                        (df.player_id_prev_1 == df.player_id)
                        & (df.type_secondary_prev_1.str.contains("loss"))
                    )
                    | (df.player_id_prev_2 == df.player_id)
                    & (df.type_secondary_prev_2.str.contains("loss"))
                )
                & (df.type_primary == "duel"),
            },
            {
                "name": "successful_dribbles",
                "condition": (df["type_secondary"].str.contains("'dribble'"))
                & (df["groundDuel_progressedWithBall"] & True),
            },
            {
                "name": "dribbles_no_foul",
                "condition": (df["type_secondary"].str.contains("'dribble'"))
                & (~df["type_secondary"].str.contains("foul_suffered")),
            },
            {
                "name": "successful_dribbles_no_foul",
                "condition": (df["type_secondary"].str.contains("'dribble'"))
                & (df["groundDuel_progressedWithBall"] & True)
                & (~df["type_secondary"].str.contains("foul_suffered")),
            },
            {
                "name": "blocked_crosses",
                "condition": (df["interceptions"] == 1)
                & (df["type_secondary_prev_1"].str.contains("cross_blocked")),
            },
            {
                "name": "offensive_duels_progressed_with_ball",
                "condition": (
                    df["type_secondary"].str.contains("'offensive_duel'")
                )
                & (df["groundDuel_progressedWithBall"] & True),
            },
            {
                "name": "offensive_duels_kept_possession",
                "condition": (
                    df["type_secondary"].str.contains("'offensive_duel'")
                )
                & (df["groundDuel_keptPossession"] & True),
            },
            {
                "name": "opponent_half_recoveries",
                "condition": (df["type_secondary"].str.contains("'recovery'"))
                & (df["opponet_half"] == 1),
            },
            {
                "name": "direct_red_card",
                "condition": (df["type_secondary"].str.contains("'red_card'"))
                & (~df["type_secondary"].str.contains("'yellow_card'")),
            },
        ]

        derivations = (
            shots
            + assists
            + passes
            + other_duels
            + aerial_duels
            + defensive_duels
            + gk
            + other
            + long_aerial_passes
        )

        def make_plural(x):
            if x.startswith("save") or x == "shot_against":
                x = "gk_" + x
            corner_cases = [
                "pass_to_final_third",
                "pass_to_penalty_area",
                "foul_suffered",
                "shot_block",
                "cross_block",
                "save_with_reflex",
                "shot_after_corner",
                "shot_after_free_kick",
                "shot_after_throw_in",
                "gk_shot_against",
            ]
            if x in corner_cases:
                return (
                    x.replace("pass", "passes")
                    .replace("foul", "fouls")
                    .replace("shot", "shots")
                    .replace("save", "saves")
                )

            else:
                return (
                    x + "es"
                    if x.endswith("s")
                    else x.replace("ry", "ries")
                    if x.endswith("ry")
                    else x + "s"
                )

        for simple_secondary in simple_secondary_events:
            derivations.append(
                {
                    "name": make_plural(simple_secondary),
                    "condition": df["type_secondary"].str.contains(
                        f"'{simple_secondary}'"
                    ),
                }
            )

        for simple_primary in simple_primary_events:
            
            derivations.append(
                {
                    "name": make_plural(simple_primary),
                    "condition": df["type_primary"] == simple_primary,
                }
            )

        # first we do the overall vars and then we do success ones since they depend on the overall vars
        # the idea is to skip some logical checks and check the var directly
        derived_vars_df = pd.concat([pd.Series(data=np.where(var["condition"], 1, 0), name=var["name"]) for var in derivations], axis=1)
        df = pd.concat([df, derived_vars_df], axis=1)
        # for var in derivations:
        #     df[var["name"]] = np.where(var["condition"], 1, 0)

        accurate_passes = [
            {
                "name": f"successful_{x['name']}",
                "condition": (df[x["name"]] == 1)
                & (df["pass_accurate"] & True),
            } if x['name'] != 'passes' else {
                "name": "successful_passes",
                "condition": df["pass_accurate"] }
            for x in passes 
        ] + [
            {
                "name": f"successful_{make_plural(x)}",
                "condition": df["type_secondary"].str.contains(f"'{x}'")
                & (df["pass_accurate"] & True),
            } if make_plural(x) != 'passes' else {
                "name": "successful_passes",
                "condition": (df['type_primary'] == 'pass') & (df["pass_accurate"]) }
            for x in simple_secondary_events + simple_primary_events
            if "pass" in x.lower() or "cross" in x.lower()
        ] 
        # + [{
        #     "name": "successful_passes_all",
        #     "condition": df["pass_accurate"]
        # }]
        accurate_long_aerial_passes = [
            {
                "name": f"successful_{x['name']}",
                "condition": (df[x["name"]] == 1)
                & (df["successful_long_aerial_pass"] == 1),
            }
            for x in long_aerial_passes
        ]
        shots_on_target = [
            {
                "name": f"{x['name']}_on_target",
                "condition": (df[x["name"]] == 1)
                & (df["shot_onTarget"] & True),
            }
            for x in shots
            if "on_target" not in x["name"]
        ] + [
            {
                "name": f"{make_plural(x)}_on_target",
                "condition": df["type_secondary"].str.contains(f"'{x}'")
                & (df["shot_onTarget"] & True),
            }
            for x in [
                "free_kick_shot",
                "head_shot",
                "shot_after_corner",
                "shot_after_free_kick",
                "shot_after_throw_in",
            ]
        ]

        defensive_duels_won = [
            {
                "name": x["name"].replace("duels", "duels_won"),
                "condition": (df[x["name"]] == 1)
                & ((df["groundDuel_recoveredPossession"] | df["groundDuel_stoppedProgress"]) & True),
            }
            for x in defensive_duels
        ]

        aerial_duels_won = [
            {
                "name": x["name"].replace("duels", "duels_won"),
                "condition": (df[x["name"]] == 1)
                & (df["aerialDuel_firstTouch"] & True),
            }
            for x in aerial_duels
        ]

        success_vars = (
            accurate_passes
            + shots_on_target
            + defensive_duels_won
            + aerial_duels_won
            + accurate_long_aerial_passes
        )

        success_vars_df = pd.concat([pd.Series(data=np.where(var["condition"], 1, 0), name=var["name"]) for var in success_vars], axis=1)
        df = pd.concat([df, success_vars_df], axis=1)
        # for var in success_vars:
        #     df[var["name"]] = np.where(var["condition"], 1, 0)

        df["assist_xg"] = np.where(
            (df["type_secondary"].str.contains("shot_assist")),
            df["shot_xg_next_1"],
            0,
        )

        df["gk_save_xg"] = np.where(
            (df["type_secondary"].str.contains("save")),
            df["possession_attack_xg_prev_1"],
            0,
        )
        

        return df, derivations + success_vars

    @staticmethod
    def get_loc_df(
        df: pd.core.groupby.generic.DataFrameGroupBy,
    ) -> pd.DataFrame:
        """Get a grouped df and generate the coord quantiles

        Args:
            pre_grouped (pd.core.groupby.generic.DataFrameGroupBy): [description]

        Returns:
            pd.DataFrame: [description]
        """

        loc_dfs_list = []
        for i in range(1, 10):
            q = i / 10
            temp_df = df.groupby("player_id")[
                ["location_x", "location_y", "centrality_y"]
            ].quantile(q)
            temp_df.columns = [f"{x}_{q}_quantile" for x in temp_df.columns]
            loc_dfs_list.append(temp_df)
        loc_df = pd.concat(loc_dfs_list, axis=1)
        # filling nans
        loc_df = loc_df.fillna(50)
        return loc_df

    def prederivation_prep(self, events):
        if isinstance(events, str):
            events = json.loads(events)
        df = pd.DataFrame(events)
        flatten_subset = [
            "player",
            "location",
            "team",
            "opponentTeam",
            "type",
            "pass",
            "shot",
            "groundDuel",
            "aerialDuel",
            "infraction",
            "possession",
        ]
        df = self.flatten_dict_cols(df, True, flatten_subset)

        # go one level deeper for possesions:
        df = self.flatten_dict_cols(
            df,
            True,
            [
                "possession_attack",
                "possession_team",
                "pass_endLocation",
            ],
        )
        obj_to_str_cols = ["type_secondary"]
        # excluding opponent events that are stupidly tagged as part of the possession:
        df["possession_id"] = np.where(
            df["team_id"] == df["possession_team_id"],
            df["possession_id"],
            np.nan,
        )
        for col in obj_to_str_cols:
            df[col] = df[col].astype(str)
        shift_dict = {
            "type_secondary": [-1, -2, 1, 2],
            "type_primary": [-1, -2, 1],
            "location_x": [-1],
            "location_y": [-1],
            "possession_attack_xg": [1],
            "pass_height": [1],
            "player_id": [1, 2, -1, -2],
            "team_id": [-1, -2],
            "matchTimestamp": [-1, -2],
            "pass_accurate": [-1],
            "shot_xg": [-1],
        }
        df = self.shift_vars(df, shift_dict)
        df = self.derive_additional_logic(df)
        # shifting again because inplay is additionally derived logic
        df = self.shift_vars(
            df,
            {
                "inplay": [1],
                "opponent_penalty": [1],
            },
        )

        return df

    def prep(self, events):
        if isinstance(events, str):
            events = json.loads(events)
        df = pd.DataFrame(events)
        flatten_subset = [
            "player",
            "location",
            "team",
            "opponentTeam",
            "type",
            "pass",
            "shot",
            "groundDuel",
            "aerialDuel",
            "infraction",
            "possession",
        ]
        df = self.flatten_dict_cols(df, True, flatten_subset)

        # go one level deeper for possesions:
        df = self.flatten_dict_cols(
            df,
            True,
            [
                "possession_attack",
                "possession_team",
                "pass_endLocation",
            ],
        )
        obj_to_str_cols = ["type_secondary"]
        # excluding opponent events that are stupidly tagged as part of the possession:
        df["possession_id"] = np.where(
            df["team_id"] == df["possession_team_id"],
            df["possession_id"],
            np.nan,
        )
        for col in obj_to_str_cols:
            df[col] = df[col].astype(str)
        shift_dict = {
            "type_secondary": [-1, -2, 1, 2],
            "type_primary": [-1, -2, 1],
            "location_x": [-1],
            "location_y": [-1],
            "possession_attack_xg": [1],
            "pass_height": [1],
            "player_id": [1, 2, -1, -2],
            "team_id": [-1, -2],
            "matchTimestamp": [-1, -2],
            "pass_accurate": [-1],
            "shot_xg": [-1],
        }
        df = self.shift_vars(df, shift_dict)
        df = self.derive_additional_logic(df)
        # shifting again because inplay is additionally derived logic
        df = self.shift_vars(
            df,
            {
                "inplay": [1],
                "opponent_penalty": [1],
            },
        )

        df, derivations = self.derive_vars(df)
        # removing the non-attributed events after we are done with derivations:
        df = df[df["player_id"] != 0]
        pre_grouped = df.groupby("player_id")
        #this jank is required because we are first defining a new var interceptions and then 
        # we are using it to derive other stuff, and then we want to use it as well 
        # but we dont add it to the derivations because we will have duplicate cols:
        grouped_df = pre_grouped[[x["name"] for x in derivations + [{"name": "interceptions"}]]].sum()

        grouped_df[["mean_xg", "sum_xg"]] = (
            df.dropna(subset=["possession_id"])
            .drop_duplicates(["player_id", "possession_id"])
            .groupby("player_id")
            .agg({"possession_attack_xg": ["mean", "sum"]})
        )
        xg_vars = ["shot_xg", "assist_xg", "gk_save_xg"]
        grouped_df[xg_vars] = pre_grouped[xg_vars].sum()
        grouped_df["matchId"] = pre_grouped["matchId"].max()  # keeping matchID

        grouped_df = pd.concat([grouped_df, self.get_loc_df(df)], axis=1)
        grouped_df = grouped_df.reset_index().rename(
            columns={"player_id": "playerId"}
        )
        # TODO keep only aggregated once prepping is done
        return {"advanced_stats": df, "grouped_df": grouped_df}


def prep_advanced_stats(events):
    try:
        asp = AdvancedStatsPrepper("advanced_stats")
        return asp.prep(events)
    except:
        print("Messed up game")


def prep_advanced_stats_prederivation(events):
    try:
        asp = AdvancedStatsPrepper("advanced_stats")
        return asp.prederivation_prep(events)
    except:
        print("Messed up game")
