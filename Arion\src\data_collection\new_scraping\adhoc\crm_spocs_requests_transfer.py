import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from uuid import uuid4

cnx = create_engine(postgres_prod_str)
pd.set_option("max_columns", None)

organizations = ['2a3458cd-6dfc-4dbe-b4ef-b4f50aa5af34', '92f31416-f6ec-4569-a7cb-4f280835ae4c', 'ebfdb98e-9af4-4ba0-a437-4ce1997132e1',
                 '92f31416-f6ec-4569-a7cb-4f280835ae4e', '92f31416-f6ec-4569-a7cb-4f280835ae4f', '92f31416-f6ec-4539-a7cb-4f280835ae4f',
                 '2a3458cd-6dfc-4dbe-b4ef-b4f50aa5af35', '2a3458cd-6dfc-4dbe-b4ef-b4f50aa5af41']
minutes = 30
schema = 'crm'

# Get the latest requests from SPOCS - 
requests_to_transfer_df = pd.read_sql(
    f"""select * from {schema}.team_requests tr where created_at >= current_timestamp - ({minutes} || 'minutes')::interval
and organization_id in ({', '.join(f"'{item}'" for item in organizations)
})""",
    cnx,
)

# Convert the 'organization_id' column to string
requests_to_transfer_df['organization_id'] = requests_to_transfer_df['organization_id'].astype(str)


try:

    # Drop all rows that aren't created by our organizations
    requests_to_transfer_df = requests_to_transfer_df[requests_to_transfer_df['organization_id'].isin(organizations)]


    for index, request in requests_to_transfer_df.iterrows():
        requests_to_add = []
        if request['organization_id'] in organizations:
            for org in organizations:
                if org != request['organization_id']:
                    dd = request.to_dict()
                    dd['organization_id'] = org
                    dd['id'] = uuid4()
                    requests_to_add.append(dd)
        df_to_save = pd.DataFrame(requests_to_add)
        df_to_save.to_sql('team_requests', cnx, schema=schema, index=None, if_exists='append')
except Exception as e:
    print(e)
    pass

