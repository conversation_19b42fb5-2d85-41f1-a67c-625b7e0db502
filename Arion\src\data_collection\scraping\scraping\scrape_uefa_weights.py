import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from settings import postgres_prod_str
from fuzzywuzzy import process

from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)


def scrape_prep_table():
    sp = get_soup_from_url(
        "https://en.competitions.uefa.com/memberassociations/uefarankings/club/libraries//years/2020/"
    )
    df = pd.read_html(str(sp.find("table")))[0]
    df = df.replace("-", np.nan)
    for col in df.columns:
        if "/" in col:
            df[col] = df[col].astype(float)
    df["teamId"] = np.nan
    df["Club"] = df["Club"].apply(lambda x: " ".join(x.split(" ")[1:]))
    country_dict = {
        "GER": "DEU",
        "ENG": "XEN",
        "POR": "PRT",
        "NED": "NLD",
        "SUI": "CHE",
        "GRE": "GRC",
        "DEN": "DNK",
        "SCO": "XSC",
        "CRO": "HRV",
        "BUL": "BGR",
        "MDA": "MHL",
        "WAL": "XWA",
        "NIR": "XNI",
        "KOS": "XKS",
    }
    df["Country"] = df["Country"].map(country_dict).fillna(df["Country"])
    return df


def read_ws_teams(cnx_prod):
    teams = pd.read_sql(
        """select distinct on (st."teamId") 
                        st."teamId", st."officialName" as "name", st."area_alpha3code"  
                        from 
                        seasons_teams st, seasons ss
                        where 
                            ss."seasonId"=st."seasonId" 
                            and  ss."seasoncompetitionId" in (108, 109)  """,
        cnx_prod,
    )
    return teams


def match_ws_uefa(df, ws):
    for i, row in df.iterrows():
        # just not gonna match dupes, there are very few and we can sort by hand if necessary
        name = row["Club"]
        print(name)
        country = row["Country"]
        if (
            len(ws[ws.area_alpha3code == country]) == 1
            and len(df[df.Country == country]) == 1
        ):
            df.loc[i, "teamId"] = ws[
                ws.area_alpha3code == country
            ].teamId.squeeze()
        if (
            len(df[df["Club"] == name]) == 1
            and len(ws[ws["name"] == name]) == 1
        ):
            df.loc[i, "teamId"] = ws[ws.clean_names == name].teamId.squeeze()
        else:
            fuzzy_match = process.extractOne(name, ws["name"])
            if (
                fuzzy_match[1] > 89
                and ws[ws["name"] == fuzzy_match[0]]["teamId"].nunique() == 1
            ):
                if (
                    ws[ws["name"] == fuzzy_match[0]].area_alpha3code.squeeze()
                    == country
                ):
                    df.loc[i, "teamId"] = ws[
                        ws["name"] == fuzzy_match[0]
                    ].teamId.squeeze()
            else:
                df.loc[i, "teamId"] = np.nan
    df = df.drop_duplicates(["teamId"], keep=False)
    return df


def save_table_to_sql(df, cnx_prod):
    df.to_sql("uefa_club_ratings", cnx_prod, index=False, if_exists="replace")


def main():

    cnx_prod = create_engine(postgres_prod_str)
    df = scrape_prep_table()
    ws = read_ws_teams(cnx_prod)
    mg = match_ws_uefa(df, ws)
    save_table_to_sql(mg, cnx_prod)


if __name__ == "__main__":
    main()
