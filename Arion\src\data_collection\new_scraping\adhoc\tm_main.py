# It will be used in a DAG in the future
# Running an example script:
# Collecting data for given players and populating the required tables
import asyncio
from datetime import datetime
from sqlalchemy.engine import create_engine
from src.data_collection.new_scraping.cleaning.validation import (
    PlayerPrep,
    TransfersPrep,
)
from src.data_collection.new_scraping.orchestrators.bulgarian_orchestrator import (
    Orchestrator,
)
from src.data_collection.new_scraping.scraping.scrape_tm_player_profiles import (
    PlayerProfilesScraper,
)

INITIAL_TABLE = "tm_to_ws_ids"
# INITIAL_TABLE = "bg_tm_urls"
PROG_TABLE = "progress_table"
MIGR_TABLE = "migration_table"
EXIS_TABLE = "existing_players_table"
FAILED_TABLE = "failed_players"
RAW_DATA_TABLE = "raw_data"
TRANSFERS_TABLE = "transfers_table"
AGENT_HISTORY_TABLE = "tm_historical_agents"
engine = create_engine(
    f"postgresql://postgres:password123@localhost:5433/demo_scraper"
)
asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
# DEMO
headers = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 5.1)"
        " AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/49.0.2623.112 Safari/537.36"
    )
}
# DEMO


def run(limit):
    # Get players IDs from initial table
    orchestrator.get_ids_from_progress(limit, "tm_player_url")  # Add limit

    # Second
    # Scrape the players data
    orchestrator.get_df()

    # Third
    # Save the collected players data in the migration table.
    orchestrator.save_scraped_data(PlayerPrep, TransfersPrep)
    # Update the progress table
    orchestrator.update_progress("tm_player_id", "\"playerId\"", "tm_player_id")


print(f"Start time - {datetime.now().strftime('%H:%M:%S')}")
if __name__ == "__main__":
    orchestrator = Orchestrator(
        INITIAL_TABLE,
        PROG_TABLE,
        MIGR_TABLE,
        EXIS_TABLE,
        FAILED_TABLE,
        RAW_DATA_TABLE,
        TRANSFERS_TABLE,
        AGENT_HISTORY_TABLE,
        engine,
        PlayerProfilesScraper,
    )
    # First
    # Populating the Progress table with data from the table with players - SQl Task
    orchestrator.engine.execute(
        f"INSERT INTO {orchestrator.prog_table} SELECT * FROM"
        f" {orchestrator.initial_table}"
    )
    for i in range(1):
        run(limit=107)
    # Fourth
    # When the Migration table is full move the data to the Existing Players table
print(f"End time - {datetime.now().strftime('%H:%M:%S')}")
