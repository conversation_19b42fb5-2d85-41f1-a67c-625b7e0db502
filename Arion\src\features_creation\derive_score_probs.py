from itertools import combinations, permutations
import math
import numpy as np
import pandas as pd
from functools import partial
from scipy.stats import poisson
from scipy.optimize import minimize
from settings import postgres_prod_str
from sqlalchemy import create_engine
import pandas as pd
from src.helper_funcs import fast_read_sql, fast_write_sql, expandgrid, convert_elo_to_win_prob
from time import time

def calculate_score_probability(x, team1_goals, team2_goals):
    rv1 = poisson(x[0])
    team1_goal_probability = rv1.pmf(team1_goals)

    rv2 = poisson(x[1])
    team2_goal_probability = rv2.pmf(team2_goals)

    return team1_goal_probability, team2_goal_probability

def convert_elos(raw_df):
    wp1, dp, wp2 = convert_elo_to_win_prob(raw_df['rating'], raw_df['opponent_rating'])

    raw_df['team1_winp'] = wp1
    raw_df['team2_winp'] = wp2
    raw_df['drawp'] = dp

    match_probs_df = raw_df[["competitionId", 'matchId', 'date', 'teamId', 'opponentId', 'team1_winp', 'team2_winp', 'drawp']].drop_duplicates().copy()

    return match_probs_df



class OddsCalculator:
    def __init__(self, max_total_goals,  market_margin_dict, cnx, clean_init):
        self.max_total_goals = max_total_goals
        self.cnx = cnx
        if market_margin_dict is None:
            self.market_margin_dict = {'match_outcome':0.07, 'score':0.07, 'handicap':0.07}
        self.dw_query = open('src/queries/new_queries/team_match_rating_query_for_odds.sql', 'r').read()

        if clean_init:
            self.cnx.execute('delete from derived_tables.match_score_odds')
            self.cnx.execute('delete from derived_tables.match_outcome_odds')
    
    def read_relevant_matches(self):
        
        self.tmr = fast_read_sql(self.dw_query, self.cnx)
        

        if 'match_score_odds' in self.cnx.table_names():
            computed_matches = pd.read_sql('select distinct "matchId" from derived_tables.match_score_odds', self.cnx)

            self.tmr = self.tmr[~self.tmr.matchId.isin(computed_matches.matchId)]
        
        self.elo_df = convert_elos(self.tmr)

        

    def generate_scores(self):
        score_df = pd.DataFrame(expandgrid(range(self.max_total_goals + 1), range(self.max_total_goals + 1)))
        score_df['total_goals'] = score_df['Var1'] + score_df['Var2']
        score_df['match_outcome'] = np.where(score_df['Var1'] > score_df['Var2'], 'home_win', np.where(score_df['Var2'] > score_df['Var1'], 'away_win', 'draw'))
        score_df = score_df[score_df['total_goals'] < self.max_total_goals + 1]
        self.score_df = score_df

    def add_margin(self, original_odd, market):
        orig_prob = 1/original_odd
        if market in ['score', 'handicap']:
            divisor = 2
        else:
            divisor = 3
        # profit_prob = orig_prob + (divisor - 1) * self.market_margin_dict[market]/divisor
        profit_prob = orig_prob + self.market_margin_dict[market]/divisor
        profit_margin_odds = 1/profit_prob
        return profit_margin_odds

    # TODO Add profit margin odds for two odds and BO2 

    def revert_margin(self, profit_margin_odds, market):
        # TODO Split margin based on favor
        # TODO Test thoroughly
        profit_prob = 1/profit_margin_odds
        divisor = 3
        orig_prob = profit_prob - self.market_margin_dict[market]/divisor
        original_odd = 1/orig_prob
        return original_odd


    def infer_score_probabilities(self, final_probs):

        def optimize_probs(p, scores, match_win_prob_team):

            # n: Number of home goals
            # k: number of away goals
            # p: probability of scoring (this is what we are trying to find)
            # scores: possible scores in game
            # match_win_prob_team_1: Actual Probability of self.team_1 winning the match
            # print(scores.columns)
            # scores = scores[scores.iloc[:,0]>scores.iloc[:, 1]]

            estimated_score_probability = []

            for i in range(scores.shape[0]):
                k = scores.iloc[i, 1]
                n = scores.iloc[i, 0]

                # print(len(p))

                p1, p2 = calculate_score_probability(p, n, k)

                estimated_score_probability.append(p1*p2)

            scores['estimated_score_probability'] = estimated_score_probability

            estimated_outcome_probs = scores.groupby('match_outcome')['estimated_score_probability'].sum().to_list()

            dst = np.sqrt(np.sum((np.array(match_win_prob_team) - np.array(estimated_outcome_probs))**2))

            return dst
        

        # Get the probability of match win per team
        home_win = final_probs[0]
        away_win = final_probs[1]
        draw = final_probs[2]


        # if total_prob != 1:
        #     draw = 1 - total_prob
        #     team_1_win += draw /2
        #     team_2_win += draw /2

        potential_scores = self.score_df.copy() 

        init_p = [0.5, 0.5]
        t1_prob = minimize(optimize_probs, x0 = init_p, args=(potential_scores, 
        [away_win, draw, home_win] # Alphabetical order bc pandas
        ), \
            method='BFGS', options = {'maxiter': 1000})

        mu = t1_prob['x']

        estimated_score_probability = []

        for i in range(self.score_df.shape[0]):
            k = self.score_df.iloc[i, 1]
            n = self.score_df.iloc[i, 0]

            p1, p2 = calculate_score_probability(mu, n, k)

            estimated_score_probability.append(p1*p2)

        self.score_df['estimated_score_probability'] = estimated_score_probability/sum(estimated_score_probability)

        return self.score_df

    def convert_winprob_to_score_odds(self, df):

        def tmp_prob(df_row):
            mid = df_row['matchId']
            tid = df_row['teamId']
            oid = df_row['opponentId']
            date = df_row['date']

            p = [df_row['team1_winp'], df_row['team2_winp'], df_row['drawp']]

            out_df = self.infer_score_probabilities(p)

            out_df['matchId'] = mid
            out_df['teamId'] = tid
            out_df['opponentId'] = oid
            out_df['date'] = date

            return out_df

        score_probs = pd.DataFrame()

        i = 0
        t1 = time()
        for index, row in df.iterrows():
            # print(i)
            # print(row)
            tmp_df = tmp_prob(row)
            
            
            score_probs = pd.concat([score_probs, tmp_df])
            # print(score_probs.tail())
            
            i +=1

            if (i % 10 == 0) | (i == df.shape[0]):
                
                cols = list(score_probs.columns)
                cols[0] = 'home_goals'
                cols[1] = 'away_goals'
                score_probs.columns = cols
                score_probs['raw_odds'] = 1/score_probs['estimated_score_probability']
                score_probs['profit_margin_odds'] = self.add_margin(score_probs['raw_odds'], 'score')
                # score_probs_all.append(score_probs)
                score_probs['profit_margin_odds'] = score_probs['profit_margin_odds'].clip(lower = 1.01, upper = 100)

                t2 = time()
                print(f'Computed score odds for {score_probs.shape[0] / self.score_df.shape[0]} matches. Writing to db. Time elapsed: {t2 - t1}s')

                t1 = t2
                fast_write_sql(score_probs, 'match_score_odds_elo', self.cnx, if_exists='append', schema='derived_tables')
                score_probs = pd.DataFrame()


        # self.score_probabilities = pd.concat(list(self.match_probs_df.apply(lambda x: tmp_prob(x), axis = 1))).reset_index()

        # return score_probs_all

    def make_match_score_odds(self):

        
        self.generate_scores()
        self.convert_winprob_to_score_odds(self.elo_df)
        self.cnx.execute('grant all on table derived_tables.match_score_odds_elo to kliment, elvan, playervaluation, airflow, ff_usr, quicksilver_usr')
    

    def make_outcome_odds(self):

        # tmr = fast_read_sql(self.dw_query, self.cnx)
        # self.elo_df = convert_elos(tmr)

        # Match Outcome Odds
        self.elo_df.columns = ['competitionId', 'matchId', 'date', 'teamId', 'opponentId', 'home_win', 'away_win', 'draw']

        self.elo_df = self.elo_df.melt(id_vars = ['competitionId', 'matchId', 'teamId', 'opponentId', 'date'], var_name = 'outcome', value_name = 'prob')
        self.elo_df['raw_odds'] = 1/self.elo_df['prob']
        self.elo_df['profit_margin_odds'] = self.add_margin(self.elo_df['raw_odds'], 'match_outcome')
        self.elo_df['profit_margin_odds'] = self.elo_df['profit_margin_odds'].clip(lower = 1.01, upper = 100)

        fast_write_sql(self.elo_df, 'match_outcome_odds_elo', self.cnx, if_exists='append', schema='derived_tables')
        self.cnx.execute('grant all on table derived_tables.match_outcome_odds_elo to kliment, elvan, playervaluation, airflow, ff_usr, quicksilver_usr')

    

def main():
    cnx = create_engine(postgres_prod_str)
    OC = OddsCalculator(7, None, cnx, False)
    OC.read_relevant_matches()
    OC.make_match_score_odds()
    OC.make_outcome_odds()

if __name__ == '__main__':
    main()

    

        

