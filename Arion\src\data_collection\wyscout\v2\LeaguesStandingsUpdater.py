import json
import pandas as pd
from typing import Union, List

from src.data_collection.wyscout.v2.Updater import Updater
from src.helper_funcs import check_existance


class LeaguesStandingsUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/standings"
        self.object_type = "teams"
        self.if_exists = "append"
        self.id_name = "seasonId"
        self.write_in_loop = True

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[dict, List[dict], None]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        payload = json.loads(resp).get(self.object_type)
        if payload is None:
            return payload
        for x in payload:
            x.update({"seasonId": code})
        return payload

    def get_objects_for_collection(self):
        existance = check_existance(self.cnx_prod, self.table_name, "wyscout")
        # the first time we run, no point in checking which seasons we have collected standings for:
        leagues_query = """SELECT ss."seasonId" 
                                FROM 
                                wyscout.seasons ss,
                                wyscout.competitions cc
                                WHERE 
                                ss."competitionId" = cc."competitionId"
                                and
                                cc.format='Domestic league'
                                and cc.gender='male'
                                and ss.active ='false' 
                                and ss."seasonId" > -1
                                """
        # if the table already exists, we are checking for collected seasons:
        if existance:
            leagues_query += f""" 
                            and ss."seasonId" not  in 
                            (select distinct "seasonId" from 
                            wyscout.{self.table_name})"""
        self.collection_list = pd.read_sql(leagues_query, self.cnx_prod)[
            "seasonId"
        ].tolist()

    def prep(self, results):
        # converting dict to df and dropping groupName since this is a league
        df = pd.DataFrame(results).drop(columns=["groupName"])
        # adding seasonId for matching
        # keep only one entry for team per season with most
        #  points/matches played because wyscout is garbage
        df = df.sort_values(
            ["teamId", "seasonId", "totalPoints", "totalPlayed"],
            ascending=False,
        ).drop_duplicates(subset=["teamId", "seasonId"], keep="first")
        # create goal diffence col and then sort by points and the goal difference
        df["goal_diff"] = df.totalGoalsFor - df.totalGoalsAgainst
        # some pandas vodoo magic below:
        cols = ["totalPoints", "goal_diff"]
        # sorting everything by pts and goal diff and then creating a tuple
        tups = df[cols].sort_values(cols, ascending=False).apply(tuple, 1)
        # factorizing this to have a rank across all teams and seasons:
        df["placement"] = pd.Series(pd.factorize(tups)[0] + 1, tups.index)
        # grouping by on a season level to have ranks across seaons:
        df["placement"] = df.groupby("seasonId")["placement"].rank("dense")
        return df
