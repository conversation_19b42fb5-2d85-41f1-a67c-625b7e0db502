import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str

cnx = create_engine(postgres_prod_str)
pd.set_option('max_columns', None)
club_requests = pd.read_sql(
    """select tt.team_id, tt.team_name, tt.league_country, tt.league_tier,
            string_agg(CONCAT(c.first_name,' ',c.last_name),' ') AS sources,
            max(tt2.fee) as maximum_fee_last_two_years
            from transfermarkt.tm_teams tt
            left join transfermarkt.tm_to_ws_team_ids ttwti on ttwti.transfermarkt_team_id = tt.team_id 
            left join crm.team_requests tr on tr."teamId" = ttwti."teamId"
            left join crm.source_to_record str on str.team_request_id = tr.id and tr.organization_id = 'ebfdb98e-9af4-4ba0-a437-4ce1997132e1'
            left join crm.contacts c on c.id = str.source_id 
            left join transfermarkt.tm_transfers tt2 on tt2.joined_id = tt.team_id
            and tt2."date" > CURRENT_DATE  - interval '2' year and tt2.fee > 0 and tt2.new_type = 'transfer'
            where (league_tier in (1,2) or league_tier in (1,2,3) and league_country = 'Germany')
            group by tt.team_id, tt.team_name, tt.league_country, tt.league_tier""",
    cnx,
)
club_staff = pd.read_sql(
    """            select tt.team_id ,sd.name as director, (CURRENT_DATE - sd.birth_date::date)/365 as directors_age
            , sda.name as scout, (CURRENT_DATE - sda.birth_date::date)/365 as scout_age from transfermarkt.tm_teams tt
            left join transfermarkt.staff_data sd on sd.current_team_id = tt.team_id 
            and sd.role in ('Sports Director', 'Director of Football', 'Technical Director', 'Head of Football Operations', 'Sporting CEO', 'Sporting Director')
            and coalesce(sd.in_charge_until::date, CURRENT_DATE)>= '2023-02-07'::date
            and sd.status = 'alive'
            left join transfermarkt.staff_data sda on sda.current_team_id = tt.team_id 
            and sda.role in ('Chief Scout', 'Head of Scouting') and coalesce(sda.in_charge_until::date, CURRENT_DATE)>= '2023-02-07'::date
            and sda.status = 'alive'""",
    cnx,
)

joined_df = club_requests.set_index("team_id").join(
    club_staff.set_index("team_id")
)
print(joined_df.to_csv('./mega_club_dump.csv',  encoding="utf-8-sig"))
