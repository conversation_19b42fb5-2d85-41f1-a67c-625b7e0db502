from datetime import datetime
import io
from typing import Dict, List
import pandas as pd
from pandas import ExcelWriter

from src.reporting.Reporter import Reporter, ReportContent, ContentType
from src.helper_funcs import fast_read_sql, get_sql_array_str


class MonthlyScoutingReporter(Reporter):
    def __init__(self):
        super().__init__()
        self.df_dict: Dict[str, pd.DataFrame] = {}
        self.rank_df_dict: Dict[str, pd.DataFrame] = {}
        self.comparable_positions_dict: Dict[str, List[str]] = {
            "lcmf3": ["lcmf3", "rcmf3", "rcmf", "lcmf"],
            "amf": ["lcmf3", "rcmf3", "rcmf", "lcmf", "amf", "ramf", "lamf"],
            "lb5": ["lb5", "lb", "lwb", "rb5", "rb", "rwb"],
            "cf": ["cf"],
            "winger": ["lamf", "lwf", "lw", "ramf", "rwf", "rw"],
            "cb": ["cb", "rcb", "lcb", "lcb3", "rcb3"],
            "number6": [
                "rdmf",
                "dmf",
                "ldmf",
                "rcmf",
                "lcmf",
                "rcmf3",
                "lcmf3",
            ],
        }
        self.vars: List[str] = [
            "shot_assists",
            "shots",
            "progressive_runs",
            "through_passes",
            "defensive_duels_won",
            "crosses",
            "dribbles",
        ]
        var_str = ", ".join(f'AVG(pmi."{x}") as "{x}" ' for x in self.vars)
        self.base_query: str = f"""
                        SELECT ws.*, tm.player_url FROM (
                        SELECT  pl.name,   st.*
                        FROM (SELECT _VARS, COUNT(*) AS num_games, pmi."playerId"
                            FROM wyscout.player_match_info pmi, 
                                    (
                                    SELECT
                                        ps."matchId",
                                        ps."playerId"
                                    FROM
                                        wyscout.player_match_positions ps,
                                        wyscout.matches m,
                                        wyscout.competitions cs
                                    WHERE
                                        ps.position IN _POSITIONS
                                        AND m.date > ( NOW() - INTERVAL '30 DAYS')::text
                                        AND m."matchId" = ps."matchId"
                                        AND m."competitionId" = cs."competitionId"
                                        AND cs.gender = 'male'

                                    GROUP BY
                                        ps."matchId",
                                        ps."playerId"
                                    HAVING
                                        sum(ps.percent) >= 75
                                ) pos
                            WHERE pmi."matchId" =  pos."matchId"
                            AND pmi."playerId" = pos."playerId"
                            GROUP BY pmi."playerId"
                            HAVING COUNT(*) > 2)st,
                            (SELECT pl."playerId", pl."firstName" ||' ' || pl."lastName" ||  
                            ' playerId: ' || pl."playerId" || ' from ' || coalesce(t.name, 'No team') as name
                            FROM 
                            wyscout.players pl left join wyscout.teams t on pl."currentTeamId" = t."teamId"
                            ) pl
                            WHERE st."playerId" = pl."playerId" ) ws
                            LEFT JOIN transfermarkt.transfermarkt_data tm using ("playerId")
                            """.replace(
            "_VARS", var_str
        )

    def get_data(self) -> pd.DataFrame:
        print("start data load", datetime.now())
        for k, v in self.comparable_positions_dict.items():
            comp_pos_str = get_sql_array_str(v)
            self.df_dict[k] = fast_read_sql(
                self.base_query.replace("_POSITIONS", comp_pos_str),
                self.cnx_prod,
            )
            print(f"{k} read ", datetime.now())

    def make_top_performers_df(self, df: pd.DataFrame) -> pd.DataFrame:
        new_cols: list = []
        for var in self.vars:
            temp_df = df.sort_values(var, ascending=False)
            rank_col = (
                (temp_df["name"] + ": " + temp_df[var].round(2).astype(str))
                .reset_index(drop=True)
                .head(10)
            )
            tm_url = temp_df["player_url"].reset_index(drop=True).head(10)
            temp_new = pd.concat([rank_col, tm_url], axis=1)
            temp_new.columns = [var, f"tm_link_{var}"]
            new_cols.append(temp_new)
        return pd.concat(new_cols, sort=True, axis=1)

    @staticmethod
    def fix_worksheet_column_width(worksheet, df):
        for idx, col in enumerate(df):  # loop through all columns
            series = df[col]
            max_len = (
                max(
                    (
                        series.astype(str)
                        .map(len)
                        .max(),  # len of largest item
                        len(str(series.name)),  # len of column name/header
                    )
                )
                + 1
            )  # adding a little extra space
            worksheet.set_column(idx, idx, max_len)  # set column width

    def make_report(self):
        print("start export", datetime.now())
        with io.BytesIO() as buffer:
            with ExcelWriter(buffer) as writer:
                for k, v in self.df_dict.items():
                    temp_df = self.make_top_performers_df(v)
                    temp_df.to_excel(writer, k, index=False)
                    worksheet = writer.sheets[k]  # pull worksheet object
                    self.fix_worksheet_column_width(worksheet, temp_df)
                    print(f"{k} saved ", datetime.now())
            self.content_list.append(
                ReportContent(
                    type=ContentType.attachment_file,
                    content=buffer.getvalue(),
                    file_format="xlsx",
                )
            )
