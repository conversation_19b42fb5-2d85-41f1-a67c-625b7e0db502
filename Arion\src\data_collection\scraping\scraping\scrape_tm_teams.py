import re
from datetime import datetime
from time import sleep, time
import requests
import traceback

from bs4 import BeautifulSoup
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import PROXIES, postgres_prod_str, postgres_dev_str
from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)


class TmTeamsScraper:
    def __init__(self, cnx_prod, cnx_dev):
        # initiate empty df, columns are same as dict keys
        self.cnx_prod = cnx_prod
        self.cnx_dev = cnx_dev
        self.session = requests.Session()
        self.session.headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 5.1)"
                " AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/49.0.2623.112 Safari/537.36"
            )
        }
        self.base_url = (
            "https://www.transfermarkt.com/_TEAM_NAME/startseite/verein/_ID"
        )
        self.team_urls = None
        self.teams_df = None
        self.collection_list = None
        self.batch_size = 1000
        self.df_list = []
        self.info_dict = {
            x: []
            for x in [
                "team_id",
                "team_name",
                "team_url",
                "league_name",
                "league_country",
                "league_tier",
            ]
        }

    def get_teams_for_scraping(
        self,
    ):
        df = pd.read_sql(
            "SELECT left_team_season_transfers_url,"
            " joined_team_season_transfers_url FROM tm_transfers",
            self.cnx_prod,
        )
        urls = np.unique(df.dropna())
        already_collected = pd.read_sql(
            "SELECT team_url from raw_tm_teams", self.cnx_dev
        )["team_url"]
        self.collection_list = list(
            {
                self.base_url.replace("_TEAM_NAME", x.split("/")[1]).replace(
                    "_ID", x.split("/")[-3]
                )
                for x in urls
                if "saison_id" in x
            }
        )
        # TODO write this in a better way... this check is kinda slow atm
        self.collection_list = [
            x
            for x in self.collection_list
            if x not in already_collected.tolist()
        ]

    @staticmethod
    def try_except_extract(item, expr, except_value=np.nan):
        try:
            return eval(expr)
        except Exception as e:
            # traceback.print_exc()
            return except_value

    @retry(
        wait=wait_random(min=10, max=30),
        stop=stop_after_attempt(5),
    )
    def scrape_team(self, url):
        soup = get_soup_from_url(url, self.session, PROXIES)
        summary = soup.find(class_="dataZusatzDaten")
        if summary is not None:
            for k, v in {
                "league_name": "item.find(class_='hauptpunkt').text",
                "league_tier": "item.find(class_='mediumpunkt').a.text",
                "league_country": (
                    "item.find(class_='mediumpunkt').a.img['alt']"
                ),
            }.items():
                self.info_dict[k].append(self.try_except_extract(summary, v))
        else:
            for x in ["league_name", "league_tier", "league_country"]:
                self.info_dict[x].append(np.nan)
        self.info_dict["team_name"].append(url.split("/")[3])
        self.info_dict["team_id"].append(int(url.split("/")[-1]))
        self.info_dict["team_url"].append(url)

    def loop(self):
        for i in range(0, len(self.collection_list), self.batch_size):
            batch = self.collection_list[i : i + self.batch_size]
            for url in batch:
                try:
                    self.scrape_team(url)
                except:
                    print(f"Broken link: {url}")
            self.teams_df = pd.DataFrame(self.info_dict)
            self.write_raw_teams()
            print("batch_done", datetime.now())

    def write_raw_teams(self):
        """
        Read raw teams, subset only new ones and write them to dev db
        """
        table_name = "raw_tm_teams"
        # try 3 times if it doesnt work print broken url for reference:
        try:
            collected = pd.read_sql(
                f"""SELECT team_id FROM  {table_name}""", self.cnx_dev
            )
            collected_ids = collected["team_id"]
        except:
            # if table doesnt exist then the collected list is empty
            collected_ids = []
        write_df = self.teams_df[~self.teams_df["team_id"].isin(collected_ids)]
        write_df.to_sql(
            table_name, self.cnx_dev, if_exists="append", index=False
        )
        print(f"{len(write_df)} teams written")
        # important to reset list once each batch is saved so that we dont save dupes:
        self.info_dict = {
            x: []
            for x in [
                "team_id",
                "team_name",
                "team_url",
                "league_name",
                "league_country",
                "league_tier",
            ]
        }


def main():
    cnx_prod = create_engine(postgres_prod_str)
    cnx_dev = create_engine(postgres_dev_str)
    scraper = TmTeamsScraper(cnx_prod, cnx_dev)
    scraper.get_teams_for_scraping()
    scraper.loop()


if __name__ == "__main__":
    main()
