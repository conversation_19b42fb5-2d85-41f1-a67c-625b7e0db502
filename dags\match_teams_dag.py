from datetime import datetime, timedelta
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator

from dag_settings import workdir, db_config, config


dag_params = {
    "dag_id": "match_teams_dag",
    "start_date": datetime(2020, 1, 18),
    "schedule_interval": timedelta(days=90),
    "params": {"workdir": workdir},
    "catchup": False,
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
    },
}

with DAG(**dag_params) as dag:

    match_teams = BashOperator(
        task_id="match_teams",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            arionflow_venv/bin/python3 src/data_collection/scraping/matching/match_transfermakt_teams_sql.py
                            """,
    )
    match_teams
