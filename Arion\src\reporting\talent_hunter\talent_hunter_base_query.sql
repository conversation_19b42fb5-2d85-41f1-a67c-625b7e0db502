CREATE TABLE derived_tables.young_players_appearances AS
SELECT
	tm.player_url as tm_link,
	base.player_name,
	base.team_name,
	base.competition_name,
	base.age,
	base.role_name,
	base."matchId",
	base."player_id" AS "playerId",
	base.team_id AS "teamId",
	base.label,
	base.date::timestamp::date as "date",
	base."minutesTagged",
	base.goals,
	base."divisionLevel"

FROM (
	SELECT
		DATE_PART('year', AGE(pmi.date::date, pl."birthDate"::date)) AS age,
		pl."firstName" || ' ' || pl."lastName" AS player_name,
		cs2."name" || ', ' || cs2."area_name" AS competition_name,

		t.name AS team_name,
		t."teamId" AS team_id,
		pl."playerId" AS player_id,
		*
	FROM
		wyscout.player_match_info pmi,
		wyscout.players pl,
		wyscout.teams t,
		wyscout.seasons_teams st,
		wyscout.seasons ss,
		wyscout.competitions cs,
		(select "competitionId", name, area_name from  wyscout.competitions) cs2
	WHERE
		pl."playerId" = pmi."playerId"
		AND pmi."competitionId" = cs2."competitionId"
		AND cs."competitionId" = ss."competitionId"
		AND pl."birthDate" > (now() - INTERVAL '20 YEARS')::text
		AND pl."currentTeamId" = t."teamId"
		AND t."teamId" = st."teamId"
		AND st."seasonId" = ss."seasonId"
		AND cs.type = 'club'
		AND cs.gender = 'male'
		AND cs.category = 'default'
		AND pmi."minutesTagged" > 45
		AND ss.active = TRUE
		AND (_COMP_FILTER)
		AND pmi.date > (now() - INTERVAL '7 DAYS')::text
		AND pmi.label !~ 'U\d[2]'
		AND pmi.label NOT ILIKE '%youth%'
		AND pl."currentTeamId" = pmi."teamId") base
	LEFT JOIN transfermarkt.transfermarkt_data tm ON base.player_id = tm."playerId"
