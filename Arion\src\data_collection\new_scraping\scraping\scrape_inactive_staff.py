from multiprocessing import cpu_count, Pool
import time
import aiohttp
import asyncio
from bs4 import BeautifulSoup
import pandas as pd
from settings import PROXIES
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)
import requests
from src.data_collection.new_scraping.scraping.scrape_tm_staff import (
    TmStaffScraper,
)
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
# import sys
#policy = asyncio.WindowsSelectorEventLoopPolicy()
#asyncio.set_event_loop_policy(policy)
pd.set_option("display.max_columns", 15)


class TmInactiveStaffScraper(Scraper):
    def collect_staff_urls(self):
        manager_url = "https://www.transfermarkt.com/manager/verfuegbaremanager/statistik?page="
        coach_url = "https://www.transfermarkt.com/trainer/verfuegbaretrainer/statistik?page="
        manager_cont = True
        counter = 1
        managers = []

        # Collecting urls for inactive managers
        while manager_cont:
            text = requests.get(
                manager_url + str(counter),
                headers={
                    "User-Agent": (
                        "Mozilla/5.0 (Windows NT 5.1)"
                        " AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/49.0.2623.112 Safari/537.36"
                    )
                },
                proxies={"https": PROXIES},
            ).content
            soup = BeautifulSoup(text, "html.parser")
            if soup is None:
                print(f"no soup {manager_url+str(counter)}")
                time.sleep(5)
                manager_cont = False

            table_items = soup.find("table", attrs={"class": "items"})
            manager_links = [
                td.find("a")["href"]
                for td in table_items.find_all(
                    "td", attrs={"class": ["zentriert hauptlink"]}
                )
            ]
            managers.append(manager_links)

            try:
                next_b = soup.find(
                    "li",
                    attrs={"class": "tm-pagination__list-item--icon-last-page"},
                )
                next_b.find("a")
                counter += 1
            except:
                manager_cont = False
                counter = 1

        manager_cont = True

        # Collecting urls for inactive coaches
        while manager_cont:
            text = requests.get(
                coach_url + str(counter),
                headers={
                    "User-Agent": (
                        "Mozilla/5.0 (Windows NT 5.1)"
                        " AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/49.0.2623.112 Safari/537.36"
                    )
                },
                proxies={"https": PROXIES},
            ).content
            soup = BeautifulSoup(text, "html.parser")
            if soup is None:
                print(f"no soup {coach_url+str(counter)}")
                time.sleep(5)
                manager_cont = False

            table_items = soup.find("table", attrs={"class": "items"})
            manager_links = [
                td.find("a")["href"]
                for td in table_items.find_all(
                    "td", attrs={"class": ["zentriert hauptlink"]}
                )
            ]
            managers.append(manager_links)

            try:
                next_b = soup.find(
                    "li",
                    attrs={"class": "tm-pagination__list-item--icon-last-page"},
                )
                next_b.find("a")
                counter += 1
            except:
                manager_cont = False
                counter = 1

        return managers

    def collect_data(
        self, urls, scraper: TmStaffScraper, engine
    ):

        text_ll = []

        for page in urls:
            for staff_url in page:
                text = requests.get(
                    staff_url,
                    headers={
                        "User-Agent": (
                            "Mozilla/5.0 (Windows NT 5.1)"
                            " AppleWebKit/537.36 (KHTML, like Gecko) "
                            "Chrome/49.0.2623.112 Safari/537.36"
                        )
                    },
                    proxies={"https": PROXIES},
                ).content
                text_ll.append((text, staff_url))

                cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(scraper.scrape_staff, text_ll)
        pool.close()
        pool.join()

        for data in res:
            if data:
                pd.DataFrame(data).to_sql(name='raw_staff_data', con=engine, schema='meta_scraping', index=False, if_exists="append", chunksize=1000, method='multi')

if __name__ == "__main__":
    scraper = TmStaffScraper()
    inacitve_scraper = TmInactiveStaffScraper()
    managers = inacitve_scraper.collect_staff_urls()

    engine = create_engine(postgres_prod_str)
    inacitve_scraper.collect_data(managers, scraper, engine)
