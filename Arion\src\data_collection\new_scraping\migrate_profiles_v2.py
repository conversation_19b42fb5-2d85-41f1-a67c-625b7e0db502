from settings import postgres_prod_str
from sqlalchemy.engine import create_engine


def main():
    engine = create_engine(postgres_prod_str)
    with engine.begin() as con:

        con.execute("""DELETE FROM transfermarkt_players.transfermarkt_data td 
                    USING transfermarkt_players.player_migration_table pmt
                    WHERE td.tm_player_id = pmt.tm_player_id""")

        con.execute(
            "INSERT INTO transfermarkt_players.transfermarkt_data SELECT * FROM"
            " transfermarkt_players.player_migration_table"
        )
        # When the migr table is full move all the data to the existing table
        query = """DELETE FROM transfermarkt_players.player_migration_table tmt
                    USING transfermarkt_players.transfermarkt_data tmp
                    WHERE tmt.tm_player_id = tmp.tm_player_id"""
        con.execute(query)

if __name__ == "__main__":
    main()