from datetime import datetime, timedelta
import schedule
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

workdir = '/home/<USER>/Projects/ad-hoc-analyses'

dag_params = {
    "dag_id": "automated-weekly-scouting-dag",

    "start_date": datetime(2023, 4, 1),
    "schedule_interval": timedelta(days=7),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    scout_and_email_players = BashOperator(
        task_id="scout_and_email_players",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/automated-quality-scouting-list/create-scouting-list.py """,
    )

    scout_and_email_players 