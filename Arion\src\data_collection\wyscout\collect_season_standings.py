from sqlalchemy import create_engine
import pandas as pd
from ratelimit import limits, sleep_and_retry
from tenacity import retry, wait_random, stop_after_attempt
from datetime import datetime
from collections import Counter

from src.helper_funcs import get_wyscout_response
from settings import postgres_prod_str, postgres_dev_str


class StandingsCollector:
    def __init__(self, cnx_prod, cnx_dev):
        self.cnx_prod = cnx_prod
        self.cnx_dev = cnx_dev
        self.league_ids = None
        self.cup_ids = None
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/standings"
        self.cups_base_url = "https://apirest.wyscout.com/v3/seasons/_ID/career"

        self.cups_df_list = []
        self.leagues_df_list = []

    def get_league_ids(self):
        leagues_query = """SELECT ss."seasonId" 
                            FROM 
                            seasons ss
                             WHERE 
                            "competition_format"='Domestic league'
                            and "competition_gender"='male'
                            and "seasonactive"='false' 
                            and ss."seasonId" not  in 
                            (select distinct "seasonId" from 
                            leagues_standings) """
        self.league_ids = pd.read_sql(leagues_query, self.cnx_dev)["seasonId"]
        print(len(self.league_ids), "league season ids for collection")

    @retry(wait=wait_random(), stop=stop_after_attempt(2))
    @sleep_and_retry
    @limits(calls=8, period=1)
    def get_resp(self, url):
        return get_wyscout_response(url)

    def get_cup_ids(self):
        # cups_query = '''SELECT ss."seasonId"
        #                     FROM
        #                     seasons ss
        #                      WHERE
        #                     "competition_format" ILIKE '%cup%'
        #                     and "competition_gender"='male'
        #                     and "seasonactive"='false'
        #                     and ss."seasonId" not in
        #                     (select distinct "seasonId"
        #                     from cups_standings) '''
        cups_query = """SELECT "seasonId" 
                        FROM seasons
                        WHERE 
                        "seasoncompetitionId" IN (82, 84, 108, 109 )
                        and seasonactive = 'false'  """
        self.cup_ids = pd.read_sql(cups_query, self.cnx_dev)["seasonId"]
        print(len(self.cup_ids), "cup season ids for collection")

    def collect_cups(self):
        def map_stage_names(x):
            x = x.copy()
            # whole thing is fucked because
            # 2nd round in libertadores is qualifications,
            # while secound round in sudamericana is best of 32
            stage_dict = {
                "8th finals": "Round of 16",
                "16th finals": "Round of 32",
                "quarter-finals": "Quarter-Finals",
                "semi-finals": "Semi-Finals",
                "group stage": "Group Stage",
                "final": "Final",
                "finals": "Final",
            }
            x["roundName"] = stage_dict.get(
                str(x["roundName"]).lower(), "Other"
            )
            return x

        def order_stages(x):
            def inner_order(x):
                order_dict = {
                    "Round of 16": 3,
                    "Round of 32": 2,
                    "Quarter-Finals": 4,
                    "Semi-Finals": 5,
                    "Group Stage": 1,
                    "Final": 6,
                    "Finals": 6,
                    "Other": 0,
                }
                return order_dict[x]

            return max(x, key=lambda x: inner_order(x))

        for i, x in enumerate(self.cup_ids):
            url = self.cups_base_url.replace("_ID", str(x))
            try:
                resp = self.get_resp(url)
            except:
                print(url)
                continue
            if "rounds" in resp:
                rounds = resp["rounds"]
                teams = []
                for r in rounds:
                    for group in r["groups"]:
                        for t in group["teams"]:
                            teams.append(t)
                teams = list(map(map_stage_names, teams))
                temp_df = pd.DataFrame(teams)
                best_placement = (
                    temp_df.groupby(["teamId", "seasonId"])["roundName"]
                    .apply(order_stages)
                    .reset_index()
                )
                self.cups_df_list.append(best_placement)

    def collect_leagues(self):
        for i, x in enumerate(self.league_ids):
            url = self.base_url.replace("_ID", str(x))
            resp = self.get_resp(url)
            if "teams" in resp:
                teams = resp["teams"]
                # converting dict to df and dropping groupName since this is a league
                df = pd.DataFrame(teams).drop(columns=["groupName"])
                # adding seasonId for matching
                df["seasonId"] = x
                # keep only one entry for team per season with most
                #  points/matches played because wyscout is garbage
                df = df.sort_values(
                    ["teamId", "seasonId", "totalPoints", "totalPlayed"],
                    ascending=False,
                ).drop_duplicates(subset=["teamId", "seasonId"], keep="first")
                # create goal diffence col and then sort by points and the goal difference
                df["goal_diff"] = df.totalGoalsFor - df.totalGoalsAgainst
                df = df.sort_values(
                    ["totalPoints", "goal_diff"], ascending=False
                )
                # df = df.sort_values(['seasonId', 'totalPoints', 'point_dif'],
                # ascending=False)
                # since ws provides them ordered, we reset the index
                # and use it as final placement column
                df = (
                    df.reset_index(drop=True)
                    .reset_index()
                    .rename(columns={"index": "placement"})
                    .drop(columns=["goal_diff"])
                )
                df["placement"] = df["placement"] + 1
            else:
                df = pd.DataFrame({"seasonId": [x]})
            self.leagues_df_list.append(df)
            if i % 100 == 0:
                print(f"{i} leagues collected", datetime.now())

    def write_standings(self, comp_format):
        # some pretty ghetto code but fuck wyscout
        # already spent too much time on this crap because their stupid API
        if comp_format == "cups":
            comp_df = pd.concat(self.cups_df_list)
            comp_df.to_sql(
                f"{comp_format}_standings",
                self.cnx_dev,
                if_exists="replace",
                index=False,
            )
        elif comp_format == "leagues":
            comp_df = pd.concat(self.leagues_df_list)
            comp_df.to_sql(
                f"{comp_format}_standings",
                self.cnx_dev,
                if_exists="append",
                index=False,
            )
        else:
            raise ValueError("Invalid competition format")


def main():
    cnx_prod = create_engine(postgres_prod_str)
    cnx_dev = create_engine(postgres_dev_str)
    collector = StandingsCollector(cnx_prod, cnx_dev)
    collector.get_cup_ids()
    collector.get_league_ids()
    # collector.league_ids = [181189]
    # collector.collect_cups()
    write_formats = ["leagues"]
    try:
        collector.collect_leagues()
    except Exception as e:
        # save whatever we have and then raise the error:
        for comp_format in write_formats:
            collector.write_standings(comp_format)
        raise Exception(e)
    for comp_format in write_formats:
        collector.write_standings(comp_format)


if __name__ == "__main__":
    main()
