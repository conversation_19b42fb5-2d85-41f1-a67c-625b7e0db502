#!/bin/bash
USR=$(grep '^USR =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
PASS=$(grep '^PASS =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
HOST=$(grep '^HOST =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')
PORT=$(grep '^PORT =' .env | cut -d '=' -f2 | cut -d ' ' -f2 | tr -d '"')

# if we are having different schemes, move to the same schema before pgdump-psql
if [ "$DEV_SCHEMA" != "$PROD_SCHEMA" ]
then 
 psql "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" \
-c "DROP TABLE IF EXISTS $PROD_SCHEMA.$TABLE CASCADE; CREATE TABLE $PROD_SCHEMA.$TABLE AS SELECT * FROM $DEV_SCHEMA.$TABLE"
fi
# drop table at existing destination in order to dump the updated one form dev:
psql "dbname=wyscout_raw_production host=$HOST user=$USR password=$PASS port=$PORT" \
-c "DROP TABLE IF EXISTS $PROD_SCHEMA.$TABLE CASCADE;"
#dump
pg_dump "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" \
-t $PROD_SCHEMA.$TABLE -O | psql "dbname=wyscout_raw_production host=$HOST user=$USR password=$PASS port=$PORT" 
if [ "$DEV_SCHEMA" != "$PROD_SCHEMA" ]
# if we had created this previously to align the schemas, clean up by dropping it when done:
then 
 psql "dbname=wyscout_raw_development host=$HOST user=$USR password=$PASS port=$PORT" \
-c "DROP TABLE IF EXISTS $PROD_SCHEMA.$TABLE CASCADE;"
fi
# grant permissions on the new table in prod:
psql "dbname=wyscout_raw_production host=$HOST user=$USR password=$PASS port=$PORT" -c \
"GRANT ALL ON TABLE $PROD_SCHEMA.$TABLE TO daniel, elvan, kliment, indeavr WITH GRANT OPTION;"
