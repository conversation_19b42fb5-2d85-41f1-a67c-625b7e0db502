import json
import time
import requests
from typing import List, Dict, Literal, Union, Callable, Optional, Tuple
from pydantic import BaseSettings
from tenacity import retry, wait_fixed, stop_after_attempt


class CrmRefresher:
    def __init__(
        self,
        settings: BaseSettings,
        refresh_object: Literal["team_requests", "players"],
        change_cols: List[str],
    ):
        self.settings = settings
        self.refresh_object = refresh_object
        self.change_cols = change_cols
        self.crm_objects: Dict[Union[int, str], Dict] = {}
        self.ids_for_checking: List[Tuple] = []
        self.commited_changes: Dict = {}
        self.changes: Dict[int, Dict] = {}
        self.check_object = (
            refresh_object if refresh_object == "players" else "teams"
        )
        self.id_name = f"{self.check_object[:-1]}Id"
        self.token = (
            requests.post(
                f"{settings.BASE_URL}/auth/jwt/login",
                data={
                    "username": settings.SHADOW_ELEVEN_USR,
                    "password": settings.SHADOW_ELEVEN_PASS,
                },
            )
            .json()
            .get("access_token")
        )
        if self.token is None:
            raise Exception("Could not get token")

    @retry(wait=wait_fixed(5), stop=stop_after_attempt(2))
    def get_crm_objects_from_api(
        self,
    ):
        obj_resp = requests.get(
            f"{self.settings.BASE_URL}/{self.refresh_object}/",
            headers={"Authorization": f"Bearer {self.token}"},
        )
        obj_resp.raise_for_status()
        return obj_resp.json()

    def set_crm_objects(
        self, crm_objects: List[Dict], filter_lambda: Optional[Callable] = None
    ):
        # here we keep by record id (player id or team request id):
        if filter_lambda is None:
            self.crm_objects = {o.get("_id"): {"crm": o} for o in crm_objects}
        else:
            self.crm_objects = {
                o.get("_id"): {"crm": o}
                for o in crm_objects
                if filter_lambda(o)
            }
        # the whole thing with using two sets of ids is caused by team requests
        # where we have one id for the request and another one for the team:
        self.ids_for_checking = [
            (obj["crm"]["_id"], obj["crm"][self.id_name])
            for obj in self.crm_objects.values()
        ]

    @retry(wait=wait_fixed(5), stop=stop_after_attempt(2))
    def get_current_state_of_id(self, rid):
        return requests.get(
            f"{self.settings.LOOKUP_API_URL}/{self.check_object}/{rid}",
            auth=(self.settings.LOOKUP_API_USR, self.settings.LOOKUP_API_PASS),
        )

    def get_current_state_of_objects(self):
        already_checked = {}
        for record_id, check_object_id in self.ids_for_checking:
            # if we have already checked the current state of the object,
            # we retrieve directly from the dict; this applies primarily for multiple
            # requests for the same team so that we dont have to query the api for each:
            if check_object_id in already_checked:
                self.crm_objects[record_id]["current"] = self.crm_objects[
                    already_checked[check_object_id]
                ]["current"]
                continue

            # if we have not checked the current state of the object, we query the api:
            try:
                resp = self.get_current_state_of_id(check_object_id)
                resp.raise_for_status()
                self.crm_objects[record_id]["current"] = resp.json()[0]
                already_checked[check_object_id] = record_id
                time.sleep(0.2)
            except Exception as e:
                print(f"Could not get current state of {check_object_id}: {e}")
                raise e from e

    def compute_changes(self):
        for rid in self.crm_objects:
            # if we are missing current version, skip because obviously we cant compare:
            if (
                self.crm_objects[rid].get("current") is None
                or self.crm_objects[rid].get("crm") is None
            ):
                continue
            for col in self.change_cols:
                if (
                    self.crm_objects[rid]["current"][col]
                    != self.crm_objects[rid]["crm"][col]
                ):
                    # if there has already been a change, we add one more key to change dict:
                    if rid in self.changes:
                        self.changes[rid][col] = self.crm_objects[rid][
                            "current"
                        ].get(col)
                    else:
                        # if this is the first change, we create the dict:
                        self.changes[rid] = {
                            col: self.crm_objects[rid]["current"].get(col)
                        }
                # we are replacing in-place because we are using put requests now, doing this after the change has been determined:
                self.crm_objects[rid]["crm"][col] = self.crm_objects[rid][
                    "current"
                ][col]

    def commit_updates(self):
        if len(self.changes) == 0:
            print("No changes to commit")
            return
        for rid in self.changes:
            try:
                resp = requests.put(
                    f"{self.settings.BASE_URL}/{self.refresh_object}/{rid}",
                    headers={"Authorization": f"Bearer {self.token}"},
                    data=json.dumps(self.crm_objects[rid]["crm"]),
                )
                resp.raise_for_status()
            except Exception as e:
                print(f"Could not commit changes for {rid}: {e}")
                print(f"Changes: {self.changes[rid]}")
                print(f"Response: {resp.json()}")
                raise e from e
            self.commited_changes[rid] = self.changes[rid]
        print(self.commited_changes)

    def refresh(self):
        self.set_crm_objects(self.get_crm_objects_from_api())
        self.get_current_state_of_objects()
        self.compute_changes()
        self.commit_updates()
