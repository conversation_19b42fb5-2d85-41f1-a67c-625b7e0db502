
alter table wyscout.players ADD PRIMARY KEY ("playerId") ;

alter table wyscout.seasons ADD PRIMARY KEY ("seasonId") ;

alter table wyscout.competitions ADD PRIMARY KEY ("competitionId") ;

alter table wyscout.matches ADD PRIMARY KEY ("matchId") ;

alter table wyscout.transfers ADD PRIMARY KEY ("transferId") ;

alter table wyscout.areas ADD PRIMARY KEY ("area_id") ;

alter table wyscout.teams ADD PRIMARY KEY ("teamId") ;

alter table wyscout.fixtures ADD PRIMARY KEY ("matchId") ;

alter table derived_tables.team_match_rating add primary key("teamId", "matchId");

alter table wyscout.lineups add primary key("playerId", "matchId");

alter table wyscout.advanced_stats add primary key("playerId", "matchId");

alter table wyscout.player_match_positions add primary key("playerId", "matchId", "position");

alter table wyscout.formations add primary key("playerId", "matchId", "id");

alter table wyscout.events ADD PRIMARY KEY ("matchId");

alter table wyscout.seasons_matches ADD PRIMARY KEY ("seasonId", "matchId") ;

alter table wyscout.seasons_players ADD PRIMARY KEY ("seasonId", "playerId") ;

alter table wyscout.seasons_teams ADD PRIMARY KEY ("seasonId", "teamId") ;

alter table transfermarkt.transfermarkt_data ADD PRIMARY KEY ("playerId") ;

alter table meta_scraping.player_migration_table add PRIMARY KEY ("playerId") ;


ALTER TABLE wyscout."seasons_players" ADD  CONSTRAINT "seasons_players_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE wyscout."seasons_players" ADD  CONSTRAINT "seasons_players_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId") ON UPDATE CASCADE DEFERRABLE;

ALTER TABLE wyscout."seasons_matches" ADD  CONSTRAINT "seasons_matches_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId") ON UPDATE CASCADE DEFERRABLE;

ALTER TABLE wyscout."seasons_matches" ADD  CONSTRAINT "seasons_matches_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") DEFERRABLE;

ALTER TABLE wyscout."seasons_teams" ADD  CONSTRAINT "seasons_teams_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId") ON UPDATE CASCADE DEFERRABLE;

ALTER TABLE wyscout."seasons_teams" ADD  CONSTRAINT "seasons_teams_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;

ALTER TABLE wyscout."seasons" ADD  CONSTRAINT "seasons_competitionId_fkey" FOREIGN KEY ("competitionId") REFERENCES wyscout."competitions" ("competitionId") DEFERRABLE;

ALTER TABLE wyscout."competitions" ADD  CONSTRAINT "competitions_area_id_fkey" FOREIGN KEY ("area_id") REFERENCES wyscout."areas" ("area_id") DEFERRABLE;


ALTER TABLE wyscout."lineups" ADD  CONSTRAINT "lineups_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId")  DEFERRABLE;

ALTER TABLE wyscout."lineups" ADD  CONSTRAINT "lineups_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;

ALTER TABLE wyscout."lineups" ADD  CONSTRAINT "lineups_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") ON DELETE CASCADE DEFERRABLE;

ALTER TABLE wyscout."formations" ADD  CONSTRAINT "formations_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId")  DEFERRABLE;

ALTER TABLE wyscout."formations" ADD  CONSTRAINT "formations_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;

ALTER TABLE wyscout."formations" ADD  CONSTRAINT "formations_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") ON DELETE CASCADE DEFERRABLE;

ALTER TABLE wyscout."player_match_positions" ADD  CONSTRAINT "player_match_positions_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId")  DEFERRABLE;

ALTER TABLE wyscout."player_match_positions" ADD  CONSTRAINT "player_match_positions_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") ON DELETE CASCADE DEFERRABLE;

ALTER TABLE wyscout."events" ADD  CONSTRAINT "events_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") ON DELETE CASCADE DEFERRABLE;

ALTER TABLE wyscout."matches" ADD  CONSTRAINT "matches_competitionId_fkey" FOREIGN KEY ("competitionId") REFERENCES wyscout."competitions" ("competitionId") DEFERRABLE;

ALTER TABLE wyscout."matches" ADD  CONSTRAINT "matches_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId") ON UPDATE CASCADE DEFERRABLE;

ALTER TABLE wyscout."matches" ADD  CONSTRAINT "matches_home_teamId_fkey" FOREIGN KEY ("home_teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;

ALTER TABLE wyscout."matches" ADD  CONSTRAINT "matches_away_teamId_fkey" FOREIGN KEY ("away_teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;


ALTER TABLE wyscout."transfers" ADD  CONSTRAINT "transfer_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE wyscout."advanced_stats" ADD  CONSTRAINT "advanced_stats_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") ON DELETE CASCADE DEFERRABLE;

ALTER TABLE wyscout."advanced_stats" ADD  CONSTRAINT "advanced_stats_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE wyscout."cups_standings" ADD  CONSTRAINT "cups_standings_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;

ALTER TABLE wyscout."cups_standings" ADD  CONSTRAINT "cups_standings_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId")  DEFERRABLE;

ALTER TABLE wyscout."leagues_standings" ADD  CONSTRAINT "leagues_standings_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;

ALTER TABLE wyscout."leagues_standings" ADD  CONSTRAINT "leagues_standings_seasonId_fkey" FOREIGN KEY ("seasonId") REFERENCES wyscout."seasons" ("seasonId")  DEFERRABLE;


ALTER TABLE wyscout.players ADD  CONSTRAINT "players_currentTeamId_fkey" FOREIGN KEY ("currentTeamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;

ALTER TABLE wyscout.players ADD  CONSTRAINT "palyers_currentNationalTeamId_fkey" FOREIGN KEY ("currentNationalTeamId") REFERENCES wyscout."teams" ("teamId")  DEFERRABLE;


-- ALTER TABLE derived_tables.team_match_rating ADD  CONSTRAINT "team_match_rating_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") DEFERRABLE;

-- ALTER TABLE derived_tables.team_match_rating ADD  CONSTRAINT "team_match_rating_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;

ALTER TABLE derived_tables.player_match_roles ADD  CONSTRAINT "player_match_roles_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") DEFERRABLE;

ALTER TABLE derived_tables.player_match_roles ADD  CONSTRAINT "player_match_roles_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE derived_tables.player_roles ADD  CONSTRAINT "player_roles_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;


ALTER TABLE derived_tables.scaling_attributes ADD  CONSTRAINT "scaling_attributes_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") DEFERRABLE;

ALTER TABLE derived_tables.scaling_attributes ADD  CONSTRAINT "scaling_attributes_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

-- ALTER TABLE derived_tables.scaling_attributes ADD  CONSTRAINT "scaling_attributes_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;

ALTER TABLE transfermarkt.transfermarkt_data ADD  CONSTRAINT "transfermarkt_data_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE transfermarkt.transfermarkt_injuries ADD  CONSTRAINT "transfermarkt_injuriesplayerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE transfermarkt.tm_historical_agents ADD  CONSTRAINT "tm_historical_agents_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE transfermarkt.tm_to_ws_ids ADD  CONSTRAINT "tm_historical_agents_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE derived_tables.twitter_posts ADD  CONSTRAINT "twitter_posts_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES wyscout."players" ("playerId") DEFERRABLE;

ALTER TABLE derived_tables.twitter_posts ADD  CONSTRAINT "twitter_posts_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES wyscout."matches" ("matchId") DEFERRABLE;

