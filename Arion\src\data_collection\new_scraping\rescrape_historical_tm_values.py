# It will be used in a DAG in the future
# Running an example script:
# Collecting data for given players and populating the required tables
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
from src.data_collection.new_scraping.orchestrators.tm_historical_value_orchestrator import (
    TMHistoricalValueOrchestrator,
)
from src.data_collection.new_scraping.scraping.scrape_tm_historical_value import (
    HistoricalValueScraper,
)

SCHEMA = "meta_scraping"
PROG_TABLE = "values_progress_table"
RAW_DATA_TABLE = "tm_historical_values"

engine = create_engine(postgres_prod_str)
# asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def run(limit):
    # Get players IDs from initial table
    orchestrator.get_urls_from_progress(limit, "tm_player_url")  # Add limit

    # Second
    # Scrape values data
    orchestrator.get_df()

    # Third
    # Save the collected players data in the raw table.
    orchestrator.save_scraped_data()

    orchestrator.update_progress('"playerId"')

    return bool(orchestrator.check_progress())


if __name__ == "__main__":
    orchestrator = TMHistoricalValueOrchestrator(
        PROG_TABLE,
        RAW_DATA_TABLE,
        engine=engine,
        scraper=HistoricalValueScraper,
    )

    # Scrape players
    while True:
        if not run(limit=1000):
            break
