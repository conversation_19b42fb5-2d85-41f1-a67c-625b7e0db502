import aiohttp

import pandas as pd
import numpy as np
from tenacity import retry, wait_fixed, stop_after_attempt

from src.data_collection.wyscout.v2.PlayersUpdater import PlayersUpdater
from src.data_collection.wyscout.v2.Updater import Updater


class TeamsUpdater(PlayersUpdater):
    def __init__(self):
        PlayersUpdater.__init__(self)
        self.base_url = "https://apirest.wyscout.com/v3/teams/_ID"
        self.object_type = self.base_url.split("/")[-2]
        self.id_name = "teamId"
        self.if_exists = "append"

    @retry(wait=wait_fixed(10), stop=stop_after_attempt(2))
    def prep(self, result):
        df = pd.DataFrame(result)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df.columns = [x.replace("wyId", "teamId") for x in df.columns]
        for col in ["children", "parent_teamId", "parent_name"]:
            if col not in df.columns:
                df[col] = np.nan
        return df
