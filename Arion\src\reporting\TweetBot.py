from datetime import datetime

import tweepy
from pydantic import BaseModel

from settings import (
    TWITTER_API_KEY_SECRET,
    TWITTER_API_KEY,
    TWITTER_ACCESS_TOKEN,
    TWITTER_ACCESS_TOKEN_SECRET,
)


class TweetInfo(BaseModel):
    twitter_id: int
    created_at: datetime
    content: str


class TweetBot:
    def __init__(self):
        auth = tweepy.OAuthHandler(TWITTER_API_KEY, TWITTER_API_KEY_SECRET)
        auth.set_access_token(TWITTER_ACCESS_TOKEN, TWITTER_ACCESS_TOKEN_SECRET)

        self.api = tweepy.API(auth)

    def post_tweet(self, content: str) -> TweetInfo:
        res = self.api.update_status(content)
        res_json = res._json
        ts = datetime.strptime(res_json["created_at"], "%a %b %d %H:%M:%S +0000 %Y")
        twitter_id = res_json["id"]
        return TweetInfo(twitter_id=twitter_id, created_at=ts, content=content)

    def delete_tweet(self, twitter_id: int):
        self.api.destroy_status(twitter_id)

