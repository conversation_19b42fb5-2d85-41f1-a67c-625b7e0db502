from datetime import datetime
from multiprocessing import cpu_count, Pool
import time
import aiohttp
import asyncio
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
from lxml import etree
import requests
from settings import PROXIES
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
)

pd.set_option("display.max_columns", 15)


class PlayerInjuriesScraper(Scraper):
    async def loop_through_urls(
        self, urls: list, ids: list
    ) -> "list[pd.DataFrame]":
        html_tasks = []
        async with aiohttp.ClientSession() as session:
            for url, id in zip(urls, ids):
                url = url.replace("/profil/", "/verletzungen/")
                html_tasks.append(fetch(session, url, id))
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> "list[pd.DataFrame]":
        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_page, htmls)
        pool.close()
        pool.join()

        injuries_df = []
        error_ids = []

        for data in res:
            if data["injuries_dict"]:
                [injuries_df.append(d) for d in data["injuries_dict"]]
            if data["error_ids"] is not None:
                error_ids.append(data["error_ids"])
        return [pd.DataFrame(injuries_df), pd.DataFrame(error_ids)]

    def scrape_page(self, tuple, initial=True, result={}, base_url=None):
        # sourcery no-metrics
        text, url, wyscout_id = tuple
        if initial:
            base_url = url

        id = url.split("/")[-1] if initial else url.split("/")[-3]
        error_ids = {
            "tm_player_id": id,
            "status": "unknown",
            "date_collected": str(datetime.today().date()),
        }
        result = {
            "injuries_dict": [],
            "error_ids": None,
        }

        injuries_dict = {
            "playerId": np.nan,
            "player_injuries_url": np.nan,
            "games_missed": np.nan,
            "duration": np.nan,
            "injured_until": np.nan,
            "injured_from": np.nan,
            "injury": np.nan,
            "season": np.nan,
        }

        info_items = [
            "season",
            "injury",
            "injured_from",
            "injured_until",
            "duration",
            "games_missed",
        ]

        soup = BeautifulSoup(text, "html.parser")
        dom = etree.HTML(str(soup))

        id = url.split("/")[-1] if initial else url.split("/")[-3]

        if soup is None:
            print(f"no soup {url}")
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Error"
            result["error_ids"] = error_ids
            time.sleep(5)
            return result
        if not soup.find(
            "h1", attrs={"class": "data-header__headline-wrapper"}
        ):
            print(f"no name {url}")
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Error"
            error_ids["date_collected"] = str(datetime.today().date())
            result["error_ids"] = error_ids
            return result
        if dom.xpath("//span[text()='Retired']"):
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Retired"
            result["error_ids"] = error_ids
            return result
        if soup.find("div", attrs={"class": "dataRibbonRIP"}):
            error_ids["tm_player_id"] = id
            error_ids["status"] = "Dead"
            result["error_ids"] = error_ids
            return result
        try:
            table_el = soup.find("table", attrs={"class": "items"}).find(
                "tbody"
            )
        except:
            table_el = None
        # No injuries, so we won't collect him

        if table_el is None:
            error_ids["status"] = "No injuries"
            result["error_ids"] = error_ids
            return result

        table_rows = table_el.find_all("tr")
        if len(table_rows) > 0:
            for row in table_rows:
                cells = row.find_all("td")

                if len(injuries_dict) - 2 != len(cells):
                    continue

                cells = row.find_all("td")
                for inj_var, inj_value in zip(info_items, cells):
                    injuries_dict[inj_var] = inj_value.text
                # need to append player id to each injury row
                injuries_dict["playerId"] = wyscout_id
                injuries_dict["player_injuries_url"] = url
                result["injuries_dict"].append(injuries_dict.copy())
        if initial:
            pager = soup.find("div", attrs={"class": "pager"})
            if pager:
                for i in range(2, len(pager.find_all("li")) - 1):
                    url = f"{base_url}/page/{i}"
                    headers = {
                        "User-Agent": (
                            "Mozilla/5.0 (Windows NT 5.1)"
                            " AppleWebKit/537.36 (KHTML, like Gecko) "
                            "Chrome/49.0.2623.112 Safari/537.36"
                        )
                    }

                    page = requests.get(
                        url, headers=headers, proxies={"https": PROXIES}
                    )
                    [
                        result["injuries_dict"].append(d)
                        for d in self.scrape_page(
                            (page.content, url, wyscout_id),
                            initial=False,
                            result=result,
                            base_url=base_url,
                        )["injuries_dict"]
                    ]

        if len(result["injuries_dict"]) > 0:
            result["error_ids"] = None
        return result


if __name__ == "__main__":
    inj = PlayerInjuriesScraper()
    print(
        asyncio.run(
            inj.loop_through_urls(
                [
                    "https://www.transfermarkt.com/ferran-torres/verletzungen/spieler/398184"
                ],
                [123],
            )
        )
    )
