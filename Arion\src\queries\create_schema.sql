-- Create Schema Enskai
CREATE SCHEMA enskai;

COMMENT ON SCHEMA enskai
    IS 'Script generation for schema and the database structure. Types and relationships will be enforced as well as indices.';

--- Grant access to existing tables
grant all on schema enskai to elvan;
grant all on all tables in schema enskai to elvan;
grant all on all sequences in schema enskai to elvan;
grant all on all functions in schema enskai to elvan;

grant all on schema enskai to kliment;
grant all on all tables in schema enskai to kliment;
grant all on all sequences in schema enskai to kliment;
grant all on all functions in schema enskai to kliment;

grant all on schema enskai to daniel;
grant all on all tables in schema enskai to daniel;
grant all on all sequences in schema enskai to daniel;
grant all on all functions in schema enskai to daniel;

grant all on schema enskai to postgres;
grant all on all tables in schema enskai to postgres;
grant all on all sequences in schema enskai to postgres;
grant all on all functions in schema enskai to postgres;


--- Grant access to all future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO daniel WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO kliment WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO elvan WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO cloudsqlsuperuser WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO cloudsqladmin WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON TABLES TO postgres WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO daniel WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO kliment WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO elvan WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO postgres WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO cloudsqladmin WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT ALL ON SEQUENCES TO cloudsqlsuperuser WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO kliment WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO daniel WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO elvan WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO postgres WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO cloudsqladmin WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT EXECUTE ON FUNCTIONS TO cloudsqlsuperuser WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO kliment WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO daniel WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO elvan WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO postgres WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO cloudsqladmin WITH GRANT OPTION;

ALTER DEFAULT PRIVILEGES IN SCHEMA enskai
GRANT USAGE ON TYPES TO cloudsqlsuperuser WITH GRANT OPTION;
