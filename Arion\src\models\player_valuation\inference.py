from pv.model_inference import PlayerValuePredictor
import src.models.player_valuation.utils.database_connect as dbconnect
from src.models.player_valuation.utils.config import *
from  itertools import product
import pandas as pd

def main():

    cnx_prod = dbconnect.main()
    model_type = 'regression'
    tm_value_used = [True, False]
    log_transformed = [True, False]
    root_transformed = [True, False]

    df = pd.DataFrame({'model_type': model_type,
        'tm_value_used':tm_value_used,
        'log_transformed':log_transformed,
        'root_transformed':root_transformed})

    columns = list(df.columns)

    df = pd.DataFrame(list(product(*df.values.T)))
    df.columns = columns
    df = df[~((df.root_transformed == True) & (df.log_transformed == True))].\
        drop_duplicates().reset_index(drop = True)

    for i, row in df.iterrows():
        model_type = row['model_type']
        log_transformed = row['log_transformed']
        root_transformed = row['root_transformed']
        tm_value_used = row['tm_value_used']
        PVP = PlayerValuePredictor(cnx_prod, prediction_table_name, model_type=model_type, 
            tm_value_used=tm_value_used, 
            log_transformed=log_transformed, root_transformed=root_transformed)
        PVP.read_prediction_df()
        if 'currently_injured' in PVP.prediction_df.columns:
            PVP.prediction_df['currently_injured'] = (PVP.prediction_df['currently_injured'] == 'True')
        PVP.get_final_model_from_gcloud()
        PVP.read_cloud_model_object()
        PVP.predict_values(delete_object_after_use = True, table_name='player_value_predictions')

if __name__ == "__main__":
    main()