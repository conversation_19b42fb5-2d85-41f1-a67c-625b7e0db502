with latest_elo as (
    select distinct on ("teamId") *
    from derived_tables.team_match_rating tmr
    order by "teamId",
        "date" desc
),
competition_filter as (
    select *
    from wyscout.competitions c
    where 1 = 1 --and area_name in ('England', 'France', 'United States', 'Italy', 'Russia')
        and format = 'Domestic league'
        and gender = 'male'
        and position('friend' in lower("name")) = 0
        and category = 'default'
        and "divisionLevel" > 0
        and "divisionLevel" < 2
    union
    select *
    from wyscout.competitions c
    where 1 = 1 --and area_name = 'England'
        and format = 'Domestic league'
        and gender = 'male'
        and position('friend' in lower("name")) = 0
        and category = 'default'
        and "divisionLevel" = 2
),
team_competition as (
    select *
    from (
            select distinct on ("home_teamId") "home_teamId" as "teamId",
                "matchId",
                "date",
                "competitionId"
            from wyscout.matches m
            where "competitionId" in (
                    select "competitionId"
                    from competition_filter
                )
            order by "home_teamId",
                "date" desc
        ) fq
    union
    select *
    from (
            select distinct on ("away_teamId") "away_teamId" as "teamId",
                "matchId",
                "date",
                "competitionId"
            from wyscout.matches m
            where "competitionId" in (
                    select "competitionId"
                    from competition_filter
                )
            order by "away_teamId",
                "date" desc
        ) sq
),
latest_team_competition as (
    select distinct on ("teamId") *
    from team_competition
    order by "teamId",
        "date"
),
final_select as (
    select t."officialName",
        c."area_name",
        c."name" as competition,
        le.*
    from latest_elo le,
        latest_team_competition ltc,
        wyscout.competitions c,
        wyscout.teams t
    where le."teamId" = ltc."teamId"
        and ltc."competitionId" = c."competitionId"
        and t."teamId" = le."teamId"
)
select *
from final_select
order by rating desc