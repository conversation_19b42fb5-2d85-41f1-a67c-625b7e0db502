import json
import pandas as pd
import numpy as np
from src.data_collection.wyscout.v2.Updater import Updater
from src.helper_funcs import (
    fast_read_sql,
)

class InitialCoachUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = (
            "https://apirest.wyscout.com/v3/coaches/_ID"
        )
        self.object_type = "coaches"
        self.id_name = "coachId"
        self.write_in_loop = (
            True  # applies to all tables that are being dropped and re-created
        )
    def get_objects_for_collection(self):
        id_series = fast_read_sql(
            f'select  "away_coachId" from wyscout.matches union  select  "home_coachId" from wyscout.matches',
            self.cnx_prod,
        )["away_coachId"]
        self.convert_id_series_to_collection_list(id_series)

    def extract_payload_from_resp(
        self, resp: str, code: int = None
    ):
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        payload = json.loads(resp)
        if len(payload) == 0:
            return None
        return [payload]

    def prep(self, results):
        df = pd.DataFrame(results)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df["coachId"] = df["wyId"].replace("", np.nan).astype("Int64")
        df["currentTeamId"] = df["currentTeamId"].replace("", np.nan).astype("Int64")
        for col in ["shortName", "firstName", "lastName", "middleName"]:
            df[col] = df[col].str.replace("(\\r|\\n)", "", regex=True)
        df = df.drop(columns=["wyId"])
        return df



class TempCoachUpdater(InitialCoachUpdater):
    def __init__(self, table_name):
        super().__init__(table_name)
    
    def get_objects_for_collection(self):
        self.collection_list = [278305,
83540,
382652,
135649,
144555,
166312,
57688,
68279,
336522,
211418,
0,
531727,
143657,
68220,
641574,
144537,
7973,
169058,
275374,
681598,
85183,
165349,
69014,
561162,
271971,
82503,
816505,
799460,
799487,
68611,
20920,
137196,
138118,
790530,
211124,
166493,
210184,
579571,
669317,
173052,
68448,
210836,
68771,
761364,
768199,
210139,
144543,
85273,
268461,
756126,
814452,
118346,
744657,
196890,
68741,
419491,
227975,
210165,
68194,
266381,
222459,
228505,
73150,
566580,
264588,
590526,
141187,
628006,
744341,
262968,
293312,
579372,
269403,
210694,
365605,
92245,
169086,
252561,
270355,
84049,
84147,
542746,
393927,
742197,
83927,
169055,
425860,
236729,
284054,
211647,
83963,
207114,
165266,
513819,
610707,
603296,
687861,
210078,
362020,
563605,
607115,
404844,
169052,
283947,
137137,
232904,
210672,
218741,
169040,
685405,
346479,
262995,
205753,
791448,
669441,
743956,
196916,
212162,
385631,
83779,
739860,
702039,
566303,
259781,
268714,
83875,
753831,
84495,
812846,
780050,
263037,
698519,
236810,
209508,
753830,
554268,
581199,
118679,
326774,
546966,
794385,
536947,
236889,
207011,
68138,
524275,
169988,
543974,
496058,
405468,
210270,
692351,
85219,
83933,
415310,
346442,
93027,
770278,
732875,
705775,
271729,
811662,
137136,
169031,
794205,
665373,
754488,
69288,
617308,
127886,
73011,
210690,
231512,
753832,
118443,
659637,
743565,
430058,
68350,
205751,
83903,
270358,
547704,
84175,
700120,
68907,
325654,
127737,
673272,
218429,
165323,
288640,
84387,
196619,
69076,
793694,
51157,
110099,
580561,
531973,
745195,
224054,
144559,
710383,
68956,
68811,
210269,
567932,
198445,
85457,
362019,
782290,
69352,
405449,
136076,
246514,
564932,
813785,
528927,
84345,
84083,
293310,
280347,
78616,
300146,
118641,
285943,
729622,
210374,
397210,
277216,
477058,
602643,
506581,
199707,
693206,
246918,
730182,
236886,
285672,
745044,
284000,
140062,
67976,
331983,
127636,
365391,
300640,
767886,
754277,
139278,
240992,
118953,
193214,
741715,
348548,
118377,
670657,
669318,
73110,
129029,
136750,
258697,
73044,
191773,
583397,
722836,
669411,
562289,
211402,
73512,
412672,
647386,
444340,
798207,
500830,
622309,
754505,
595178,
826273,
574439,
212049,
266680,
232938,
128750,
106017,
211504,
707499,
68924,
331875,
586971,
422858,
220747,
254471,
296831,
564967,
127803,
637927,
821586,
829243,
255679,
754739,
496233,
811993,
821593,
64434,
68004,
196604,
73396,
444483,
602119,
123006,
216670,
240094,
94157,
83654,
645023,
374004,
172934,
797950,
614455,
673733,
432843,
503601,
809697,
67689,
787756,
72734,
437713,
568270,
659618,
653841,
532658,
407212,
212025,
822967,
258179,
511547,
216717,
291314,
348541,
714835,
373848,
797834,
259928,
331984,
191167,
236711,
495053,
593443,
283478,
73046,
424648,
211387,
761344,
681620,
368676,
144979,
531967,
771103,
530558,
246629,
190060,
84828,
231479,
102352,
292808,
252065,
242020,
715516,
166337,
734343,
216959,
582407,
128525,
686657,
128464,
215853,
344277,
739649,
296838,
128217,
372660,
573571,
127912,
118468,
210645,
223693,
386526,
140560,
85650,
85313,
256624,
283047,
166301,
386530,
521842,
746338,
118319,
81009,
733817,
687021,
210780,
348309,
220865,
710194,
211363,
231346,
701982,
417933,
286261,
217917,
240209,
216960,
241681,
211123,
400674,
369520,
127857,
754253,
481529,
149335,
713116,
746724,
282509,
222634,
127750,
405617,
812157,
]


class CoachUpdater(Updater):
    ...