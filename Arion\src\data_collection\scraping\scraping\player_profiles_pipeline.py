import glob
import pandas as pd
from src.data_collection.scraping.scraping import scrape_tm_player_profiles
from src.data_collection.scraping.cleaning import clean_player_profiles


def main():
    # scrape_transfer_markt.main()
    # clean_transfermarkt_data.main()

    # TODO add scraping url part here
    # scrape whatever needs to be scraped
    try:
        prepped_file_path = "data/processed/scraped/cleaned_player_profiles.csv"
        prepped_file = pd.read_csv(
            prepped_file_path, index_col=0, encoding="utf-16"
        )
        df_list = [prepped_file]
    except:
        df_list = []
    scrape_tm_player_profiles.main()
    files = [x for x in glob.glob("data/raw/scraped/players/*.csv")]

    for file in files:
        df = pd.read_csv(file)
        df = clean_player_profiles.clean_profiles(df)
        df_list.append(df)
    df_concat = pd.concat(df_list, sort=True, axis=0).reset_index(drop=True)
    df_concat = df_concat.drop_duplicates(subset=["player_id"])
    df_concat.to_csv(prepped_file_path, encoding="utf-16")


if __name__ == "__main__":
    main()
