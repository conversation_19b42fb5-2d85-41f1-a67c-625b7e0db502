import functools
from multiprocessing import Pool, cpu_count
from typing import Union, List, Optional

import traceback
import json
import numpy as np
import pandas as pd

import aiohttp
import asyncio

from src.helper_funcs import (
    partial_func,
    cloud_log_struct,
)
from src.data_collection.wyscout.v2.TimeStampsExtractor import (
    TimeStampsExtractor,
)
from src.data_collection.wyscout.v2.EventsPrepper import EventsPrepper
from src.data_collection.wyscout.v2.FormationsPrepper import FormationsPrepper
from src.data_collection.wyscout.v2.LineupsPrepper import LineupsPrepper
from src.data_collection.wyscout.v2.AdvancedStatsPrepper import (
    AdvancedStatsPrepper,
)
from src.data_collection.wyscout.v2.MatchPrepper import MatchPrepper
from src.data_collection.wyscout.v2.Updater import Updater


class MatchObjectsUpdater(Updater):
    def __init__(self):
        super().__init__(None)
        self.base_url = "https://apirest.wyscout.com/v3/matches/_ID/events"
        self.cpu = cpu_count()
        self.tse = TimeStampsExtractor()
        self.ep = EventsPrepper()
        self.fp = FormationsPrepper()
        self.lp = LineupsPrepper()
        self.asp = AdvancedStatsPrepper()
        self.mp = MatchPrepper()
        self.object_type = "matches"
        self.id_name = "matchId"
        self.sleep_time = (
            1 / 10
        )  # setting it a bit slower because events resp is heavy

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[dict, List[dict]]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        return json.loads(resp)

    def handle_200_request(self, resp: str, code: int):
        payload = self.extract_payload_from_resp(resp, code)
        # here we dont append the id to collected list because we
        # need to see if all preppers process it successfully:
        if payload is None:
            self.no_data_ids_list.append(code)
        return payload

    def prep(self, response):  # sourcery skip: merge-list-append
        matchId = response["match"]["wyId"]
        ts = self.tse.prep(response["events"])
        partials_list = []
        partials_list.append(
            functools.partial(self.ep.prep, response["events"])
        )
        partials_list.append(
            functools.partial(self.fp.prep, response["formations"], matchId)
        )
        partials_list.append(
            functools.partial(self.lp.prep, response["match"], ts)
        )
        partials_list.append(functools.partial(self.mp.prep, response["match"]))
        pool = Pool(processes=self.cpu)
        res = pool.map(partial_func, partials_list)
        pool.close()
        pool.join()
        return res

    @staticmethod
    def debug_outside_multip(payload):

        matchId = payload["match"]["wyId"]
        # tse = TimeStampsExtractor()
        # ts = tse.prep(payload["events"])
        ep = EventsPrepper()
        fp = FormationsPrepper()
        lp = LineupsPrepper()
        asp = AdvancedStatsPrepper()
        mp = MatchPrepper()

        ep.prep(payload["events"])
        fp.prep(payload["formations"], matchId)
        lp.prep(payload["match"])
        asp.prep(payload["events"])
        mp.prep(payload["match"])
        return []

    async def process_prep_response(
        self,
        session: aiohttp.ClientSession,
        code: int,
    ) -> Optional[List[pd.DataFrame]]:
        """Calls the wyscout API and preps all match objets


        Args:
            session (aiohttp.ClientSession): [description]
            matchId(int): id of match, append to collected ids list if prep is successful

        Returns:
            list: list of all prepped dataframes for objects related
            to this match: formations, lineups, advanced_stats,
        """
        payload = await self.process_response(
            session,
            code,
            {
                "fetch": "match,formations",
                "exclude": "names,positions,formations",
            },
        )
        if payload is None:
            return None
            # if we have timeout, sleep and try again
        if payload == 504:
            # handling of the timeout logic has been moved to  Updater.handle_request_timeout
            # which is called in Updater.process_response; here we just retry since we now that a timeout
            # happened, we sleeped, logged and now need to retry
            return await self.process_prep_response(session, code)

        try:
            prepped_df_list = self.prep(payload)
            self.prepped_ids_list.append(code)
        except:
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "action": "failed_prep_match_objects",
                    "error": traceback.format_exc(),
                    "url": self.base_url.replace("_ID", str(code)),
                },
            )
            prepped_df_list = None
        return prepped_df_list

    async def collect(
        self,
    ) -> Union[List[pd.DataFrame], None]:

        tasks = []
        async with aiohttp.ClientSession() as session:
            for code in self.collection_list:
                await asyncio.sleep(self.sleep_time)
                tasks.append(
                    asyncio.create_task(
                        self.process_prep_response(session, code)
                    )
                )
            results = await asyncio.gather(*tasks)
            results = list(filter(lambda x: x is not None, results))
            if not results:
                return None
            return list(np.concatenate(results))

    async def loop(self) -> Union[List[pd.DataFrame], None]:
        """Overwrite the loop since the prep is happening within the processing of each request,
        rather than in bulk

        Returns:
            list: [description]
        """
        return await self.collect()
