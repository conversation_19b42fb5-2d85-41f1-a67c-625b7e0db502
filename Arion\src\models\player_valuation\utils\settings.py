import os
import dotenv

dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
dotenv.load_dotenv(dotenv_path)


DEVELOPMENT_DB = os.environ.get("DEVELOPMENT_DB")
PRODUCTION_DB = os.environ.get("PRODUCTION_DB")
DO_DB = os.environ.get("DO_DB")
USR = os.environ.get("USR")
PASS = os.environ.get("PASS")
HOST = os.environ.get("HOST")
PORT = os.environ.get("PORT")
DO_HOST = os.environ.get("DO_HOST")
WYSCOUT_USR = os.environ.get('WYSCOUT_USR')
WYSCOUT_PASS = os.environ.get('WYSCOUT_PASS')
PROXIES = os.environ.get('PROXIES')


postgres_dev_str = f'postgresql://{USR}:{PASS}@{HOST}:{PORT}/{DEVELOPMENT_DB}'
postgres_do_str = f'postgresql://{USR}:{PASS}@{DO_HOST}:{PORT}/{DO_DB}'
postgres_prod_str = f'postgresql://{USR}:{PASS}@{HOST}:{PORT}/{PRODUCTION_DB}'
postgres_research_str = f'postgresql://{USR}:{PASS}@{HOST}:{PORT}/research_db'
