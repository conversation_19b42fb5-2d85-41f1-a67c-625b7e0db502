from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

# from dag_settings import workdir
workdir = '/home/<USER>/Projects/player-quality'

dag_params = {
    "dag_id": "player_quality_dag",
    "start_date": datetime(2022, 10, 5),
    "schedule_interval": timedelta(days=14),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 0,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    calculate_player_quality_adult = BashOperator(
        task_id="calculate_player_quality_adult",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 /home/<USER>/Projects/player-quality/src/score_adult_players.py""",
    )

    calculate_player_quality_youth = BashOperator(
        task_id="calculate_player_quality_youth",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 /home/<USER>/Projects/player-quality/src/score_youth_players.py""",
    )


    create_rank_classes_mv = PostgresOperator(
        task_id="create_rank_classes_mv",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_rank_classes_mv.sql").read(),
    )

    create_latest_quality_mv= PostgresOperator(
        task_id="create_latest_quality_mv",
        database="wyscout_raw_production",
        sql=open(workdir + "/src/queries/create_latest_ranks_mv.sql").read(),
    )

    calculate_avg_player_quality = BashOperator(
        task_id="calculate_avg_player_quality",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 /home/<USER>/Projects/player-quality/src/compute_average_quality.py""",
    )

    calculate_player_quality_adult >> calculate_player_quality_youth >> create_rank_classes_mv >> create_latest_quality_mv >> calculate_avg_player_quality
