import os
from glob import glob
import numpy as np
import pandas as pd
from sqlalchemy import create_engine

from settings import postgres_prod_str
from src.helper_funcs import read_config


class InjuryCleaner:
    def __init__(self, read_dir, write_path):
        self.read_dir = read_dir
        self.write_path = write_path
        self.df = None

    def load_scraped_injuries(self):
        files_list = [
            x
            for x in glob(os.path.join(self.read_dir, "*.csv"))
            if "player_injuries.csv" not in x
        ]
        concat_df = pd.concat(
            [pd.read_csv(df, encoding="utf-16") for df in files_list]
        )
        concat_df = concat_df.reset_index(drop=True)
        self.df = concat_df

    def clean_df(self):
        self.df["games_missed"] = (
            self.df["games_missed"].replace("-", 0).replace("?", np.nan)
        )
        self.df["duration"] = (
            self.df["duration"]
            .apply(lambda x: x.replace(" days", ""))
            .replace("?", np.nan)
            .astype(float)
        )
        # self.df['injured_until'] =  self.df['injured_until'].replace('-', pd.NaT)
        for date_col in ["injured_until", "injured_from"]:
            self.df[date_col] = pd.to_datetime(
                self.df[date_col], format="%b %d, %Y", errors="coerce"
            )

    def save_df(self):
        self.df.to_csv(self.write_path, encoding="utf-16")


def main():
    config = read_config()["scraping"]["directories"]
    cleaner = InjuryCleaner(
        config["scraped_player_injuries"], config["cleaned_player_injuries"]
    )
    cleaner.load_scraped_injuries()
    cleaner.clean_df()
    cleaner.save_df()


if __name__ == "__main__":
    main()
