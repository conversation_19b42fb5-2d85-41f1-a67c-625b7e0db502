from datetime import datetime, timedelta
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

from dag_settings import workdir_ff

dag_params = {
    "dag_id": "run_ff_pipeline",
    "start_date": datetime(2021, 11, 9),
    "schedule_interval": timedelta(days=7),
    "params": {"workdir": workdir_ff},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    calculate_shots = PostgresOperator(
        task_id="calculate_shots",
        database="wyscout_raw_production",
        sql=open(workdir_ff + "/src/sql/create_derived_shots.sql").read(),
    )

    create_ff_matches_mv = PostgresOperator(
        task_id="create_ff_matches_mv",
        database="wyscout_raw_production",
        sql=open(workdir_ff + "/src/sql/team_rating_score.sql").read(),
    )

    create_ff_probability_mv = PostgresOperator(
        task_id="create_ff_probability_mv",
        database="wyscout_raw_production",
        sql=open(
            workdir_ff + "/src/sql/create_probability_modelling_mv.sql"
        ).read(),
    )

    # model_score_probabilities = BashOperator(
    #     task_id="model_score_probabilities",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/modelling/model_score_probabilities.py """,
    # )

    simulate_player_ff_outcomes = BashOperator(
        task_id="simulate_player_ff_outcomes",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/inference/simulate_ff_points.py """,
    )

    scrape_fanteam_results = BashOperator(
        task_id="scrape_fanteam_results",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_wrangling/collect_fanteam.py """,
    )

    # aggregate_simulation_results = PostgresOperator(
    #     task_id="aggregate_simulation_results",
    #     database="wyscout_raw_production",
    #     sql=open(workdir_ff + "/src/sql/player_points_calculation.sql").read(),
    # )

    create_ff_ml_mv = PostgresOperator(
        task_id="create_ff_ml_mv",
        database="wyscout_raw_production",
        sql=open(workdir_ff + "/src/sql/make_dataset_for_points_ml.sql").read(),
    )

    build_ml_model_for_fantasy = BashOperator(
        task_id="build_ml_model_for_fantasy",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/modelling/model_total_points_ml.py """,
    )


    calculate_shots >> create_ff_matches_mv >> create_ff_probability_mv
    create_ff_probability_mv >> simulate_player_ff_outcomes >> create_ff_ml_mv >> build_ml_model_for_fantasy
    create_ff_probability_mv >> scrape_fanteam_results
