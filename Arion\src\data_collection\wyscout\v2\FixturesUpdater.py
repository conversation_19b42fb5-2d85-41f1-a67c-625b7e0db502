from typing import Union, List
import aiohttp
import asyncio
import json

import numpy as np

from src.data_collection.wyscout.v2.MatchesUpdater import MatchesUpdater
from src.helper_funcs import fast_read_sql


class FixturesUpdater(MatchesUpdater):
    def __init__(self, table_name):
        super().__init__()
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/fixtures"
        self.object_type = "fixtures"
        self.if_exists = "replace"
        self.id_name = "matchId"
        self.write_in_loop = True
        self.table_name = table_name
        self.sleep_time = (
            2  # making it a bit slower since wyscout is choking again
        )

    def get_objects_for_collection(self):
        with open("src/queries/select_active_seasons.sql") as f:
            act_seasons_query = f.read()
        id_series = fast_read_sql(act_seasons_query, self.cnx_prod)["seasonId"]
        self.convert_id_series_to_collection_list(id_series)

    def extract_payload_from_resp(
        self, resp: str, code: int = None
    ) -> Union[dict, List[dict], None]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        # overwriting this because
        # it is matches not fixtures, gee thanks wyscout
        payload = json.loads(resp)["matches"]
        if len(payload) == 0:
            return None
        return list(
            filter(lambda x: x is not None, (x.get("match") for x in payload))
        )

    async def collect(
        self,
    ) -> Union[List, None]:    # TODO think about reducing code duplication,
        # we are only changing the params to the request
        """Collects a batch of ids from Wyscout endpoint

        Returns:
            list: A list of responses
        """
        if self.collection_list is None:
            raise ValueError("Collection list not implemented")
        tasks = []
        async with aiohttp.ClientSession() as session:
            for code in self.collection_list:
                if code not in [188394, '188394']:
                    await asyncio.sleep(self.sleep_time)
                    await self.check_timeouts()
                    tasks.append(
                        asyncio.create_task(
                            self.process_response(
                                session, code, params={"details": "matches"}
                            )
                        )
                    )
            # collect results form tasks:
            results = await asyncio.gather(*tasks)
            # filter for None responses (i.e. empty/broken responses)
            results = list(filter(lambda x: x is not None, results))
            if results:
                return list(np.concatenate(results))
            else:
                return
