#
# This file is autogenerated by pip-compile with python 3.9
# To update, run:
#
#    pip-compile '.\requirements.in'
#
aiohttp==3.8.1
    # via -r .\requirements.in
aiosignal==1.2.0
    # via aiohttp
alembic==1.7.5
    # via apache-airflow
apache-airflow==2.0.1
    # via -r .\requirements.in
apache-airflow-providers-ftp==2.0.1
    # via apache-airflow
apache-airflow-providers-http==2.0.1
    # via apache-airflow
apache-airflow-providers-imap==2.0.1
    # via apache-airflow
apache-airflow-providers-sqlite==2.0.1
    # via apache-airflow
apispec[yaml]==3.3.2
    # via flask-appbuilder
argcomplete==1.12.3
    # via apache-airflow
asgiref==3.4.1
    # via django
async-timeout==4.0.1
    # via aiohttp
atomicwrites==1.4.0
    # via pytest
attrs==20.3.0
    # via
    #   aiohttp
    #   apache-airflow
    #   cattrs
    #   jsonschema
    #   pytest
autopep8==1.6.0
    # via -r .\requirements.in
babel==2.9.1
    # via flask-babel
backcall==0.2.0
    # via ipython
beautifulsoup4==4.10.0
    # via -r .\requirements.in
bgtasks==0.0.44
    # via -r .\requirements.in
black==21.10b0
    # via -r .\requirements.in
cached-property==1.5.2
    # via apache-airflow
cachetools==4.2.4
    # via google-auth
calmjs.parse==1.3.0
    # via js2xml
cattrs==1.8.0
    # via apache-airflow
certifi==2021.10.8
    # via requests
cffi==1.15.0
    # via cryptography
charset-normalizer==2.0.7
    # via
    #   aiohttp
    #   requests
click==7.1.2
    # via
    #   black
    #   clickclick
    #   flask
    #   flask-appbuilder
clickclick==20.10.2
    # via connexion
colorama==0.4.4
    # via
    #   colorlog
    #   flask-appbuilder
    #   ipython
    #   pytest
    #   rich
colorlog==6.6.0
    # via apache-airflow
commonmark==0.9.1
    # via rich
connexion[flask,swagger-ui]==2.9.0
    # via apache-airflow
coverage[toml]==6.1.2
    # via pytest-cov
croniter==0.3.37
    # via apache-airflow
cryptography==35.0.0
    # via apache-airflow
decorator==5.1.0
    # via ipython
defusedxml==0.7.1
    # via python3-openid
dill==0.3.4
    # via apache-airflow
django==3.2.9
    # via
    #   django-admin-material
    #   django-cache-management
    #   django-condition-chain
    #   django-generic-counter
    #   django-mptt
    #   django-river
    #   django-zoneke-contrib
django-admin-material==0.1.0
    # via -r .\requirements.in
django-cache-management==5.0.0
    # via -r .\requirements.in
django-codemirror2==0.2
    # via django-river
django-condition-chain==0.0.9
    # via -r .\requirements.in
django-cte==1.1.4
    # via django-river
django-generic-counter==0.0.9
    # via -r .\requirements.in
django-js-asset==1.2.2
    # via django-mptt
django-mptt==0.9.1
    # via django-river
django-river==3.3.0
    # via -r .\requirements.in
django-zoneke-contrib==0.5
    # via -r .\requirements.in
djlime-settings==1.0.2
    # via -r .\requirements.in
dnspython==2.1.0
    # via email-validator
docutils==0.18
    # via python-daemon
edx-opaque-keys==2.2.2
    # via -r .\requirements.in
email-validator==1.1.3
    # via flask-appbuilder
exifread==2.3.2
    # via picorg
flask==1.1.4
    # via
    #   apache-airflow
    #   connexion
    #   flask-appbuilder
    #   flask-babel
    #   flask-caching
    #   flask-jwt-extended
    #   flask-login
    #   flask-openid
    #   flask-sqlalchemy
    #   flask-wtf
flask-appbuilder==3.1.1
    # via apache-airflow
flask-babel==1.0.0
    # via flask-appbuilder
flask-caching==1.10.1
    # via apache-airflow
flask-jwt-extended==3.25.1
    # via flask-appbuilder
flask-login==0.4.1
    # via
    #   apache-airflow
    #   flask-appbuilder
flask-openid==1.3.0
    # via flask-appbuilder
flask-sqlalchemy==2.5.1
    # via flask-appbuilder
flask-wtf==0.14.3
    # via
    #   apache-airflow
    #   flask-appbuilder
frozenlist==1.2.0
    # via
    #   aiohttp
    #   aiosignal
fuzzywuzzy==0.18.0
    # via -r .\requirements.in
google-api-core[grpc]==2.2.2
    # via
    #   google-cloud-appengine-logging
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-storage
google-auth==2.3.3
    # via
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-storage
google-cloud-appengine-logging==1.1.0
    # via google-cloud-logging
google-cloud-audit-log==0.2.0
    # via google-cloud-logging
google-cloud-core==2.2.1
    # via
    #   google-cloud-logging
    #   google-cloud-storage
google-cloud-logging==2.7.0
    # via -r .\requirements.in
google-cloud-storage==1.42.3
    # via -r .\requirements.in
google-crc32c==1.3.0
    # via google-resumable-media
google-resumable-media==2.1.0
    # via google-cloud-storage
googleapis-common-protos[grpc]==1.53.0
    # via
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
graphviz==0.18.1
    # via apache-airflow
greenlet==1.1.2
    # via sqlalchemy
grpc-google-iam-v1==0.12.3
    # via google-cloud-logging
grpcio==1.41.1
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.41.1
    # via google-api-core
gunicorn==19.10.0
    # via apache-airflow
idna==3.3
    # via
    #   email-validator
    #   requests
    #   yarl
importlib-resources==1.5.0
    # via apache-airflow
inflection==0.5.1
    # via connexion
iniconfig==1.1.1
    # via pytest
ipython==7.29.0
    # via line-profiler
iso8601==1.0.0
    # via apache-airflow
isodate==0.6.0
    # via openapi-schema-validator
itsdangerous==1.1.0
    # via
    #   apache-airflow
    #   flask
    #   flask-wtf
jedi==0.18.0
    # via ipython
jinja2==2.11.3
    # via
    #   apache-airflow
    #   flask
    #   flask-babel
    #   python-nvd3
    #   swagger-ui-bundle
joblib==1.1.0
    # via
    #   -r .\requirements.in
    #   scikit-learn
js2xml==0.4.0
    # via -r .\requirements.in
jsonschema==3.2.0
    # via
    #   apache-airflow
    #   connexion
    #   flask-appbuilder
    #   openapi-schema-validator
    #   openapi-spec-validator
lazy-object-proxy==1.6.0
    # via apache-airflow
levenshtein==0.16.0
    # via -r .\requirements.in
line-profiler==3.3.1
    # via -r .\requirements.in
lockfile==0.12.2
    # via
    #   apache-airflow
    #   python-daemon
lxml==4.6.4
    # via
    #   -r .\requirements.in
    #   js2xml
    #   pytrends
mako==1.1.5
    # via alembic
markdown==3.3.4
    # via apache-airflow
markupsafe==1.1.1
    # via
    #   apache-airflow
    #   jinja2
    #   mako
    #   wtforms
marshmallow==3.14.1
    # via
    #   flask-appbuilder
    #   marshmallow-enum
    #   marshmallow-oneofschema
    #   marshmallow-sqlalchemy
marshmallow-enum==1.5.1
    # via flask-appbuilder
marshmallow-oneofschema==3.0.1
    # via apache-airflow
marshmallow-sqlalchemy==0.23.1
    # via flask-appbuilder
matplotlib-inline==0.1.3
    # via ipython
multidict==5.2.0
    # via
    #   aiohttp
    #   yarl
mypy-extensions==0.4.3
    # via black
natsort==8.0.0
    # via croniter
numpy==1.21.4
    # via
    #   -r .\requirements.in
    #   pandas
    #   patsy
    #   scikit-learn
    #   scipy
    #   statsmodels
oauthlib==3.1.1
    # via requests-oauthlib
openapi-schema-validator==0.1.5
    # via openapi-spec-validator
openapi-spec-validator==0.3.1
    # via connexion
openpyxl==3.0.9
    # via openpyxl
packaging==21.2
    # via
    #   google-cloud-appengine-logging
    #   pytest
pandas==1.3.4
    # via
    #   -r .\requirements.in
    #   apache-airflow
    #   pytrends
    #   statsmodels
parso==0.8.2
    # via jedi
pathspec==0.9.0
    # via black
patsy==0.5.2
    # via statsmodels
pbr==5.7.0
    # via stevedore
pendulum==2.1.2
    # via apache-airflow
pickleshare==0.7.5
    # via ipython
picorg==0.2.0
    # via -r .\requirements.in
pika==1.2.0
    # via bgtasks
pillow==8.4.0
    # via picorg
platformdirs==2.4.0
    # via black
pluggy==1.0.0
    # via pytest
ply==3.11
    # via calmjs.parse
pprintpp==0.4.0
    # via pycountry-convert
prison==0.2.1
    # via flask-appbuilder
prompt-toolkit==3.0.22
    # via ipython
proto-plus==1.19.8
    # via
    #   google-cloud-appengine-logging
    #   google-cloud-logging
protobuf==3.19.1
    # via
    #   google-api-core
    #   google-cloud-audit-log
    #   google-cloud-storage
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
    
psycopg2-binary<2.9 #very important so that fast_write_sql works

psutil==5.8.0
    # via apache-airflow
py==1.11.0
    # via pytest
pyasn1==0.4.8
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.2.8
    # via google-auth
pycodestyle==2.8.0
    # via autopep8
pycountry==20.7.3
    # via pycountry-convert
pycountry-convert==0.7.2
    # via -r .\requirements.in
pycparser==2.21
    # via cffi
pydantic==1.8.2
    # via -r .\requirements.in
pygments==2.10.0
    # via
    #   apache-airflow
    #   ipython
    #   rich
pyjwt==1.7.1
    # via
    #   apache-airflow
    #   flask-appbuilder
    #   flask-jwt-extended
pymongo==3.12.1
    # via edx-opaque-keys
pyparsing==2.4.7
    # via packaging
pyrsistent==0.18.0
    # via jsonschema
pytest==6.2.5
    # via
    #   pycountry-convert
    #   pytest-cov
    #   pytest-mock
pytest-cov==3.0.0
    # via pycountry-convert
pytest-mock==3.6.1
    # via pycountry-convert
python-daemon==2.3.0
    # via apache-airflow
python-dateutil==2.8.2
    # via
    #   apache-airflow
    #   croniter
    #   flask-appbuilder
    #   pandas
    #   pendulum
python-dotenv==0.19.2
    # via -r .\requirements.in
python-nvd3==0.15.0
    # via apache-airflow
python-slugify==4.0.1
    # via
    #   apache-airflow
    #   python-nvd3
python3-openid==3.2.0
    # via
    #   apache-airflow
    #   flask-openid
pytrends==4.7.3
    # via -r .\requirements.in
pytz==2021.3
    # via
    #   -r .\requirements.in
    #   babel
    #   django
    #   flask-babel
    #   pandas
pytzdata==2020.1
    # via pendulum
pyyaml==5.4.1
    # via
    #   -r .\requirements.in
    #   apispec
    #   clickclick
    #   connexion
    #   openapi-spec-validator
rapidfuzz==1.8.2
    # via levenshtein
ratelimit==2.2.1
    # via -r .\requirements.in
regex==2021.11.10
    # via black
repoze.lru==0.7
    # via pycountry-convert
requests==2.26.0
    # via
    #   -r .\requirements.in
    #   apache-airflow
    #   apache-airflow-providers-http
    #   connexion
    #   google-api-core
    #   google-cloud-storage
    #   pytrends
    #   requests-oauthlib
    #   tweepy
requests-oauthlib==1.3.0
    # via tweepy
rich==9.2.0
    # via apache-airflow
rsa==4.7.2
    # via google-auth
scikit-learn==1.0.1
    # via sklearn
scipy==1.7.2
    # via
    #   -r .\requirements.in
    #   scikit-learn
    #   statsmodels
setproctitle==1.2.2
    # via apache-airflow
six==1.16.0
    # via
    #   flask-jwt-extended
    #   google-auth
    #   google-cloud-storage
    #   grpcio
    #   js2xml
    #   jsonschema
    #   openapi-schema-validator
    #   openapi-spec-validator
    #   patsy
    #   prison
    #   python-dateutil
    #   sqlalchemy-utils
    #   tenacity
sklearn==0.0
    # via -r .\requirements.in
soupsieve==2.3.1
    # via beautifulsoup4
spark-yarn-submit==1.0.0
    # via -r .\requirements.in
sqlalchemy==1.4.27
    # via
    #   -r .\requirements.in
    #   alembic
    #   apache-airflow
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   sqlalchemy-jsonfield
    #   sqlalchemy-utils
sqlalchemy-jsonfield==1.0.0
    # via apache-airflow
sqlalchemy-utils==0.37.9
    # via flask-appbuilder
sqlparse==0.4.2
    # via django
statsmodels==0.13.1
    # via -r .\requirements.in
stevedore==3.5.0
    # via edx-opaque-keys
swagger-ui-bundle==0.0.9
    # via connexion
tabulate==0.8.9
    # via apache-airflow
tenacity==6.2.0
    # via
    #   -r .\requirements.in
    #   apache-airflow
termcolor==1.1.0
    # via apache-airflow
text-unidecode==1.3
    # via python-slugify
threadpoolctl==3.0.0
    # via scikit-learn
toml==0.10.2
    # via
    #   autopep8
    #   pytest
tomli==1.2.2
    # via
    #   black
    #   coverage
trade-common==0.29.0
    # via -r .\requirements.in
traitlets==5.1.1
    # via
    #   ipython
    #   matplotlib-inline
tweepy==4.3.0
    # via -r .\requirements.in
typing-extensions==********
    # via
    #   async-timeout
    #   black
    #   pydantic
    #   rich
unicodecsv==0.14.1
    # via apache-airflow
unidecode==1.3.2
    # via -r .\requirements.in
urllib3==1.26.7
    # via requests
wcwidth==0.2.5
    # via prompt-toolkit
werkzeug==1.0.1
    # via
    #   apache-airflow
    #   connexion
    #   flask
    #   flask-jwt-extended
wheel==0.37.0
    # via pycountry-convert
wtforms==3.0.0
    # via flask-wtf
XlsxWriter==3.0.3
yarl==1.7.2
    # via aiohttp

# The following packages are considered to be unsafe in a requirements file:
# setuptools
