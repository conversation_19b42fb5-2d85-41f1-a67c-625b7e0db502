from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)
import re
import js2xml
import pandas as pd


def get_historical_values(url_list):
    df_list = []
    for i, url in enumerate(url_list):
        print(i)
        print(url)
        info_dict = {}
        soup = get_soup_from_url(url)
        script = soup.find("script", text=re.compile("Highcharts.Chart"))
        if script is not None:
            script = script.string
            parsed = js2xml.parse(script)
            series = parsed.xpath("//property[@name='series']")
            info_dict["values"] = [
                d.xpath(".//property[@name='y']/number/@value") for d in series
            ][0]
            info_dict["team"] = [
                d.xpath(".//property[@name='verein']/string/text()")
                for d in series
            ][0]
            info_dict["ages"] = [
                d.xpath("//property[@name='age']//number/@value")
                for d in series
            ][0]
            info_dict["ages"] = [
                d.xpath("//property[@name='datum_mw']//string/text()")
                for d in series
            ][0]
            df = pd.DataFrame(info_dict)
            df["tm_player_id"] = url.split("/")[-1]
            df_list.append(df)
    return pd.concat(df_list)
