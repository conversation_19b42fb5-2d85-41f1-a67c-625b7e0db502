from src.reporting.Mailer import Mailer, MailConfigOptions
from src.reporting.monthly_scouting_v1.MonthlyScoutingReporter import MonthlyScoutingReporter

from src.helper_funcs import get_time_stamp


def main():
    rep = MonthlyScoutingReporter()
    rep.get_data()
    rep.make_report()
    mail = Mailer(
        f"Automated monthly report {get_time_stamp()}",
        [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ],
        rep.content_list,
        MailConfigOptions.google_enskai_alerts.value,
    )
    mail.send_mail()


if __name__ == "__main__":
    main()
