from sqlalchemy.engine import create_engine
from settings import postgres_prod_str

engine = create_engine(postgres_prod_str)
SCHEMA = "meta_scraping"

def main():
    # Truncate raw_data
    engine.execute(f"DELETE FROM {SCHEMA}.raw_data")

    # Truncate failed_players
    engine.execute(f"DELETE FROM {SCHEMA}.failed_players")

    # Truncate raw_transfers_table
    engine.execute(f"DELETE FROM {SCHEMA}.raw_transfers_table")

    # Truncate raw_agent_history_table
    engine.execute(f"DELETE FROM {SCHEMA}.raw_agent_history_table")


if __name__ == "__main__":
    main()