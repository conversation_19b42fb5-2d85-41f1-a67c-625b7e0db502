import unittest
import pandas as pd
from sqlalchemy.engine import create_engine

from src.helper_funcs import fast_read_sql


class TestPlayerBeforeMigration(unittest.TestCase):
    engine = create_engine(
        "***********************************************************/wyscout_raw_production"
    )
    with engine.begin() as con:
        df = fast_read_sql(
            """Select * from meta_scraping.raw_data""", engine
        )
        rows_query = con.execute(
            "Select count(*) from transfermarkt.tm_to_ws_ids"
        )
    initial_rows = int([r[0] for r in rows_query][0])
    migr_rows = len(df.index)

    def test_migration_data(self):
        self.assertGreater(
            self.migr_rows / self.initial_rows,
            0.90,
            "Scraped data is lower than 90% of the initial data",
        )

    def test_player_ids(self):
        player_ids = [i for i in self.df["playerId"] if pd.isnull(i)]
        self.assertEqual(
            0,
            len(player_ids),
            f"There are {len(player_ids)} player/s with NULL id/s",
        )


# 1 Need to check for scraped data count. Based on (Init count, % from which
# it is ok to assume that the task is finished)
# 2 Check if the given data is ok

# 3 TO DO: send email with the results

# test = TestBeforeMigration()

# test.test_player_ids()
