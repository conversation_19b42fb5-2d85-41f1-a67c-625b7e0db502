import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from uuid import uuid4

cnx = create_engine(postgres_prod_str)
pd.set_option("max_columns", None)

schema = "crm"

requests_to_transfer_df = pd.read_sql(
    f"""select * from {schema}.contacts c where c.organization_id = 'ebfdb98e-9af4-4ba0-a437-4ce1997132e1' 
    and contact_organization = 'SPOCS' and contact_type = 'partner'""",
    cnx,
)

try:
    contacts_to_add = []
    for index, request in requests_to_transfer_df.iterrows():
        dd = request.to_dict()
        dd["organization_id"] = "92f31416-f6ec-4569-a7cb-4f280835ae4f"
        dd["id"] = uuid4()
        contacts_to_add.append(dd)
    df_to_save = pd.DataFrame(contacts_to_add)
    df_to_save.to_sql(
        "contacts", cnx, schema=schema, index=None, if_exists="append"
    )
except Exception as e:
    print(e)
    pass
