import asyncio
import os

from src.data_collection.wyscout.v2.SeasonsXOrchestrator import (
    SeasonsXOrchestrator,
)
from src.data_collection.wyscout.v2.Updates<PERSON>hecker import Up<PERSON><PERSON>he<PERSON>
from src.data_collection.wyscout.v2.SeasonsTeamsUpdater import (
    SeasonsTeamsUpdater,
)


async def main():
    from_scratch = True  # means that we are not using changed objescts endpoint
    phantom_objects = None
    collection_mode = os.environ["COLLECTION_MODE"]
    if collection_mode == "initial":
        only_active = False  # means we are looping through all seasons
    elif collection_mode == "update":
        only_active = (
            True  # means that we are only looping through active seasons
        )
        phantom_objects = ["teams"]
    else:
        raise ValueError("Invalid collection mode")

    seasons_teams_checker = UpdatesChecker(
        table_name="seasons_teams_for_collection",
        # it is key to note that here we have an object type that is not like the real objects
        # (e.g. player, team, match) but since we are never updating seasons_x not from scratch
        # we are not calling the api endpoint and this doesnt matter
        object_type="seasons_teams",
        id_name="seasonId",
        from_scratch=from_scratch,
        all_objects_table="seasons",
        only_active=only_active,
    )
    seasons_teams_updater = SeasonsTeamsUpdater(
        "seasons_teams", "teamId", only_active
    )
    orc = SeasonsXOrchestrator(
        batch_size=5000,
        updates_checker=seasons_teams_checker,
        updater=seasons_teams_updater,
        phantom_objects=phantom_objects,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
