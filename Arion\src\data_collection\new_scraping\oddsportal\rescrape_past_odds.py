from src.data_collection.new_scraping.scraping.scrape_past_odds import (
    ScrapePastOdds,
)
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
import asyncio
import pandas as pd

pd.set_option("display.max_columns", 15)
engine = create_engine(postgres_prod_str)

if __name__ == "__main__":
    scraper = ScrapePastOdds()

    bookie_with_dds = []
    match_dd = []


    # Supply league url
    for i in asyncio.run(scraper.scrape_past_seasons(
        "https://www.oddsportal.com/soccer/england/premier-league"
    )):
        for match in i:
            [bookie_with_dds.append(el) for el in match[0].values()]
            match_dd.append(match[1])

    odds_df = pd.DataFrame(bookie_with_dds)
    match_df = pd.DataFrame(match_dd)

    odds_df.to_sql("past_match_odds", engine, "oddsportal",
     if_exists="append", index=False)

    match_df.to_sql("past_matches", engine, "oddsportal",
     if_exists="append", index=False)
