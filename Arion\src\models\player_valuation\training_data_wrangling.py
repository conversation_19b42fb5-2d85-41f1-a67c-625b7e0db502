from pv.data_wrangling_training import PlayerValuationDataWrangler
import src.models.player_valuation.utils.database_connect as dbconnect
from src.models.player_valuation.utils.config import *


def main():
    cnx_prod = dbconnect.main()
    PV = PlayerValuationDataWrangler(cnx_prod, num_injury_categories, read_mappings, rank_api_url,
        min_games, min_pct, min_tagged, time_decay, game_q_scaling)
    
    PV.read_fact_tables()
    PV.read_transfer_tables()
    PV.get_historical_player_transfers(wrangling_type = 'training')
    PV.read_injuries()
    PV.read_player_area_info()
    PV.read_player_role_pct()
    # PV.get_player_popularity()
    PV.merge_metadata_into_transfers()
    PV.transform_transfers(compare_to_player_appearance_avg = True)
    # PV.get_player_performance()
    PV.get_modelling_df(write_to_db=True)
    PV.clean_feature_families()
    PV.clean_tmp_player_table()
    if not read_mappings:
        PV.write_dicts_to_db()


if __name__ == "__main__":
    main()