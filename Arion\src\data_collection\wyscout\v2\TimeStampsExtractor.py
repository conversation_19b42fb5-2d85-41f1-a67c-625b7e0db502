class TimeStampsExtractor:
    @staticmethod
    def prep(events):
        match_periods = {x["matchPeriod"] for x in events}
        if any(x not in match_periods for x in ["1H", "2H"]):
            raise ValueError("Games doesnt have first or second half")
        # if we want last moment of game, take the video timestamp of the non-penalty events
        ts_dict = {
            "last_period": max(
                filter(lambda x: x["matchPeriod"] != "P", events),
                key=lambda x: float(x["videoTimestamp"]),
            )["matchPeriod"]
        }

        ts_dict["first_half_start"] = float(
            min(
                events,
                key=lambda x: float(x["videoTimestamp"]),
            )["videoTimestamp"]
        )

        ts_dict["total"] = float(
            max(
                filter(lambda x: x["matchPeriod"] != "P", events),
                key=lambda x: float(x["videoTimestamp"]),
            )["videoTimestamp"]
        )

        # since there are plenty of fucked games on wyscout's end, we need to handle the case where there is only one half:
        # if ts_dict["last_period"] == "1H":
        #     ts_dict["first_half_end"] = ts_dict["total"]
        #     ts_dict["total_duration"] = ts_dict["total"] / 60
        #     ts_dict["first_half_duration"] = (
        #         ts_dict["first_half_end"] - ts_dict["first_half_start"]
        #     )
        #     return ts_dict

        ts_dict["first_half_end"] = float(
            max(
                filter(lambda x: x["matchPeriod"] == "1H", events),
                key=lambda x: float(x["videoTimestamp"]),
            )["videoTimestamp"]
        )
        ts_dict["second_half_start"] = float(
            min(
                filter(lambda x: x["matchPeriod"] == "2H", events),
                key=lambda x: float(x["videoTimestamp"]),
            )["videoTimestamp"]
        )
        ts_dict["second_half_end"] = float(
            max(
                filter(lambda x: x["matchPeriod"] == "2H", events),
                key=lambda x: float(x["videoTimestamp"]),
            )["videoTimestamp"]
        )
        try:
            ts_dict["e1_start"] = float(
                min(
                    filter(lambda x: x["matchPeriod"] == "1E", events),
                    key=lambda x: float(x["videoTimestamp"]),
                )["videoTimestamp"]
            )
        except:
            ts_dict["e1_start"] = 0

        try:
            ts_dict["e1_end"] = float(
                max(
                    filter(lambda x: x["matchPeriod"] == "1E", events),
                    key=lambda x: float(x["videoTimestamp"]),
                )["videoTimestamp"]
            )
        except:
            ts_dict["e1_end"] = 0
        try:
            ts_dict["e2_start"] = float(
                min(
                    filter(lambda x: x["matchPeriod"] == "2E", events),
                    key=lambda x: float(x["videoTimestamp"]),
                )["videoTimestamp"]
            )
        except:
            ts_dict["e2_start"] = 0

        ts_dict["gap_first_second"] = (
            ts_dict["second_half_start"] - ts_dict["first_half_end"]
        )

        ts_dict["gap_second_e1"] = (
            ts_dict["e1_start"] - ts_dict["second_half_end"]
            if ts_dict["last_period"] in ("1E", "2E")
            else 0
        )

        ts_dict["gap_e1_e2"] = (
            ts_dict["e2_start"] - ts_dict["e1_end"]
            if ts_dict["last_period"] == "2E"
            else 0
        )
        ts_dict["first_half_duration"] = (
            ts_dict["first_half_end"] - ts_dict["first_half_start"]
        )
        ts_dict["second_half_duration"] = (
            ts_dict["second_half_end"] - ts_dict["second_half_start"]
        )

        ts_dict["total_duration"] = (
            ts_dict["total"]
            - ts_dict["gap_first_second"]
            - ts_dict["gap_e1_e2"]
            - ts_dict["gap_second_e1"]
            - ts_dict["first_half_start"]
        ) / 60

        return ts_dict
