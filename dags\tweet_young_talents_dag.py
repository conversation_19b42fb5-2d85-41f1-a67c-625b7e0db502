from datetime import datetime, timedelta
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator

from dag_settings import workdir

dag_params = {
    "dag_id": "tweet_young_talents_dag",
    "start_date": datetime(2021, 9, 14),
    "schedule_interval": timedelta(minutes=20),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>"],
        "email_on_failure": True,
    },
}

with DAG(**dag_params) as dag:

    tweet = BashOperator(
        task_id="tweet",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        arionflow_venv/bin/python3 src/reporting/talent_hunter/tweet_young_lions.py""",
    )
    tweet