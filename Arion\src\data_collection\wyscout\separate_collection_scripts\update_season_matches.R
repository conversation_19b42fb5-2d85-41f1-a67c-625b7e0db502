# # Create Database Structure
source('src/data_collection/wyscout/00_libs.R')

owt = as.logical(Sys.getenv("OVERWRITE_SEASON_TABLES"))

# Delete matches that are in the future
date_threshold = Sys.Date()
matches_to_drop_query = paste(
  "delete from seasons_matches where TO_TIMESTAMP(date, 'YYYY/MM/DD HH24:MI:SS') >",
  paste0("'", date_threshold, "'")
)

exec = dbSendStatement(con, matches_to_drop_query)
rows_affected = dbGetRowsAffected(exec)
dbClearResult(exec)

try({dbDisconnect(con)})
con = make_connection()

competition_seasons = dbReadTable(con, 'competition_seasons')
competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')
competition_seasons = competition_seasons[competition_seasons$competitionId%in%competitions$competitionId,]

if(!owt){
  
  downloaded_seasons = dbGetQuery(con, 'select distinct "seasonId" from seasons_matches')

  # Get final season for each competition
  final_seasons = competition_seasons %>%
    group_by(competitionId) %>%
    summarise(seasonId = max(seasonId, na.rm = T))

  competition_seasons = competition_seasons[!competition_seasons$seasonId%in%downloaded_seasons$seasonId,]

  # Combine missing seasons and final competition seasons
  competition_seasons = rbind(competition_seasons, final_seasons)

  # Get unique seasons
  competition_seasons = unique(competition_seasons)
  
  written_season_matches = dbGetQuery(con, 'select distinct "seasonId", "matchId" from seasons_matches')

}

seasons_matches = list()
cnt = 0

for (comp_id in competition_seasons$seasonId[1:length(competition_seasons$seasonId)]) {
  cnt = cnt + 1
  Sys.sleep(0.08)
  resp = GET(
    paste0(
      'https://apirest.wyscout.com/v2/seasons/',
      comp_id,
      '/matches'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  
  cont = content(resp)
  if (length(cont$matches) > 0) {
    try({
      mtch = cont$matches
      mtch = lapply(mtch, remove_null)
      com_matches = plyr::ldply(mtch, data.frame, stringsAsFactors = F)
      
      home_away = sapply(strsplit(com_matches[['label']], split = ', ', fixed = T), function(x)
        x[[1]])
      home = sapply(strsplit(home_away, split = ' - ', fixed = T), function(x)
        x[[1]])
      away = sapply(strsplit(home_away, split = ' - ', fixed = T), function(x)
        x[[2]])
      
      score = sapply(strsplit(com_matches[['label']], split = ', ', fixed = T), function(x)
        x[[2]])
      home_score = as.numeric(sapply(strsplit(
        score, split = '-', fixed = T
      ), function(x)
        x[[1]]))
      away_score = as.numeric(sapply(strsplit(
        score, split = '-', fixed = T
      ), function(x)
        x[[2]]))
      
      com_matches['home_team'] = home
      com_matches['away_team'] = away
      com_matches['home_score'] = home_score
      com_matches['away_score'] = away_score
      com_matches['home_win'] = as.numeric(com_matches[['home_score']] > com_matches[['away_score']])
      com_matches['draw'] = as.numeric(com_matches[['home_score']] == com_matches[['away_score']])
      com_matches['seasonId'] = comp_id
      seasons_matches[[length(seasons_matches) + 1]] = com_matches
    })
  }
  
  
}

seasons_matches = do.call(plyr::rbind.fill, seasons_matches)

idx = which(!duplicated(seasons_matches$matchId))
seasons_matches = seasons_matches[idx,]

colnames(seasons_matches) = gsub('.', '_', colnames(seasons_matches), fixed = T)
colnames(seasons_matches) = gsub('wyId', 'matchId', colnames(seasons_matches))

if(!owt){
  seasons_matches = seasons_matches[!paste(seasons_matches$matchId, seasons_matches$seasonId) %in%
                                      paste(written_season_matches$matchId, written_season_matches$seasonId),]
}

seasons_matches = seasons_matches %>%
  filter(as.Date(date) <= Sys.Date())

try({dbDisconnect(con)})
con = make_connection()
if(nrow(seasons_matches) > 0){
  RPostgreSQL::dbWriteTable(
  con,
  'seasons_matches',
  seasons_matches,
  overwrite = owt,
  append = !owt,
  row.names = F,
  rownames = F
  )
}
