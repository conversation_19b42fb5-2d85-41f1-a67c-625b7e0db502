from pydantic import BaseSettings


class ApiSettings(BaseSettings):
    BASE_URL: str = "https://shadow-eleven-backend-aolebn4toq-ew.a.run.app"
    SHADOW_ELEVEN_USR: str = "<EMAIL>"
    SHADOW_ELEVEN_PASS: str


class LookupSettings(BaseSettings):
    LOOKUP_API_USR: str
    LOOKUP_API_PASS: str
    LOOKUP_API_URL: str = "https://lookup-aolebn4toq-ew.a.run.app"


class Settings(ApiSettings, LookupSettings):
    class Config:
        env_file = ".env"


settings = Settings()
