from abc import ABC, abstractmethod

import pandas as pd

from src.helper_funcs import (
    fast_read_sql,
    get_sql_array_str,
)


class SeasonsIdHandler(ABC):
    def __init__(self, table_name, id_name, cnx_prod):
        self.table_name = table_name
        self.id_name = id_name
        self.cnx_prod = cnx_prod
        self.collection_list = None

    def get_objects_for_collection(self, source_table):
        query = self.query.replace("{source_table}", source_table)
        id_series = fast_read_sql(query, self.cnx_prod)["seasonId"]
        self.collection_list = id_series[id_series > -1].tolist()
        return self.collection_list

    @abstractmethod
    def handle_output(self, seasons_obj: pd.DataFrame, obj: pd.DataFrame):
        pass


class ActiveSeasonsIdHandler(SeasonsIdHandler):
    def __init__(
        self,
        table_name,
        id_name,
        cnx_prod,
    ):
        super().__init__(table_name, id_name, cnx_prod)
        self.query = """SELECT "seasonId" from wyscout.{source_table}
                    WHERE active = 'true' """

    def handle_output(self, seasons_obj: pd.DataFrame, obj: pd.DataFrame):
        active_ids_tuple = get_sql_array_str(self.collection_list)
        # this will fail if the table doesnt not exist, but the table should always be created in the inial run
        already_written_active = pd.read_sql(
            f"""SELECT * FROM wyscout.{self.table_name} 
                                            WHERE "{self.id_name}" IN {active_ids_tuple} """,
            self.cnx_prod,
        )
        seasons_obj = seasons_obj[
            ~(
                seasons_obj.seasonId.astype(str)
                + seasons_obj[self.id_name].astype(str)
            ).isin(
                already_written_active.seasonId.astype(str)
                + already_written_active[self.id_name].astype(str)
            )
        ]
        return seasons_obj


class AllSeasonsIdHandler(SeasonsIdHandler):
    def __init__(self, table_name, id_name, cnx_prod):
        super().__init__(table_name, id_name, cnx_prod)
        self.query = """SELECT "seasonId" from wyscout.{source_table} """

    def handle_output(self, seasons_obj: pd.DataFrame, obj: pd.DataFrame):
        return (
            {
                self.table_name.replace("seasons_", ""): obj,
                self.table_name: seasons_obj,
            }
            if obj is not None
            else {self.table_name: seasons_obj}
        )
