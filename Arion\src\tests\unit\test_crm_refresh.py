import pytest
import sys

from src.data_collection.crm_refresh.CrmRefresher import CrmRefresher
from src.data_collection.crm_refresh.config import settings

# python -m pytest src/tests/unit/test_crm_refresh.py  from ~/ArionFlow/Arion


@pytest.fixture
def players_refresher() -> CrmRefresher:
    return CrmRefresher(
        settings,
        "players",
        ["team_name", "teamId", "tm_link", "tm_value", "agency"],
    )


@pytest.fixture
def teams_refresher() -> CrmRefresher:
    return CrmRefresher(
        settings, "team_requests", ["division_level", "segment"]
    )


@pytest.fixture
def example_crm_players():
    return [
        {
            "_id": 673678,
            "created_at": "2022-02-22T12:48:12.337557",
            "last_updated": "2022-02-22T12:48:12.337566",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": None,
            "assigned_to": "<PERSON>",
            "playerId": 673678,
            "firstName": "<PERSON>",
            "lastName": "Odad<PERSON>",
            "foot": "right",
            "control_stage": "negotiation",
            "position": ["dmc"],
            "quality": 7,
            "potential": 9,
            "teamId": 17138,
            "team_name": "Metalac GM",
            "birth_date": "2000-11-25",
            "tm_link": "https://www.transfermarkt.com/richard-odada/profil/spieler/675238",
            "tm_value": 300000.0,
            "club_asking_price": None,
            "transfer_period": ["summer_2023", "winter_2024"],
            "deals_with_player": [""],
            "agency": "BGM",
            "phone_number": None,
            "description": None,
            "current_gross_salary": None,
            "source": "Pencho",
            "scouting_reports": [],
            "priority_player": False,
            "proactively_scouted": True,
            "xtransfer_low": None,
            "xtransfer_high": None,
            "xgross_salary_low": None,
            "xgross_salary_high": None,
            "xtransfer_next_high": None,
            "xtransfer_next_low": None,
            "xgross_salary_next_high": None,
            "xgross_salary_next_low": None,
            "rec_max_investment": None,
        },
        {
            "_id": 270339,
            "created_at": "2022-03-14T22:10:23.119057",
            "last_updated": "2022-03-14T22:10:23.119064",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": None,
            "assigned_to": None,
            "playerId": 270339,
            "firstName": "Alejandro",
            "lastName": "Romero Gamarra",
            "foot": "left",
            "control_stage": "check_360",
            "position": ["cam"],
            "quality": 8,
            "potential": 8,
            "teamId": 16487,
            "team_name": "Al Taawon",
            "birth_date": "1995-01-11",
            "tm_link": "https://www.transfermarkt.com/alejandro-romero-gamarra/profil/spieler/355817",
            "tm_value": 5000000.0,
            "club_asking_price": None,
            "transfer_period": ["winter_2022"],
            "deals_with_player": [],
            "agency": None,
            "phone_number": None,
            "description": None,
            "current_gross_salary": None,
            "source": None,
            "scouting_reports": [],
            "priority_player": False,
            "proactively_scouted": False,
            "xtransfer_low": None,
            "xtransfer_high": None,
            "xgross_salary_low": None,
            "xgross_salary_high": None,
            "xtransfer_next_high": None,
            "xtransfer_next_low": None,
            "xgross_salary_next_high": None,
            "xgross_salary_next_low": None,
            "rec_max_investment": None,
        },
        {
            "_id": 426412,
            "created_at": "2022-03-14T22:10:23.119057",
            "last_updated": "2022-03-14T22:10:23.119064",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": None,
            "assigned_to": None,
            "playerId": 426412,
            "firstName": "Jan",
            "lastName": "Vodháněl",
            "foot": "left",
            "control_stage": "check_360",
            "position": ["cmf"],
            "quality": 2,
            "potential": 3,
            "teamId": 8869,
            "team_name": "Admira",
            "birth_date": "1997-04-25",
            "tm_link": "https://www.transfermarkt.com/jan-vodhanel/profil/spieler/430250",
            "tm_value": 350000.0,
            "club_asking_price": None,
            "transfer_period": ["winter_2022"],
            "deals_with_player": [],
            "agency": "Harry Schiestl",
            "phone_number": None,
            "description": None,
            "current_gross_salary": None,
            "source": None,
            "scouting_reports": [],
            "priority_player": False,
            "proactively_scouted": False,
            "xtransfer_low": None,
            "xtransfer_high": None,
            "xgross_salary_low": None,
            "xgross_salary_high": None,
            "xtransfer_next_high": None,
            "xtransfer_next_low": None,
            "xgross_salary_next_high": None,
            "xgross_salary_next_low": None,
            "rec_max_investment": None,
        },
    ]


@pytest.fixture
def example_crm_requests():
    return [
        {
            "_id": "5ca7bdb4-30c8-4e5f-8cb0-e97a888559ed",
            "created_at": "2022-01-20T14:41:37.797497",
            "last_updated": "2022-01-20T14:41:37.797505",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": None,
            "teamId": 8738,
            "name": "Rapid Wien",
            "area_name": "Austria",
            "division_level": 1,
            "position": "cmf",
            "segment": 3,
            "foot": "no_preference",
            "stage": "request_intake",
            "max_age": 30,
            "max_value": 1000000,
            "max_net_salary": 1000000,
            "transfer_period": ["winter_2022"],
            "source": "some amigo",
            "reason_for_outcome": None,
            "description": None,
            "proposed_players": [],
            "type": ["transfer", "free_transfer"],
        },
        {
            "_id": "029416ca-734e-47ab-ab83-6aa1538441f8",
            "created_at": "2022-01-28T14:08:16.414946",
            "last_updated": "2022-01-28T14:08:16.414951",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": None,
            "teamId": 3795,
            "name": "Troyes",
            "area_name": "France",
            "division_level": 1,
            "position": "cb",
            "segment": 3,
            "foot": "no_preference",
            "stage": "request_intake",
            "max_age": 24,
            "max_value": 0,
            "max_net_salary": 1000000,
            "transfer_period": ["winter_2022"],
            "source": "Ludovic Megret",
            "reason_for_outcome": None,
            "description": (
                "Troyes need a CB and a winger, free or loan with option,"
                " immediate impact. Cannot pay, max net salary is 450k"
                " eur/year."
            ),
            "proposed_players": [],
            "type": ["loan_with_option"],
        },
        {
            "_id": "296c4425-f5a1-4bed-a47d-7fa5aa9d62bf",
            "created_at": "2022-01-28T14:08:16.414946",
            "last_updated": "2022-01-28T14:08:16.416254",
            "created_by": "<EMAIL>",
            "notes": None,
            "changelog": [
                {
                    "edit_at": "2022-01-28T14:08:16.414032",
                    "edit_by": "<EMAIL>",
                    "field": "max_value",
                    "previous": 0,
                    "updated": 250000,
                },
                {
                    "edit_at": "2022-01-28T14:08:16.414032",
                    "edit_by": "<EMAIL>",
                    "field": "proposed_players",
                    "previous": [],
                    "updated": [
                        {"name": "Martin Smolenski", "playerId": 599384}
                    ],
                },
                {
                    "edit_at": "2022-01-28T14:08:16.414032",
                    "edit_by": "<EMAIL>",
                    "field": "proposed_players",
                    "previous": [
                        {"name": "Martin Smolenski", "playerId": 599384}
                    ],
                    "updated": [
                        {"name": "Martin Smolenski", "playerId": 599384},
                        {
                            "name": "Gonçalo Oscar Albuquerque Borges",
                            "playerId": 536059,
                        },
                    ],
                },
            ],
            "teamId": 3795,
            "name": "Troyes",
            "area_name": "France",
            "division_level": 1,
            "position": "rw",
            "segment": 3,
            "foot": "no_preference",
            "stage": "request_intake",
            "max_age": 24,
            "max_value": 250000,
            "max_net_salary": 1000000,
            "transfer_period": ["winter_2022"],
            "source": "Ludovic Megret",
            "reason_for_outcome": None,
            "description": (
                "Troyes need a CB and a winger, free or loan with option,"
                " immediate impact. Cannot pay, max net salary is 450k"
                " eur/year."
            ),
            "proposed_players": [
                {"name": "Martin Smolenski", "playerId": 599384},
                {
                    "name": "Gonçalo Oscar Albuquerque Borges",
                    "playerId": 536059,
                },
            ],
            "type": ["loan_with_option"],
        },
    ]


@pytest.fixture
def lookup_players_no_change():
    return [
        {
            "playerId": 673678,
            "firstName": "Richard",
            "lastName": "Odada",
            "name": "Richard Odada",
            "team_name": "Metalac GM",
            "teamId": 17138,
            "birth_date": "2000-11-25",
            "foot": "right",
            "passport": "Kenya",
            "birth_area": "Kenya",
            "tm_value": 300000.0,
            "tm_link": "https://www.transfermarkt.com/richard-odada/profil/spieler/675238",
            "agency": "BGM",
        },
        {
            "playerId": 270339,
            "firstName": "Alejandro",
            "lastName": "Romero Gamarra",
            "name": "Alejandro Romero Gamarra",
            "team_name": "Al Taawon",
            "teamId": 16487,
            "birth_date": "1995-01-11",
            "foot": "left",
            "passport": "Paraguay",
            "birth_area": "Argentina",
            "tm_value": 5000000.0,
            "tm_link": "https://www.transfermarkt.com/alejandro-romero-gamarra/profil/spieler/355817",
            "agency": None,
        },
        {
            "playerId": 426412,
            "firstName": "Jan",
            "lastName": "Vodháněl",
            "name": "Jan Vodháněl",
            "team_name": "Admira",
            "teamId": 8869,
            "birth_date": "1997-04-25",
            "foot": "left",
            "passport": "Czech Republic",
            "birth_area": "Czech Republic",
            "tm_value": 350000.0,
            "tm_link": "https://www.transfermarkt.com/jan-vodhanel/profil/spieler/430250",
            "agency": "Harry Schiestl",
        },
    ]


@pytest.fixture
def lookup_teams_no_change():
    return [
        {
            "teamId": 8738,
            "name": "Rapid Wien",
            "division_level": 1,
            "area_name": "Austria",
            "segment": 3,
            "smoothed_rating": 1874.38652995182,
        },
        {
            "teamId": 3795,
            "name": "Troyes",
            "division_level": 1,
            "area_name": "France",
            "segment": 3,
            "smoothed_rating": 1853.58597436486,
        },
    ]


@pytest.fixture
def player_refresher_w_objects(
    players_refresher, lookup_players_no_change, example_crm_players
):
    players_refresher.set_crm_objects(example_crm_players)
    for obj in lookup_players_no_change:
        players_refresher.crm_objects[obj["playerId"]]["current"] = obj
    return players_refresher


@pytest.fixture
def team_refresher_w_objects(
    teams_refresher, lookup_teams_no_change, example_crm_requests
):
    teams_refresher.set_crm_objects(example_crm_requests)
    for obj in lookup_teams_no_change:
        respective_req_id = [
            x["crm"]["_id"]
            for x in teams_refresher.crm_objects.values()
            if x["crm"]["teamId"] == obj["teamId"]
        ][0]
        teams_refresher.crm_objects[respective_req_id]["current"] = obj
    return teams_refresher


@pytest.fixture
def refresher_w_called_set_players(
    players_refresher: CrmRefresher,
) -> CrmRefresher:
    objects = players_refresher.get_crm_objects_from_api()
    players_refresher.set_crm_objects(objects)
    return players_refresher


@pytest.fixture
def refresher_w_called_set_crm_and_lookup_items(
    refresher_w_called_set_players: CrmRefresher,
) -> CrmRefresher:
    refresher_w_called_set_players.get_current_state_of_objects()
    return refresher_w_called_set_players


@pytest.mark.parametrize(
    "setting",
    [
        "BASE_URL",
        "SHADOW_ELEVEN_USR",
        "SHADOW_ELEVEN_PASS",
        "LOOKUP_API_USR",
        "LOOKUP_API_PASS",
        "LOOKUP_API_URL",
    ],
)
def test_settings(setting):
    assert settings.dict().get(setting) is not None


def test_token(players_refresher):
    assert players_refresher.token is not None


def test_teams_ids_for_checking_types(teams_refresher):
    ids = teams_refresher.ids_for_checking
    assert all(isinstance(x[0], str) and isinstance(x[1], int) for x in ids)


def test_compute_player_changes_no_change(player_refresher_w_objects):
    player_refresher_w_objects.compute_changes()
    assert not player_refresher_w_objects.changes


def test_compute_team_changes_no_change(team_refresher_w_objects):
    team_refresher_w_objects.compute_changes()
    assert not team_refresher_w_objects.changes


def test_commit_updates_no_change(team_refresher_w_objects):
    team_refresher_w_objects.compute_changes()
    team_refresher_w_objects.commit_updates()
    assert not team_refresher_w_objects.commited_changes


def test_compute_changes_w_player_change(player_refresher_w_objects):
    player_refresher_w_objects.crm_objects[673678]["current"]["teamId"] = 420
    player_refresher_w_objects.crm_objects[673678]["current"][
        "team_name"
    ] = None
    player_refresher_w_objects.crm_objects[673678]["current"]["tm_value"] = 999
    player_refresher_w_objects.crm_objects[673678]["current"][
        "tm_link"
    ] = player_refresher_w_objects.crm_objects[673678]["crm"]["tm_link"]
    player_refresher_w_objects.crm_objects[673678]["current"]["playerId"] = 420
    player_refresher_w_objects.compute_changes()
    assert len(player_refresher_w_objects.changes[673678]) == 3


def test_compute_changes_updated_crm_objects(player_refresher_w_objects):
    crm_objects = player_refresher_w_objects.crm_objects
    player_refresher_w_objects.crm_objects[673678]["current"]["teamId"] = 420
    player_refresher_w_objects.compute_changes()
    assert (
        player_refresher_w_objects.crm_objects[673678]["crm"]["teamId"] == 420
    )


def test_crm_objects_players(refresher_w_called_set_players):
    assert len(refresher_w_called_set_players.crm_objects) > 0


def test_compute_changes_w_team_change(team_refresher_w_objects):
    team_refresher_w_objects.crm_objects[
        "5ca7bdb4-30c8-4e5f-8cb0-e97a888559ed"
    ]["current"]["division_level"] = 420
    team_refresher_w_objects.crm_objects[
        "5ca7bdb4-30c8-4e5f-8cb0-e97a888559ed"
    ]["current"]["segment"] = 1
    team_refresher_w_objects.crm_objects[
        "5ca7bdb4-30c8-4e5f-8cb0-e97a888559ed"
    ]["current"]["area_name"] = "China"
    team_refresher_w_objects.compute_changes()
    assert (
        len(
            team_refresher_w_objects.changes[
                "5ca7bdb4-30c8-4e5f-8cb0-e97a888559ed"
            ]
        )
        == 2
    )


def test_player_ids_for_checking_types(refresher_w_called_set_players):
    ids = refresher_w_called_set_players.ids_for_checking
    assert all(isinstance(x[0], int) and isinstance(x[1], int) for x in ids)



@pytest.mark.skip(reason="skipping because takes long to compute")
def test_get_current_state_of_objects(
    refresher_w_called_set_crm_and_lookup_items,
):
    rf = refresher_w_called_set_crm_and_lookup_items
    assert len([x["crm"] for x in rf.crm_objects.values()]) == len(
        [x["current"] for x in rf.crm_objects.values()]
    )
