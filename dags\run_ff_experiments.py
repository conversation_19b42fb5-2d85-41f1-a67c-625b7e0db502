from datetime import datetime, timedelta
import time

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

from dag_settings import workdir_ff

dag_params = {
    "dag_id": "run_ff_experiments",
    "start_date": datetime(2022, 1, 20),
    "schedule_interval": None,
    "params": {"workdir": workdir_ff},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    baseline_probabilistic_lineups = BashOperator(
        task_id="baseline_probabilistic_lineups",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/baseline.py """,
    )

    baseline_mip_lineups = BashOperator(
        task_id="baseline_mip_lineups",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/mip.py """,
    )

    # avg_points_selection_mip = BashOperator(
    #     task_id="avg_points_selection_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/avg_points_selection_mip.py """,
    # )

    # avg_points_selection_prob = BashOperator(
    #     task_id="avg_points_selection_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/avg_points_selection_prob.py """,
    # )

    # cluster_players_mip = BashOperator(
    #     task_id="cluster_players_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/cluster_players_mip.py """,
    # )

    # cluster_players_prob = BashOperator(
    #     task_id="cluster_players_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/cluster_players_prob.py """,
    # )

    max_4_clubs_prob = BashOperator(
        task_id="max_4_clubs_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_4_clubs_prob.py """,
    )

    max_6_clubs_prob = BashOperator(
        task_id="max_6_clubs_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_6_clubs_prob.py """,
    )

    max_7_clubs_prob = BashOperator(
        task_id="max_7_clubs_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_7_clubs_prob.py """,
    )

    # max_entropy_selection_mip = BashOperator(
    #     task_id="max_entropy_selection_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/max_entropy_selection_mip.py """,
    # )

    max_entropy_selection_prob = BashOperator(
        task_id="max_entropy_selection_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_entropy_selection_prob.py """,
    )

    # max_gini_selection_mip = BashOperator(
    #     task_id="max_gini_selection_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/max_gini_selection_mip.py """,
    # )

    max_gini_selection_prob = BashOperator(
        task_id="max_gini_selection_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_gini_selection_prob.py """,
    )

    # ml_estimation_mip = BashOperator(
    #     task_id="ml_estimation_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/ml_estimation_mip.py """,
    # )

    ml_estimation_prob = BashOperator(
        task_id="ml_estimation_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/ml_estimation_prob.py """,
    )

    # no_club_selection_prob = BashOperator(
    #     task_id="no_club_selection_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/no_club_selection_prob.py """,
    # )

    # no_home_team_adv_prob = BashOperator(
    #     task_id="no_home_team_adv_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/no_home_team_adv_prob.py """,
    # )

    # no_max_clubs_prob = BashOperator(
    #     task_id="no_max_clubs_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/no_max_clubs_prob.py """,
    # )

    # only_top8_clubs_prob = BashOperator(
    #     task_id="only_top8_clubs_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/only_top8_clubs_prob.py """,
    # )

    # use_only_433_prob = BashOperator(
    #     task_id="use_only_433_prob",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/use_only_433_prob.py """,
    # )

    ml_estimation_max_gini_prob = BashOperator(
        task_id="ml_estimation_max_gini_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/ml_estimation_max_gini_prob.py """,
    )

    ml_estimation_max_entropy_prob = BashOperator(
        task_id="ml_estimation_max_entropy_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/ml_estimation_max_entropy_prob.py """,
    )

    # ml_estimation_max_gini_mip = BashOperator(
    #     task_id="ml_estimation_max_gini_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/ml_estimation_max_gini_mip.py """,
    # )

    # ml_estimation_max_entropy_mip = BashOperator(
    #     task_id="ml_estimation_max_entropy_mip",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/evaluation/experiments/ml_estimation_max_entropy_mip.py """,
    # )

    max150_lineups_prob = BashOperator(
        task_id="max150_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max150_lineups_prob.py """,
    )

    max100_lineups_prob = BashOperator(
        task_id="max100_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max100_lineups_prob.py """,
    )

    max80_lineups_prob = BashOperator(
        task_id="max80_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max80_lineups_prob.py """,
    )

    max40_lineups_prob = BashOperator(
        task_id="max40_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max40_lineups_prob.py """,
    )

    max10_lineups_prob = BashOperator(
        task_id="max10_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max10_lineups_prob.py """,
    )

    max1_lineup_prob = BashOperator(
        task_id="max1_lineup_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max1_lineup_prob.py """,
    )

    max150_lineups_ml_prob = BashOperator(
        task_id="max150_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max150_lineups_ml_prob.py """,
    )

    max100_lineups_ml_prob = BashOperator(
        task_id="max100_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max100_lineups_ml_prob.py """,
    )

    max80_lineups_ml_prob = BashOperator(
        task_id="max80_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max80_lineups_ml_prob.py """,
    )

    max40_lineups_ml_prob = BashOperator(
        task_id="max40_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max40_lineups_ml_prob.py """,
    )

    max10_lineups_ml_prob = BashOperator(
        task_id="max10_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max10_lineups_ml_prob.py """,
    )

    max1_lineup_ml_prob = BashOperator(
        task_id="max1_lineup_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max1_lineup_ml_prob.py """,
    )

    max_6_clubs_ml_prob = BashOperator(
        task_id="max_6_clubs_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_6_clubs_ml_prob.py """,
    )

    max_4_clubs_ml_prob = BashOperator(
        task_id="max_4_clubs_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_4_clubs_ml_prob.py """,
    )

    max_7_clubs_ml_prob = BashOperator(
        task_id="max_7_clubs_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_7_clubs_ml_prob.py """,
    )

    max_4_clubs_max_150_lineups_prob = BashOperator(
        task_id="max_4_clubs_max_150_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_4_clubs_max_150_lineups_prob.py """,
    )

    max_6_clubs_max_150_lineups_prob = BashOperator(
        task_id="max_6_clubs_max_150_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_6_clubs_max_150_lineups_prob.py """,
    )

    max_7_clubs_max_150_lineups_prob = BashOperator(
        task_id="max_7_clubs_max_150_lineups_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_7_clubs_max_150_lineups_prob.py """,
    )

    max_4_clubs_max_150_lineups_ml_prob = BashOperator(
        task_id="max_4_clubs_max_150_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_4_clubs_max_150_lineups_ml_prob.py """,
    )

    max_6_clubs_max_150_lineups_ml_prob = BashOperator(
        task_id="max_6_clubs_max_150_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_6_clubs_max_150_lineups_ml_prob.py """,
    )

    max_7_clubs_max_150_lineups_ml_prob = BashOperator(
        task_id="max_7_clubs_max_150_lineups_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/max_7_clubs_max_150_lineups_ml_prob.py """,
    )

    home_adv_015_prob = BashOperator(
        task_id="home_adv_015_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_015_prob.py """,
    )

    home_adv_01_prob = BashOperator(
        task_id="home_adv_01_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_01_prob.py """,
    )

    home_adv_005_prob = BashOperator(
        task_id="home_adv_005_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_005_prob.py """,
    )

    home_adv_015_ml_prob = BashOperator(
        task_id="home_adv_015_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_015_ml_prob.py """,
    )

    home_adv_01_ml_prob = BashOperator(
        task_id="home_adv_01_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_01_ml_prob.py """,
    )

    home_adv_005_ml_prob = BashOperator(
        task_id="home_adv_005_ml_prob",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/evaluation/experiments/home_adv_005_ml_prob.py """,
    )

    grouper1 = DummyOperator(task_id="grouper1")
    grouper2 = DummyOperator(task_id="grouper2")
    grouper3 = DummyOperator(task_id="grouper3")
    grouper4 = DummyOperator(task_id="grouper4")
    grouper5 = DummyOperator(task_id="grouper5")
    grouper6 = DummyOperator(task_id="grouper6")

    (
        [
            baseline_probabilistic_lineups,
            baseline_mip_lineups,
            # avg_points_selection_mip
            # avg_points_selection_prob
            # cluster_players_mip
            # cluster_players_prob
            max_4_clubs_prob,
            max_6_clubs_prob,
            max_7_clubs_prob,
        ]
        >> grouper1
        >> [
            max_entropy_selection_prob,
            # max_entropy_selection_mip
            # max_gini_selection_mip
            max_gini_selection_prob,
            # ml_estimation_mip
            ml_estimation_prob,
            ml_estimation_max_entropy_prob,
            ml_estimation_max_gini_prob,
        ]
        >> grouper2
        >> [
            max150_lineups_prob,
            max100_lineups_prob,
            max80_lineups_prob,
            max40_lineups_prob,
            max10_lineups_prob,
        ]
        >> grouper3
        >> [
            max1_lineup_prob,
            max150_lineups_ml_prob,
            max100_lineups_ml_prob,
            max80_lineups_ml_prob,
            max40_lineups_ml_prob,
        ]
        >> grouper4
        >> [
            max10_lineups_ml_prob,
            max1_lineup_ml_prob,
            max_7_clubs_ml_prob,
            max_6_clubs_ml_prob,
            max_4_clubs_ml_prob,
        ]
        >> grouper5
        >> [
            max_4_clubs_max_150_lineups_prob,
            max_6_clubs_max_150_lineups_prob,
            max_7_clubs_max_150_lineups_prob,
            max_4_clubs_max_150_lineups_ml_prob,
            max_6_clubs_max_150_lineups_ml_prob,
        ]
        >> grouper6
        >> [
            max_7_clubs_max_150_lineups_ml_prob,
            home_adv_015_prob,
            home_adv_01_prob,
            home_adv_005_prob,
            home_adv_015_ml_prob,
            home_adv_01_ml_prob,
            home_adv_005_ml_prob,
        ]
    )
    # ml_estimation_max_entropy_mip
    # ml_estimation_max_gini_mip
    # no_club_selection_prob
    # no_home_team_adv_prob
    # no_max_clubs_prob
    # only_top8_clubs_prob
    # use_only_433_prob
