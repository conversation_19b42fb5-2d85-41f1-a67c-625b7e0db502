import requests
from bs4 import BeautifulSoup
import pandas as pd
from requests.api import get
from sqlalchemy import create_engine
from src.helper_funcs import dedupe_table
from settings import PROXIES, postgres_prod_str
URL = "https://www.transfermarkt.com/detailsuche/spielerdetail/suche/31731840/ajax/yw0"
headers = {
    "User-Agent": (
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko)"
        " Chrome/47.0.2526.106 Safari/537.36"
    )
}


class ExampleScraper:
    def __init__(self, url, headers):
        self.url = url
        self.headers = headers
        self.dict = {"tm_player_id": [], "tm_player_url": []}

    def scrape_data(self):
        for i in range(1, 9):
            new_url = f"{self.url}/page/{i}"
            self.add_data(new_url)
        return pd.DataFrame(self.dict)

    def add_data(self, url):
        new_url = url
        soup = get_soup_from_url(new_url, proxies=PROXIES)
        if soup.find("table", attrs={"class": "items"}):
            trs = soup.find_all("tr", attrs={"class": ["odd", "even"]})
            for tr in trs:
                ll = tr.find_all("td", attrs={"class": "hauptlink"})
                a = ll[0].find("a")["href"]
                self.dict["tm_player_id"].append(a.split("/")[-1])
                self.dict["tm_player_url"].append(
                    f"https://www.transfermarkt.com{a}"
                )


def get_soup_from_url(
    url, session=None, header=True, proxies=None, payload=None, method="get"
):
    if proxies is not None:
        proxies = {"https": PROXIES}
    if session is not None:
        req = session.get(url, proxies=proxies)
        text = req.text
    else:
        if header:
            header = {
                "User-Agent": (
                    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML,"
                    " like Gecko) Chrome/49.0.2623.112 Safari/537.36"
                )
            }
            if method == "get":
                req = requests.get(
                    url, headers=header, proxies=proxies, data=payload
                )
            elif method == "post":
                req = requests.post(
                    url, headers=header, proxies=proxies, data=payload
                )
            else:
                raise ValueError("Invalid method type")
            text = req.text
    if req.status_code >= 400:
        return
    soup = BeautifulSoup(text, "html.parser")
    return soup

def main():
    ex = ExampleScraper(URL, headers)
    df = ex.scrape_data()
    print(df)
    engine = create_engine(
        postgres_prod_str
    )
    with engine.begin() as connection:
        df.to_sql("bg_tm_urls", con=connection, if_exists="replace")


    dedupe_table(engine, "bg_tm_urls", "public", ["tm_player_id"])


if __name__ == "__main__":
    main()