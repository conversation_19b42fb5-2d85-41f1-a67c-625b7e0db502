import logging
from datetime import datetime
from functools import partial
from multiprocessing import Pool, cpu_count, freeze_support

import numpy as np
import pandas as pd
from unidecode import unidecode
from fuzzywuzzy import fuzz, process
from sqlalchemy import (
    create_engine,
    Table,
    MetaData,
    and_,
    select,
    join,
    alias,
    column,
    types,
)

from src import helper_funcs
from src.data_collection.scraping.cleaning import clean_player_profiles
from src.data_collection.scraping.cleaning.tm_to_ws_country_map import (
    map_countries,
)
from settings import postgres_prod_str, postgres_dev_str


def load_ws_profiles(cnx):
    q = """ SELECT  "playerId", "firstName", "lastName", height, "birthDate", "passportArea_name"
            FROM all_players """
    player_data = pd.read_sql(q, cnx, parse_dates=["birthDate"])
    player_data = player_data.drop_duplicates(subset=["playerId"], keep="first")

    return player_data


def prep_ws_for_matching(ws):
    # drop wyscout dupes
    ws = ws.drop_duplicates(subset=["playerId"])
    ws["full_name"] = ws["firstName"] + " " + ws["lastName"]

    # ws['divisionLevel'] = ws.groupby('playerId')['divisionLevel'].\
    #                          transform(lambda x: '_'.join([str(y) for y in x]))
    evil_cols = ["full_name", "passportArea_name"]
    for evil_col in evil_cols:
        ws[evil_col] = ws[evil_col].apply(
            lambda x: clean_player_profiles.clean_evil(x)
        )
        ws[evil_col] = (
            ws[evil_col].apply(lambda x: unidecode(str(x))).str.lower()
        )
    return ws


def prep_tm_for_matching(tm):
    # split citizenship into list
    tm["citizenship"] = tm["citizenship"].apply(
        lambda x: str(x).split("\xa0\xa0")
    )
    tm["citizenship"] = tm["citizenship"].apply(
        lambda x: list(map(map_countries, x))
    )
    # probably wont be need as league isnt used as a filter currently
    tm["league_tier"] = tm["league_tier"].apply(
        lambda x: str(x).replace(".0", "")
    )

    for str_col in ["name", "full_name", "name_in_home_country", "citizenship"]:
        tm[str_col] = tm[str_col].apply(lambda x: unidecode(str(x))).str.lower()
        tm[str_col] = tm[str_col].replace("nan", np.nan)
    # drop dudes that we have no useful info for
    tm = tm.dropna(
        how="all", subset=["contract_expiry", "agent", "current_value"]
    )
    # shuffling so that parallel batches are more homogenous for more equal concurrent runs
    tm = tm.sample(frac=1).reset_index(drop=True)

    return tm


def match_datasets(tm, ws, config, tm_ids, threshold=87):
    logging.basicConfig(
        level=logging.DEBUG,
        filename=f"{config['tm_ws_match_logs']}/tm_match_logs_{helper_funcs.get_time_stamp()}.csv",
        filemode="a+",
        format=" %(asctime)s,%(message)s",
    )
    tm_sub = tm[tm.player_id.isin(tm_ids)]
    start = datetime.now()
    # unique_name_exact_matches, exact_match_w_stats, fuzzy_name_exact_stats = [0]*3
    merge_df = pd.DataFrame(
        columns=list(tm.columns) + ["playerId", "matching_type"]
    )  # initiate df to be filled

    def match_name(name_type, name_value):
        # exit loop if value is float which means nan
        if isinstance(name_value, str):
            if len(ws[ws["full_name"] == name_value]) > 0:

                # if we have unique, exact, match, then we assume we are good to go

                if (
                    len(ws[ws["full_name"] == name_value]) == 1
                    and len(tm[tm[name_type] == name_value]) == 1
                ):
                    merge_df.loc[i] = row
                    merge_df.at[i, "matching_type"] = 0
                    merge_df.at[i, "playerId"] = ws[
                        ws["full_name"] == name_value
                    ]["playerId"].squeeze()
                    # unique_name_exact_matches += 1
                    # print('exact unique',  name_type)
                    logging.info(
                        f"|{tm_id}|{name_type}|{name_value}|exact_unique|||||"
                    )
                    return True
                else:
                    exact_match_subset = ws[
                        (ws["full_name"] == name_value)
                        & (
                            ws["birthDate"] == birth_date
                        )  # (ws['height'] == height) &
                        & (ws["passportArea_name"].isin(citizenship))
                    ]
                    if len(exact_match_subset) == 1:
                        merge_df.loc[i] = row
                        merge_df.at[i, "matching_type"] = 1
                        merge_df.at[i, "playerId"] = exact_match_subset[
                            "playerId"
                        ].squeeze()
                        # exact_match_w_stats += 1
                        logging.info(
                            f"|{tm_id}|{name_type}|{name_value}|exact_deduped|||||"
                        )
                        # print('exact dedupe ',  name_type)
                        return True

    def fuzzy_match(name_value):
        # exit loop if value is float which means nan
        if isinstance(name_value, str):
            fuzzy_match_subset = ws[  # (ws['height'] == height) &
                (ws["birthDate"] == birth_date)
                & (ws["passportArea_name"].isin(citizenship))
            ]
            if len(fuzzy_match_subset) > 0:
                fuzzy_match = process.extractOne(
                    name_value,
                    fuzzy_match_subset["full_name"],
                    scorer=fuzz.token_set_ratio,
                )
                if fuzzy_match[1] > threshold:
                    # if verbose:
                    #     print(fuzzy_match)
                    merge_df.loc[i] = row
                    merge_df.at[i, "matching_type"] = 2
                    merge_df.at[i, "playerId"] = fuzzy_match_subset[
                        fuzzy_match_subset["full_name"] == fuzzy_match[0]
                    ]["playerId"].squeeze()
                    logging.info(
                        f"|{tm_id}|{name_type}|{name_value}|successfull_fuzzy|"
                        f"{fuzzy_match[0]}|{fuzzy_match[1]}|"
                    )
                    # fuzzy_name_exact_stats += 1
                    # print('fuzzy ',  name_type)
                    return True
                else:
                    logging.info(
                        f"|{tm_id}|{name_type}|{name_value}|unsuccessuful-_fuzzy|"
                        f"{fuzzy_match[0]}|{fuzzy_match[1]}|{len(fuzzy_match_subset)}||"
                    )
            else:
                logging.info(
                    f"|{tm_id}|{name_type}|{name_value}|no_matching_stats||||{birth_date}|{citizenship}"
                )

    for i, row in tm_sub.iterrows():
        name = row["name"]
        full_name = row["full_name"]
        name_home = row["name_in_home_country"]
        birth_date = row["birth_date"]
        citizenship = eval(row["citizenship"])
        tm_id = row["player_id"]
        names = {
            "full_name": full_name,
            "name_in_home_country": name_home,
            "name": name,
        }
        try:
            for name_type in names:
                m = match_name(name_type, names[name_type])
                # break out of first loop if we match
                if m is not None:
                    break
            # dont go to fuzzy match if we already matched previously
            if m is None:
                # then we go to option b: match all stats and get fuzzy match on the name
                for name_type in names:
                    fm = fuzzy_match(names[name_type])
                    if fm is not None:
                        break
        except Exception as e:
            logging.info(f"something went wrong with {name}:\n{str(e)}")
        if i % 100 == 0:
            print(i)

    elapsed = (datetime.now() - start).seconds
    print(
        f"Matched {len(merge_df)} players out of {len(tm_sub)}  in"
        f" {elapsed} seconds."
    )
    merge_df.to_csv(
        f'{config["processed_batch_matched"]}/merged_batch_{helper_funcs.get_time_stamp(ms=True)}.csv',
        encoding="utf-16",
        index=False,
    )
    # return merge_df


def clean_for_db(df):
    # just drop dupes for now
    df = df[["playerId", "player_id", "player_url"]]
    df = df.rename(
        columns={"player_id": "tm_player_id", "player_url": "tm_player_url"}
    )
    df = df.drop_duplicates(["playerId"], keep=False).drop_duplicates(
        ["tm_player_id"], keep=False
    )
    fuped_indeces = []
    for x in df.playerId:
        try:
            _ = float(x.strip())
        except:
            fuped_indeces.append(
                pd.Series(df[df.playerId == x].index).squeeze()
            )
    df = df[~df.index.isin(fuped_indeces)]
    df["playerId"] = df["playerId"].astype(float).astype(int)
    return df


def concat_w_existing(df, already_matched):
    final_df = pd.concat([df, already_matched], sort=True, axis=0)
    final_df = final_df.drop_duplicates(
        ["playerId"], keep=False
    ).drop_duplicates(["tm_player_id"], keep=False)
    return final_df


def output_to_db(df, cnx, config):
    dtypes = {
        "playerId": types.BigInteger(),
        "tm_player_id": types.BigInteger(),
        "tm_player_url": types.String(),
    }
    # we are replacing because we are concating and deduping within
    df.to_sql(
        config["tm_to_ws_ids"],
        cnx,
        index=False,
        if_exists="replace",
        dtype=dtypes,
        method="multi",
    )


def main():
    start_all = datetime.now()
    config_db = helper_funcs.read_config("config.yml")["db_tables"]
    config = helper_funcs.read_config("config.yml")["scraping"]["directories"]
    # logging.info('|name_type|name|result|fuzzy_match|fuzzy_score|len_fuzzy_subset|birthdate|citizenship')
    cnx_dev, cnx_prod = create_engine(postgres_dev_str), create_engine(
        postgres_prod_str
    )
    # load already matched so we know which one not to touch
    already_matched = pd.read_sql(
        f"SELECT * FROM {config_db['tm_to_ws_ids']}", cnx_prod
    )
    tm = pd.read_csv(
        config["cleaned_player_profiles"],
        parse_dates=[
            "birth_date",
            "joined",
            "contract_expiry",
            "highest_value_date",
        ],
        encoding="utf-16",
        index_col=0,
    )
    ws = load_ws_profiles(cnx_prod)
    tm = prep_tm_for_matching(tm)
    ws = prep_ws_for_matching(ws)
    cpus = cpu_count()
    tm_ids = np.array_split(tm.player_id, cpus)
    freeze_support()
    pool = Pool(cpus)
    func = partial(match_datasets, tm, ws, config)
    results = pool.map(func, tm_ids)
    pool.close()
    df_concat = helper_funcs.concat_batch_files(
        config["processed_batch_matched"], encoding="utf-16"
    )

    # sort column names first
    df_concat = clean_for_db(df_concat)
    #  then keep only those who we havent already matched
    df_concat = df_concat[
        ~df_concat["playerId"].isin(already_matched["playerId"])
    ]
    # keep output just in case?
    df_concat.to_csv(
        f'{config["processed_matched"]}/tm_profiles_matched_{helper_funcs.get_time_stamp()}.csv',
        encoding="utf-16",
    )
    # output to dev, then separate bash script will migrate to prod
    final_df = concat_w_existing(df_concat, already_matched)
    output_to_db(final_df, cnx_dev, config_db)
    print("Total run time:", datetime.now() - start_all)


if __name__ == "__main__":
    main()
