import unittest
from src.data_collection.new_scraping.mixins import (
    TransferMartkMoneyPrepMixin,
    TransferMartkDateMixin,
)
import pandas as pd


class TestMoneyMixin(unittest.TestCase):
    money_df = pd.DataFrame(
        [["€1.00m", "€6.00m"], ["€700Th.", "€50Th."]],
        columns=["current_value", "highest_value"],
    )
    prep = TransferMartkMoneyPrepMixin()

    def test_money_in_millions(self):
        self.assertEqual(
            self.prep.clean_money(self.money_df)["current_value"][0],
            1000000.000,
        )
        self.assertEqual(
            self.prep.clean_money(self.money_df)["highest_value"][0],
            6000000.000,
        )

    def test_money_in_thousands(self):
        self.assertEqual(
            self.prep.clean_money(self.money_df)["current_value"][1], 700000.000
        )
        self.assertEqual(
            self.prep.clean_money(self.money_df)["highest_value"][1], 50000.000
        )


class TestDateMixin(unittest.TestCase):
    dates_df = pd.DataFrame(
        [["Jan 16, 1989", "Jun 30, 2022"], ["Feb 26, 1985", "Nov 27, 2021"]],
        columns=["birth_date", "contract_expiry"],
    )
    prep = TransferMartkDateMixin()

    def test_dates_first_player(self):
        self.assertEqual(
            self.prep.clean_dates(self.dates_df)["birth_date"][0], "1989-01-16"
        )
        self.assertEqual(
            self.prep.clean_dates(self.dates_df)["contract_expiry"][0],
            "2022-06-30",
        )

    def test_dates_second_player(self):
        self.assertEqual(
            self.prep.clean_dates(self.dates_df)["birth_date"][1], "1985-02-26"
        )
        self.assertEqual(
            self.prep.clean_dates(self.dates_df)["contract_expiry"][1],
            "2021-11-27",
        )


test = TestMoneyMixin
test.test_money_in_millions(test)
