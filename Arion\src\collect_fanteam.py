from typing import List
import requests
import json
import math
from time import sleep
from random import uniform
from datetime import datetime

from sqlalchemy import create_engine
import pandas as pd
import numpy as np

from src.helper_funcs import fast_read_sql, fast_write_sql
from settings import postgres_prod_str


class Pillager:
    def __init__(self, max_pages: int = 15):
        self.headers = None
        self.standings = []
        self.lineups = []
        self.vip_standings = []
        self.vip_lineups = []
        self.max_pages = max_pages
        self.cnx = create_engine(postgres_prod_str)
        self.tournaments = [
            # 450471,
            # 454448,
            # 456576,
            # 461779,
            # 465407,
            # 468727,
            # 472343,
            # 478796
        ]

    @staticmethod
    def flatten_dict_cols(
        df: pd.DataFrame, keep_parent_col_name: bool, subset: List[str] = None
    ) -> pd.DataFrame:
        """Takes a pandas df and flattens all nested dict columns

        Args:
            df (pd.DataFrame):
            keep_parent_col_name (bool): if true then resulting columns will be parent-column_subcolumns,
            otherwise then column will be directly equal to the subcolumn name
            subset (list, optional): if a list is passed this is parformed only on a subset of columns. Defaults to None.


        Returns:
            pd.DataFrame: flattened df
        """
        cols = df.columns if subset is None else subset

        df_list = []
        cols_to_drop = []
        for col in cols:
            dropped_nans = df[col].dropna()
            if len(dropped_nans) > 0:
                first_non_null = dropped_nans.values[
                    0
                ]  # if df[col][0] is None else df[col][0]
                if isinstance(first_non_null, dict):
                    keys = first_non_null.keys()
                    filled = df[col].where(
                        df[col].notnull(), lambda x: {k: np.nan for k in keys}
                    )
                    flat_col_df = pd.DataFrame(filled.tolist())
                    if keep_parent_col_name:
                        flat_col_df.columns = [
                            f"{col}_{subcol}" for subcol in flat_col_df.columns
                        ]
                    df_list.append(flat_col_df)
                    cols_to_drop.append(col)
        df = df.drop(columns=cols_to_drop)
        df_list.append(df)
        df = pd.concat(df_list, axis=1)
        return df

    def get_request(self, url: str):
        if self.headers is None:
            raise ValueError("Jwt token must be obtained first")
        init_resp = requests.request("GET", url, headers=self.headers)
        if init_resp.status_code == 401:
            print(f"token expired {datetime.now()}")
            headers = self.login()
            init_resp = requests.request("GET", url, headers=headers)
            print(f"token reset {datetime.now()}")
        return init_resp

    def login(self):

        url = "https://fanteam-scott.api.scoutgg.net/api/users/login?"

        payload = json.dumps(
            {
                "password": "iw4bsT6gM!.B9!h",
                "username": "ShadowEleven",
                "device_fp": [
                    {
                        "key": "userAgent",
                        "value": (
                            "Mozilla/5.0 (Linux; Android 6.0; Nexus 5"
                            " Build/MRA58N) AppleWebKit/537.36 (KHTML, like"
                            " Gecko) Chrome/94.0.4606.71 Mobile Safari/537.36"
                        ),
                    },
                    {"key": "webdriver", "value": False},
                    {"key": "language", "value": "en-US"},
                    {"key": "colorDepth", "value": 24},
                    {"key": "deviceMemory", "value": 8},
                    {"key": "hardwareConcurrency", "value": 8},
                    {"key": "screenResolution", "value": [810, 677]},
                    {"key": "availableScreenResolution", "value": [810, 677]},
                    {"key": "timezoneOffset", "value": -180},
                    {"key": "timezone", "value": "Europe/Kiev"},
                    {"key": "sessionStorage", "value": True},
                    {"key": "localStorage", "value": True},
                    {"key": "indexedDb", "value": True},
                    {"key": "addBehavior", "value": False},
                    {"key": "openDatabase", "value": True},
                    {"key": "cpuClass", "value": "not available"},
                    {"key": "platform", "value": "Win32"},
                    {"key": "plugins", "value": []},
                    {
                        "key": "canvas",
                        "value": [
                            "canvas winding:yes",
                            "canvas"
                            " fp:data:image/png;base64,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",
                        ],
                    },
                    {
                        "key": "webgl",
                        "value": [
                            "data:image/png;base64,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",
                            "extensions:ANGLE_instanced_arrays;EXT_blend_minmax;EXT_color_buffer_half_float;EXT_disjoint_timer_query;EXT_float_blend;EXT_frag_depth;EXT_shader_texture_lod;EXT_texture_compression_bptc;EXT_texture_compression_rgtc;EXT_texture_filter_anisotropic;WEBKIT_EXT_texture_filter_anisotropic;EXT_sRGB;KHR_parallel_shader_compile;OES_element_index_uint;OES_fbo_render_mipmap;OES_standard_derivatives;OES_texture_float;OES_texture_float_linear;OES_texture_half_float;OES_texture_half_float_linear;OES_vertex_array_object;WEBGL_color_buffer_float;WEBGL_compressed_texture_s3tc;WEBKIT_WEBGL_compressed_texture_s3tc;WEBGL_compressed_texture_s3tc_srgb;WEBGL_debug_renderer_info;WEBGL_debug_shaders;WEBGL_depth_texture;WEBKIT_WEBGL_depth_texture;WEBGL_draw_buffers;WEBGL_lose_context;WEBKIT_WEBGL_lose_context;WEBGL_multi_draw",
                            "webgl aliased line width range:[1, 1]",
                            "webgl aliased point size range:[1, 1024]",
                            "webgl alpha bits:8",
                            "webgl antialiasing:yes",
                            "webgl blue bits:8",
                            "webgl depth bits:24",
                            "webgl green bits:8",
                            "webgl max anisotropy:16",
                            "webgl max combined texture image units:32",
                            "webgl max cube map texture size:16384",
                            "webgl max fragment uniform vectors:1024",
                            "webgl max render buffer size:16384",
                            "webgl max texture image units:16",
                            "webgl max texture size:16384",
                            "webgl max varying vectors:30",
                            "webgl max vertex attribs:16",
                            "webgl max vertex texture image units:16",
                            "webgl max vertex uniform vectors:4096",
                            "webgl max viewport dims:[32767, 32767]",
                            "webgl red bits:8",
                            "webgl renderer:WebKit WebGL",
                            "webgl shading language version:WebGL GLSL ES 1.0"
                            " (OpenGL ES GLSL ES 1.0 Chromium)",
                            "webgl stencil bits:0",
                            "webgl vendor:WebKit",
                            "webgl version:WebGL 1.0 (OpenGL ES 2.0 Chromium)",
                            "webgl unmasked vendor:Google Inc. (Intel)",
                            "webgl unmasked renderer:ANGLE (Intel, Intel(R) UHD"
                            " Graphics 620 Direct3D11 vs_5_0 ps_5_0,"
                            " D3D11-27.20.100.8681)",
                            "webgl vertex shader high float precision:23",
                            "webgl vertex shader high float precision"
                            " rangeMin:127",
                            "webgl vertex shader high float precision"
                            " rangeMax:127",
                            "webgl vertex shader medium float precision:23",
                            "webgl vertex shader medium float precision"
                            " rangeMin:127",
                            "webgl vertex shader medium float precision"
                            " rangeMax:127",
                            "webgl vertex shader low float precision:23",
                            "webgl vertex shader low float precision"
                            " rangeMin:127",
                            "webgl vertex shader low float precision"
                            " rangeMax:127",
                            "webgl fragment shader high float precision:23",
                            "webgl fragment shader high float precision"
                            " rangeMin:127",
                            "webgl fragment shader high float precision"
                            " rangeMax:127",
                            "webgl fragment shader medium float precision:23",
                            "webgl fragment shader medium float precision"
                            " rangeMin:127",
                            "webgl fragment shader medium float precision"
                            " rangeMax:127",
                            "webgl fragment shader low float precision:23",
                            "webgl fragment shader low float precision"
                            " rangeMin:127",
                            "webgl fragment shader low float precision"
                            " rangeMax:127",
                            "webgl vertex shader high int precision:0",
                            "webgl vertex shader high int precision"
                            " rangeMin:31",
                            "webgl vertex shader high int precision"
                            " rangeMax:30",
                            "webgl vertex shader medium int precision:0",
                            "webgl vertex shader medium int precision"
                            " rangeMin:31",
                            "webgl vertex shader medium int precision"
                            " rangeMax:30",
                            "webgl vertex shader low int precision:0",
                            "webgl vertex shader low int precision rangeMin:31",
                            "webgl vertex shader low int precision rangeMax:30",
                            "webgl fragment shader high int precision:0",
                            "webgl fragment shader high int precision"
                            " rangeMin:31",
                            "webgl fragment shader high int precision"
                            " rangeMax:30",
                            "webgl fragment shader medium int precision:0",
                            "webgl fragment shader medium int precision"
                            " rangeMin:31",
                            "webgl fragment shader medium int precision"
                            " rangeMax:30",
                            "webgl fragment shader low int precision:0",
                            "webgl fragment shader low int precision"
                            " rangeMin:31",
                            "webgl fragment shader low int precision"
                            " rangeMax:30",
                        ],
                    },
                    {
                        "key": "webglVendorAndRenderer",
                        "value": (
                            "Google Inc. (Intel)~ANGLE (Intel, Intel(R) UHD"
                            " Graphics 620 Direct3D11 vs_5_0 ps_5_0,"
                            " D3D11-27.20.100.8681)"
                        ),
                    },
                    {"key": "hasLiedLanguages", "value": False},
                    {"key": "hasLiedResolution", "value": False},
                    {"key": "hasLiedOs", "value": True},
                    {"key": "hasLiedBrowser", "value": False},
                    {"key": "touchSupport", "value": [1, True, True]},
                    {
                        "key": "fonts",
                        "value": [
                            "Arial",
                            "Arial Black",
                            "Arial Narrow",
                            "Book Antiqua",
                            "Bookman Old Style",
                            "Calibri",
                            "Cambria",
                            "Cambria Math",
                            "Century",
                            "Century Gothic",
                            "Century Schoolbook",
                            "Comic Sans MS",
                            "Consolas",
                            "Courier",
                            "Courier New",
                            "Georgia",
                            "Helvetica",
                            "Impact",
                            "Lucida Bright",
                            "Lucida Calligraphy",
                            "Lucida Console",
                            "Lucida Fax",
                            "Lucida Handwriting",
                            "Lucida Sans",
                            "Lucida Sans Typewriter",
                            "Lucida Sans Unicode",
                            "Microsoft Sans Serif",
                            "Monotype Corsiva",
                            "MS Gothic",
                            "MS PGothic",
                            "MS Reference Sans Serif",
                            "MS Sans Serif",
                            "MS Serif",
                            "Palatino Linotype",
                            "Segoe Print",
                            "Segoe Script",
                            "Segoe UI",
                            "Segoe UI Light",
                            "Segoe UI Semibold",
                            "Segoe UI Symbol",
                            "Tahoma",
                            "Times",
                            "Times New Roman",
                            "Trebuchet MS",
                            "Verdana",
                            "Wingdings",
                            "Wingdings 2",
                            "Wingdings 3",
                        ],
                    },
                    {"key": "audio", "value": "124.04347527516074"},
                ],
            }
        )
        headers = {
            "Connection": "keep-alive",
            "sec-ch-ua": (
                '"Chromium";v="94", "Google Chrome";v="94", ";Not A'
                ' Brand";v="99"'
            ),
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": "Bearer fanteam undefined",
            "sec-ch-ua-mobile": "?1",
            "User-Agent": (
                "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N)"
                " AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71"
                " Mobile Safari/537.36"
            ),
            "sec-ch-ua-platform": '"Android"',
            "Origin": "https://www.fanteam.com",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.fanteam.com/",
            "Accept-Language": "en-US,en;q=0.9",
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        response.raise_for_status()
        print("token obtained")
        jwt = response.json()["token"]

        self.headers = {"Authorization": f"Bearer fanteam {jwt}"}

    def get_standings(self):

        base_url = "https://fanteam-game.api.scoutgg.net/tournaments/_TOURNAMENT?requestParentLeaderboard=true&round=_ROUND&page=0"
        for i, t in enumerate(self.tournaments):
            print(f"starting tournament {t}")
            temp_url = base_url.replace("_TOURNAMENT", str(t)).replace(
                "_ROUND", str(i + 1)
            )
            print(temp_url)
            init_resp = self.get_request(temp_url)
            pages = math.ceil(init_resp.json()["total"] / 20)
            for p in range(min(pages, self.max_pages)):
                page_url = temp_url.replace("page=0", f"page={p}")
                resp = self.get_request(page_url)
                sleep(uniform(0.21, 0.54))
                standings = resp.json()["fantasyTeams"]
                self.standings += standings
        standings_df = self.flatten_dict_cols(
            pd.DataFrame(self.standings), True
        )
        fast_write_sql(
            standings_df, "fanteam_standings", self.cnx, schema="ff_tables", if_exists="append"
        )
        print("done")

    def get_lineups(self, standings_table, lineups_table_name, filter_users_list=None, filter_week_list=None):
        def wrap_up():
            lineups_df = self.flatten_dict_cols(
                pd.DataFrame(self.lineups), True
            )
            try:
                cols = fast_read_sql(
                    f"select * from ff_tables.{lineups_table_name} limit 1",
                    self.cnx,
                ).columns
                temp_df = pd.DataFrame(columns=cols)
            except:
                temp_df = pd.DataFrame()
            final_df = pd.concat([temp_df, lineups_df])
            fast_write_sql(
                final_df, lineups_table_name, self.cnx, schema="ff_tables", if_exists='append'
            )

        df = fast_read_sql(
            f"select * from ff_tables.{standings_table}", self.cnx
        )
        if filter_users_list is not None:
            df = df[df["user_name"].isin(filter_users_list)]
        if filter_week_list is not None:
            df = df[df["tournamentId"].isin(filter_week_list)]
        ids = df["id"].tolist()
        print(len(ids) 'lineups for collection')
        base_url = "https://fanteam-game.api.scoutgg.net/fantasy_teams/_ID"
        for i, lid in enumerate(ids):
            temp_url = base_url.replace("_ID", str(lid))
            resp = self.get_request(temp_url)
            sleep(uniform(0.18, 0.69))
            lineup = resp.json()["playerChoices"]
            self.lineups += lineup
            # write every 100 records:
            if i % 100 == 0:
                print(f"collected {i} lineups")
                # wrap_up()
        wrap_up()
        print("done")

    def get_vip_standings(self, user_list: List[str]):
        base_url = "https://fanteam-game.api.scoutgg.net/tournaments/_TOURNAMENT?requestParentLeaderboard=true&round=_ROUND&search=_SEARCH&page=0"

        for i, t in enumerate(self.tournaments):
            print(f"starting tournament {t}")
            temp_url = base_url.replace("_TOURNAMENT", str(t)).replace(
                "_ROUND", str(i + 1)
            )
            print(temp_url)
            for user in user_list:
                user_temp_url = temp_url.replace("_SEARCH", user)
                print(user)
                for p in range(10):
                    page_url = user_temp_url.replace("page=0", f"page={p}")
                    resp = self.get_request(page_url)
                    sleep(uniform(0.21, 0.72))
                    standings = resp.json().get(
                        "fantasyTeams"
                    )  # in case the dude didnt participate in that particular week
                    if standings is None:
                        print(
                            f"user {user} didnt participate in {t} week,"
                            f" page {p}"
                        )
                        break  # once we hit page with nulls, we have run out of submssions for the dude
                    self.vip_standings += standings
        standings_df = self.flatten_dict_cols(
            pd.DataFrame(self.vip_standings), True
        )
        # try:
        #     cols = fast_read_sql(
        #         "select * from ff_tables.fanteam_vip_standings limit 1",
        #         self.cnx,
        #     ).columns
        #     temp_df = pd.DataFrame(columns=cols)
        # except:
        #     temp_df = pd.DataFrame()
        # final_df = pd.concat([temp_df, standings_df])
        # return standings_df
        fast_write_sql(
            standings_df,
            "fanteam_vip_standings",
            self.cnx,
            schema="ff_tables",
            if_exists="append",
        )
        print("done")


def main():
    vip_people = [
            "Desmund123",
            "Daaceofdiamonds",
            "Checkjosh",
            "DayofVictory",
            "TeLLer",
            "Ilyan1",
            "Gaffel",
            "Alekots1",
            "Tottic",
            "ShadowEleven",
        ]
    pile = Pillager()
    pile.login()
    # pile.get_standings()
    # print('standings done', datetime.now())
    # pile.get_vip_standings(vip_people)
    # print('vip standings done', datetime.now())
    # vips week 8:
    pile.get_lineups("fanteam_vip_standings", "fanteam_vip_lineups", vip_people, [478796])
    print('vip lineups done', datetime.now())
    # all people week 8 
    pile.get_lineups("fanteam_standings", "fanteam_lineups", None, [478796])
    print('lineups done', datetime.now())


if __name__ == "__main__":
    main()

