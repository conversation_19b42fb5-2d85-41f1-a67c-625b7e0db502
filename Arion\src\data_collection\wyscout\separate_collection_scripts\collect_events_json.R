# Query Wyscout API for new event structure
source('src/data_collection/wyscout/00_libs.R')
collected_events = dbGetQuery(con, 'select distinct "matchId" from events_object_db')
adv_stats_matches = dbGetQuery(con, 'select distinct "matchId" from advanced_stats_total')
competition_matches = adv_stats_matches %>%
  filter(!matchId %in% collected_events$matchId)

events_jsons = list()
for(i in 1:length(competition_matches$matchId)){
  mid = competition_matches$matchId[i]
  # Get new output from the beta events endpoint
  Sys.sleep(0.8)
  resp <- GET(
    paste0(
      'https://apirest.wyscout.com/v2/matches/',
      mid,
      '/events/beta'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  
  new_out <- content(resp, as = 'text', encoding = 'UTF-8')
  new_out_df <- jsonlite::fromJSON(new_out, simplifyVector = F, simplifyDataFrame = F)
  
  # Get events out
  if('elements' %in% names(new_out_df)){
    if(length(new_out_df$elements) == 1){
      if('events' %in% names(new_out_df$elements[[1]])){
        evts_df <- new_out_df$elements[[1]]$events # Response has one redundant layer of nesting. TODO Ask Wyscout
        
      }else{
        evts_df <- NA
      }
      
      # Get non-event elements out
      non_event_fields = setdiff(names(new_out_df$elements[[1]]), 'events')
      if(length(non_event_fields) > 0){
        no_evts_df <- new_out_df$elements[[1]][[non_event_fields]]
        
      }else{
        no_evts_df <- NA
      }
    }
    
  }
  
  meta_df <- NA
  if('meta' %in% names(new_out_df)){
    if(length(new_out_df[['meta']]) > 0)
      meta_df <- new_out_df$meta
  }
  
  evts_json <- sapply(evts_df, function(x) as.character(jsonlite::toJSON(x, dataframe = 'rows', auto_unbox = T,
                                                                         null = 'list',
                                                                         na = 'null')))
  meta_json <- sapply(meta_df, function(x) as.character(jsonlite::toJSON(x, dataframe = 'rows', auto_unbox = T,
                                                                         null = 'list',
                                                                         na = 'null')))
  no_evts_json <- sapply(no_evts_df, function(x) as.character(jsonlite::toJSON(x, dataframe = 'rows', auto_unbox = T,
                                                                               null = 'list',
                                                                               na = 'null')))
  
  to_write_array <- data.frame(
    matchId = mid,
    events_json = evts_json,
    meta = meta_json,
    other_elements = no_evts_json,
    stringsAsFactors = F)

  events_jsons[[length(events_jsons) + 1]] = to_write_array

    if (i %% 500 == 0 | i == nrow(competition_matches)) {
    
    events_jsons = plyr::ldply(events_jsons, plyr::rbind.fill)

    RPostgreSQL::dbWriteTable(con, 
        'events_object_db', 
        events_jsons, 
        append = T, 
        row.names = F, 
        rownames = F)
    
    events_jsons = list()
    
  }

}

# events_jsons = plyr::ldply(events_jsons, plyr::rbind.fill)

# RPostgreSQL::dbWriteTable(con, 'events_object_db', events_jsons, append = T, row.names = F, rownames = F)
# # json_insert_qry = paste('INSERT INTO events_object_db("matchId", "events", "meta", "other_elements") VALUES', events_j,'ON CONFLICT DO NOTHING';)

#https://apirest.wyscout.com/v2/matches/2852748/events/beta