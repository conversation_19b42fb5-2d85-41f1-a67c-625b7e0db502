from datetime import datetime, timedelta
import schedule
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

from dag_settings import workdir_speed

dag_params = {
    "dag_id": "run_speed_pipeline2",

    "start_date": datetime(2022, 3, 1),
    "schedule_interval": timedelta(days=14),
    "params": {"workdir": workdir_speed},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    calculate_acceleration_speed = BashOperator(
        task_id="calculate_acceleration_speed",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_wrangling/derive_acceleration_speeds.py """,
    )

    # src/scoring/group_speeds.py

    get_speed_groups = BashOperator(
        task_id="get_speed_groups",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/scoring/group_speeds.py """,
    )


    # calculate_event_speed = BashOperator(
    #     task_id="calculate_event_speed",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/data_wrangling/derive_speeds_kalman.py """,
    # )

    # build_speed_model = BashOperator(
    #     task_id="build_speed_model",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/modelling/build_speed_gbm.py """,
    # )

    # score_speeds = BashOperator(
    #     task_id="score_speeds",
    #     bash_command="""
    #                     export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                     cd {{ params.workdir }}
    #                     python3 src/scoring/score_speeds.py """,
    # )

    # aggregate_speeds = PostgresOperator(
    #     task_id="aggregate_speeds",
    #     database="wyscout_raw_production",
    #     sql=open(workdir_speed + "/queries/create_agg_player_speeds.sql").read(),
    # )

    calculate_acceleration_speed >> get_speed_groups