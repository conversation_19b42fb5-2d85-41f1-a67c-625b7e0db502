import pandas as pd

from src.data_collection.wyscout.v2.SeasonsXUpdater import SeasonsXUpdater


class SeasonsMatchesUpdater(SeasonsXUpdater):
    def __init__(self, table_name, id_name, only_active=True):
        super().__init__(table_name, id_name, only_active)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/matches"
        self.object_type = self.base_url.split("/")[-1]

    @staticmethod
    def format_response(payload, code):
        return [{k: x[k] for k in ("matchId", "seasonId")} for x in payload]

    def prep(self, results):
        """For seasons_matches, we return None for base obj because
        we are collecting the matches separately, since there is more info there
        Args:
            results (dict): output from endpoint

        Returns:

        """
        return pd.DataFrame(results), None
