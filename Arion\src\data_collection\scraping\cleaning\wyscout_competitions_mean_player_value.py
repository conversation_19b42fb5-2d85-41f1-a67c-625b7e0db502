import pandas as pd
from settings import postgres_prod_str
from sqlalchemy import create_engine


def read_data():
    cnx = create_engine(postgres_prod_str)
    comp_teams_query = """ select *
                FROM competition_teams 
                WHERE "gender"='male' and "category"='default'
                """
    comps_query = comp_teams_query.replace("competition_teams", "competitions")

    # teams_init = pd.read_sql('''SELECT * FROM competition_teams;''', cnx)
    merged = pd.read_csv(
        "data/processed/matched_tm_manually_checked.csv",
        usecols=["teamId", "transfermakt_team_id"],
    )

    tm_players = pd.read_csv(
        "data/processed/scraped/players_w_team_league.csv",
        usecols=["team_id", "current_market_value"],
    )

    ys_comp_teams = pd.read_sql(comp_teams_query, cnx)
    ys_comps = pd.read_sql(comps_query, cnx)

    return merged, tm_players, ys_comps, ys_comp_teams


def merge_tables(merged, ys, tm):
    tm_merged = pd.merge(
        merged,
        tm,
        how="left",
        left_on="transfermakt_team_id",
        right_on="team_id",
    )
    all_merged = pd.merge(ys, tm_merged, how="left", on="teamId")
    return all_merged


def competition_groupby(ys_comps, all_merged):
    league_mean_player = (
        all_merged.groupby("competition")["current_market_value"]
        .mean()
        .reset_index()
    )
    league_mean_player.columns = ["competitionId", "mean_player_value"]
    ys_comps_w_mean_value = pd.merge(
        ys_comps, league_mean_player, how="left", on="competitionId"
    )
    return ys_comps_w_mean_value


def write_data(data):
    data.to_csv("data/processed/ys_comps_w_mean_value.csv")


def main():
    merged, tm_players, ys_comps, ys_comp_teams = read_data()
    all_merged = merge_tables(merged, ys_comp_teams, tm_players)
    ys_comps_w_mean_value = competition_groupby(ys_comps, all_merged)
    write_data(ys_comps_w_mean_value)


if __name__ == "__main__":
    main()
