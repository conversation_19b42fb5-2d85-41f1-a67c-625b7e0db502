import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import Bash<PERSON>perator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir_sorare, config

sys.path.append(workdir_sorare)
experiment_name = 'production'

dag_params = {
    "dag_id": "sorare_tweets_collection_dag_new_fix_timezone",
    "start_date": datetime(2022, 3, 15, 12, 1),
    "schedule_interval": "1 */1 * * *",
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_sorare,
        "experiment_name": f'"{experiment_name}"'
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    # update_sorare_progress_table = PostgresOperator(
    #     task_id="update_sorare_progress_table",
    #     database="wyscout_raw_production",
    #     sql="""INSERT INTO sorare.sorare_mvp_players_progress select * from sorare.sorare_mvp_players""",
    # )

    # update_sorare_progress_table_noretweet = PostgresOperator(
    #     task_id="update_sorare_progress_table_noretweet",
    #     database="wyscout_raw_production",
    #     sql="""INSERT INTO sorare.sorare_mvp_players_progress_noretweets select * from sorare.sorare_mvp_players """,
    # )

    discord_bot = BashOperator(
        task_id="run_discord_bot",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/discord_bot/discord_bot.py
                           """,
    )

    collect_tweets_count_batch1 = BashOperator(
        task_id="collect_tweets_count_batch1",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/data_collection/collect_tweet_counts.py 2 1 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    collect_tweets_count_batch2 = BashOperator(
        task_id="collect_tweets_count_batch2",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/data_collection/collect_tweet_counts.py 2 2 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    collect_tweets_count_noretweet_batch1 = BashOperator(
        task_id="collect_tweets_count_noretweet_batch1",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/data_collection/collect_tweet_counts.py 2 1 "src/configs/config_production_noretweet.json" {{params.experiment_name}}
                           """,
    )

    collect_tweets_count_noretweet_batch2 = BashOperator(
        task_id="collect_tweets_count_noretweet_batch2",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/data_collection/collect_tweet_counts.py 2 2 "src/configs/config_production_noretweet.json" {{params.experiment_name}}
                           """,
    )


    check_for_alert = BashOperator(
        task_id="check_for_alert",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/alert_creation/check_for_alerts.py "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )


    collect_tweets_content_batch1 = BashOperator(
        task_id="collect_tweets_content_batch1",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/data_collection/collect_tweet_content.py 1 1 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    # collect_tweets_content_batch2 = BashOperator(
    #     task_id="collect_tweets_content_batch2",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/data_collection/collect_tweet_content.py 2 2 '"./src/configs/config_production.json"'
    #                        """,
    # )


    collect_tweets_count_batch1 >> collect_tweets_count_batch2 
    
    # collect_tweets_count >> [model_tweet_count_batch_1, model_tweet_count_batch_2,model_tweet_count_batch_3, model_tweet_count_batch_4, model_tweet_count_batch_5]

    collect_tweets_count_noretweet_batch1 >> collect_tweets_count_noretweet_batch2 
    
    # collect_tweets_count_noretweet >> [model_tweet_count_batch_1_noretweet, model_tweet_count_batch_2_noretweet, model_tweet_count_batch_3_noretweet, model_tweet_count_batch_4_noretweet, model_tweet_count_batch_5_noretweet]

    [collect_tweets_count_batch2, collect_tweets_count_noretweet_batch2] >> check_for_alert >> collect_tweets_content_batch1 
    
    collect_tweets_content_batch1 >> discord_bot
    

