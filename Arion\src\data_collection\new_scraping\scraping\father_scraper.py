from tenacity.stop import stop_after_attempt
from tenacity.wait import wait_random
from tenacity import retry
import requests
from settings import PROXIES


class Scraper:
    # Initial scraper
    # Others will inherit from it
    def __init__(self):
        pass

    def loop_through_urls(self, urls=[]):
        pass

    def scrape_page(self, url: str):
        pass


@retry(
    stop=stop_after_attempt(3),
    wait=wait_random(min=30, max=60),
)
async def fetch(session, url, id=None):
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 5.1)"
            " AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/49.0.2623.112 Safari/537.36"
        )
    }
    if not PROXIES:
        raise ValueError()

    async with session.get(url, proxy="67.205.190.164:8080", headers=headers) as response:
        print(response)
        if response.status == 403:
            raise Exception(f"Access denied for {url}")
        text = await response.content.read()
        if id is None:
            return (text, url)
        else:
            return (text, url, id)


def get_response(url: str) -> str:
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 5.1)"
            " AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/49.0.2623.112 Safari/537.36"
        )
    }
    return requests.get(url, headers=headers, proxies={"https": PROXIES}).text
