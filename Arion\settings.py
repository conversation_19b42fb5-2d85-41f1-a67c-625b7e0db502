import os
import json
import dotenv

dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
dotenv.load_dotenv(dotenv_path)

DEVELOPMENT_DB = os.environ.get("DEVELOPMENT_DB")
PRODUCTION_DB = os.environ.get("PRODUCTION_DB")
DO_DB = os.environ.get("DO_DB")
USR = os.environ.get("USR")
PASS = os.environ.get("PASS")
HOST = os.environ.get("HOST")
PORT = os.environ.get("PORT")
DO_HOST = os.environ.get("DO_HOST")
WYSCOUT_USR = os.environ.get("WYSCOUT_USR")
WYSCOUT_PASS = os.environ.get("WYSCOUT_PASS")
PROXIES = os.environ.get("PROXIES")

PLAYER_ROLE_API_USR = os.environ.get("PLAYER_ROLE_API_USR")
PLAYER_ROLE_API_PASS = os.environ.get("PLAYER_ROLE_API_PASS")
GOOGLE_ENSKAI_ALERTS_PASS = os.environ.get("GOOGLE_ENSKAI_ALERTS_PASS")
OUTLOOK_ENSKAI_ALERTS_PASS = os.environ.get("OUTLOOK_ENSKAI_ALERTS_PASS")

TWITTER_API_KEY_SECRET = os.environ.get("TWITTER_API_KEY_SECRET")
TWITTER_API_KEY = os.environ.get("TWITTER_API_KEY")

TWITTER_ACCESS_TOKEN = os.environ.get("TWITTER_ACCESS_TOKEN")
TWITTER_ACCESS_TOKEN_SECRET = os.environ.get("TWITTER_ACCESS_TOKEN_SECRET")

MONGO_URL_PROD = os.environ.get("MONGO_URL_PROD")
MONGO_URL_DEV = os.environ.get("MONGO_URL_DEV")

postgres_dev_str = f"postgresql://{USR}:{PASS}@{HOST}:{PORT}/{DEVELOPMENT_DB}"
postgres_do_str = f"postgresql://{USR}:{PASS}@{DO_HOST}:{PORT}/{DO_DB}"
postgres_prod_str = f"postgresql://{USR}:{PASS}@{HOST}:{PORT}/{PRODUCTION_DB}"
postgres_research_str = f"postgresql://{USR}:{PASS}@{HOST}:{PORT}/research_db"

USERS = json.load(open("postgres_accounts.json"))["users"]
SERVICE_ACCS = json.load(open("postgres_accounts.json"))["service_accs"]

