source('src/data_collection/wyscout/00_libs.R')

try({dbDisconnect(con)})
con = make_connection()

seasons_matches = dbReadTable(con, 'seasons_matches') %>%
  distinct()
competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')

seasons_matches = seasons_matches %>%
  filter(competitionId %in% competitions$competitionId & as.Date(date) > as.Date('2015-12-31')) %>%
  distinct()

if('matches_teams' %in% dbListTables(con)){
  written_matches = dbGetQuery(con, 'select distinct "matchId" from matches_teams')
  seasons_matches = seasons_matches %>%
    anti_join(written_matches)
  owt = F
  tml = dbGetQuery(con, 'select * from team_matches_lineups limit 1')
}else{
  written_matches = NULL
  owt = T
}

seasons_matches = seasons_matches %>%
  arrange(competitionId)

matches_teams = list()
match_team_lineups = list()
i = 0
for (match_id in seasons_matches$matchId[1:nrow(seasons_matches)]){
  i = i + 1
  # print(i)
  if(!match_id%in%written_matches$matchId){
    try({
      
      Sys.sleep(0.08)
      resp = GET(
        paste0(
          'https://apirest.wyscout.com/v2/matches/',
          match_id,
          '?useSides=true&details=players&hasFormation=1'
        ),
        authenticate(wyscout_username, wyscout_pass)
      )
      cont = content(resp)
      cont = remove_null(cont)
      match_data = data.frame(cont[c(
        'wyId',
        'label',
        'date',
        'dateutc',
        'status',
        'duration',
        'winner',
        'competitionId',
        'seasonId',
        'roundId',
        'gameweek'
      )], stringsAsFactors = F)
      colnames(match_data)[1] = 'matchId'
      home_data = data.frame(cont$teamsData$home[c(
        'teamId',
        'side',
        'score',
        'scoreHT',
        'scoreET',
        'scoreP',
        'coachId',
        'hasFormation'
      )],
      stringsAsFactors = F)
      home_data['matchId'] = match_id
      
      away_data = data.frame(cont$teamsData$away[c(
        'teamId',
        'side',
        'score',
        'scoreHT',
        'scoreET',
        'scoreP',
        'coachId',
        'hasFormation'
      )],
      stringsAsFactors = F)
      away_data['matchId'] = match_id
      match_teams = rbind(home_data, away_data)
      matches_teams[[length(matches_teams) + 1]] = match_teams
      # })
      
      
      # Get Home Team Lineup
      #try({
      home_team = cont$teamsData$home
      home_team_all = NULL
      lnp = NULL
      bnc = NULL
      if (home_team$hasFormation) {
        lnp = lapply(home_team$formation$lineup, remove_null)
        lnp = lapply(lnp, function(x) lapply(x,remove_null))
        if(length(lnp)>0){
          lnp = lapply(lnp, fix_lineup_passport_data, rel_cname = 'passportArea')
          lnp = lapply(lnp, fix_lineup_passport_data, rel_cname = 'birthArea')
        }
        home_team_lineup = plyr::ldply(lnp,
                                       data.frame,
                                       stringsAsFactors = F)
        
        if(nrow(home_team_lineup)>0){
          home_team_lineup['starting_lineup'] = 1
          home_team_lineup['teamId'] = home_team$teamId
        }else{
          home_team_lineup = NULL
        }
        
        
        bnc = lapply(home_team$formation$bench, remove_null)
        bnc = lapply(bnc, function(x) lapply(x,remove_null))
        if(length(bnc)>0){
          bnc = lapply(bnc, fix_lineup_passport_data, rel_cname = 'passportArea')
          bnc = lapply(bnc, fix_lineup_passport_data, rel_cname = 'birthArea')
        }
        
        home_team_bench = plyr::ldply(bnc, data.frame, stringsAsFactors = F)
        
        if(nrow(home_team_bench)>0){
          home_team_bench['starting_lineup'] = 0
          home_team_bench['teamId'] = home_team$teamId
        }else{
          home_team_bench = NULL
        }
        
        
        sbs = lapply(home_team$formation$substitutions, remove_null)
        sbs = lapply(sbs, function(x) lapply(x,remove_null))
        home_team_subs = plyr::ldply(sbs,
                                     data.frame,
                                     stringsAsFactors = F)
        
        if(!is.null(home_team_lineup)){
          # home_team_bench = NULL
          # home_team_lineup['substitute'] = NA
          # sub_out = match(home_team_subs[['playerOut']], home_team_lineup[['playerId']])
          # sub_out = sub_out[!is.na(sub_out)]
          # home_team_lineup[['substitute']][sub_out] = home_team_subs$playerIn
          #
          # if(!is.null(home_team_bench)){
          #   home_team_bench['substitute'] = NA
          #   sub_in = match(home_team_subs[['playerIn']], home_team_bench[['playerId']])
          #   sub_in = sub_in[!is.na(sub_in)]
          #   home_team_bench[['substitute']][sub_in] = home_team_subs$playerOut
          # }
          #
          
          
          home_team_all = rbind(home_team_lineup, home_team_bench)
          home_team_all['matchId'] = match_id
          home_team_all['side'] = 'home'
        }
      }
      
      # Get Away Team Lineup
      away_team = cont$teamsData$away
      away_team_all = NULL
      lnp2 = NULL
      bnc2 = NULL
      if (away_team$hasFormation) {
        lnp2 = lapply(away_team$formation$lineup, remove_null)
        lnp2 = lapply(lnp2, function(x) lapply(x,remove_null))
        if(length(lnp2)>0){
          lnp2 = lapply(lnp2, fix_lineup_passport_data, rel_cname = 'passportArea')
          lnp2 = lapply(lnp2, fix_lineup_passport_data, rel_cname = 'birthArea')
        }
        away_team_lineup = plyr::ldply(lnp2,
                                       data.frame,
                                       stringsAsFactors = F)
        
        if(nrow(away_team_lineup)>0){
          away_team_lineup['starting_lineup'] = 1
          away_team_lineup['teamId'] = away_team$teamId
        }else{
          away_team_lineup = NULL
        }
        
        
        bnc2 = lapply(away_team$formation$bench, remove_null)
        bnc2 = lapply(bnc, function(x) lapply(x,remove_null))
        if(length(bnc)>0){
          bnc2 = lapply(bnc2, fix_lineup_passport_data, rel_cname = 'passportArea')
          bnc2 = lapply(bnc2, fix_lineup_passport_data, rel_cname = 'birthArea')
        }
        
        away_team_bench = plyr::ldply(bnc2, data.frame, stringsAsFactors = F)
        if(nrow(away_team_bench)>0){
          away_team_bench['starting_lineup'] = 0
          away_team_bench['teamId'] = away_team$teamId
        }else{
          away_team_bench = NULL
        }
        
        
        sbs2 = lapply(away_team$formation$substitutions, remove_null)
        sbs2 = lapply(sbs2, function(x) lapply(x,remove_null))
        away_team_subs = plyr::ldply(sbs2,
                                     data.frame,
                                     stringsAsFactors = F)
        if(!is.null(away_team_lineup)){
          # away_team_bench = NULL
          # away_team_lineup['substitute'] = NA
          # sub_out = match(away_team_subs[['playerOut']], away_team_lineup[['playerId']])
          # sub_out = sub_out[!is.na(sub_out)]
          # away_team_lineup[['substitute']][sub_out] = away_team_subs$playerIn
          #
          # if(!is.null(away_team_bench)){
          #   away_team_bench['substitute'] = NA
          #   sub_in = match(away_team_subs[['playerIn']], away_team_bench[['playerId']])
          #   sub_in = sub_in[!is.na(sub_in)]
          #   away_team_bench[['substitute']][sub_in] = away_team_subs$playerOut
          # }
          
          away_team_all = rbind(away_team_lineup, away_team_bench)
          away_team_all['matchId'] = match_id
          away_team_all['side'] = 'away'
        }
        
      }
      match_lineups = rbind(home_team_all, away_team_all)
      match_team_lineups[[length(match_team_lineups) + 1]] = match_lineups
    })
    
    if(i%%1000 == 0 | i >= nrow(seasons_matches)){
      match_team_lineups = do.call(plyr::rbind.fill, match_team_lineups)
      match_team_lineups[setdiff(colnames(tml), colnames(match_team_lineups))] = NA
      match_team_lineups = match_team_lineups[colnames(tml)]
      matches_teams = do.call(plyr::rbind.fill, matches_teams)
      
      #break
      try({
        colnames(match_team_lineups) = gsub('match_id', 'matchId', colnames(match_team_lineups))
        colnames(match_team_lineups) = gsub('player.', '', colnames(match_team_lineups), fixed = T)
      })
      
      try({
        colnames(matches_teams) = gsub('match_id', 'matchId', colnames(matches_teams))
        colnames(matches_teams) = gsub('.', '_', colnames(matches_teams), fixed = T)
      })
      
      
      try({
        dbDisconnect(con)
      })
      
      try({
        con = make_connection()
      })
      
      try({
        RPostgreSQL::dbWriteTable(
          con,
          'matches_teams',
          matches_teams,
          overwrite = owt,
          append = !owt,
          row.names = F,
          rownames = F
        )
      })
      
      try({
        RPostgreSQL::dbWriteTable(
          con,
          'team_matches_lineups',
          match_team_lineups,
          overwrite = owt,
          append = !owt,
          row.names = F,
          rownames = F
        )
      })
      
      
      owt = F
      
      matches_teams = list()
      match_team_lineups = list()
    }
  }
}


con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'matches_teams',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'team_matches_lineups',tbl_qry))
}

dbDisconnect(con)