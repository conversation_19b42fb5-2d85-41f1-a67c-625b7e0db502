from sqlalchemy import create_engine
import pandas as pd
from settings import postgres_prod_str
from src.helper_funcs import fast_write_sql


class TeamNameMatcher:
    def __init__(self, match_threshold, substring_threshold, cnx):
        self.cnx = cnx
        self.match_threshold = match_threshold
        self.substring_threshold = substring_threshold

    def primary_matching(self):
        self.merged_df = self.match_teams_from_query(
            "src/queries/merge_tm_ws_latest_teams.sql", "replace"
        )

    def insert_manual_matching(self):
        sql_file = open("src/queries/manual_team_matching_insert.sql")
        self.cnx.execute(sql_file.read())

    def secondary_matching(self):
        self.merged_df2 = self.match_teams_from_query(
            "src/queries/merge_tm_ws_teams_secondary_sweep.sql", "append"
        )

    def match_teams_from_query(self, query_path, if_exists):
        sql_file = open(query_path)
        result = pd.read_sql(sql_file.read(), self.cnx)
        fast_write_sql(
            result,
            "tm_to_ws_team_ids",
            self.cnx,
            if_exists=if_exists,
            schema="transfermarkt",
        )


def main():
    cnx = create_engine(postgres_prod_str)
    TNM = TeamNameMatcher(90, 3, cnx)
    TNM.primary_matching()
    TNM.insert_manual_matching()
    TNM.secondary_matching()


if __name__ == "__main__":
    main()
