import requests

import pandas as pd
import numpy as np
from sqlalchemy import create_engine

from settings import postgres_prod_str
from src.helper_funcs import fast_write_sql

def flatten_dict_cols(
    df: pd.DataFrame, keep_parent_col_name: bool, subset: List[str] = None
) -> pd.DataFrame:
    """Takes a pandas df and flattens all nested dict columns

    Args:
        df (pd.DataFrame):
        keep_parent_col_name (bool): if true then resulting columns will be parent-column_subcolumns,
        otherwise then column will be directly equal to the subcolumn name
        subset (list, optional): if a list is passed this is parformed only on a subset of columns. Defaults to None.


    Returns:
        pd.DataFrame: flattened df
    """
    cols = df.columns if subset is None else subset

    df_list = []
    cols_to_drop = []
    for col in cols:
        dropped_nans = df[col].dropna()
        if len(dropped_nans) > 0:
            first_non_null = dropped_nans.values[
                0
            ]  # if df[col][0] is None else df[col][0]
            if isinstance(first_non_null, dict):
                keys = first_non_null.keys()
                filled = df[col].where(
                    df[col].notnull(), lambda x: {k: np.nan for k in keys}
                )
                flat_col_df = pd.DataFrame(filled.tolist())
                if keep_parent_col_name:
                    flat_col_df.columns = [
                        f"{col}_{subcol}" for subcol in flat_col_df.columns
                    ]
                df_list.append(flat_col_df)
                cols_to_drop.append(col)
    df = df.drop(columns=cols_to_drop)
    df_list.append(df)
    df = pd.concat(df_list, axis=1)
    return df


def main():
    base_url = "https://compstats.uefa.com/v1/player-ranking?optionalFields=PLAYER%2CTEAM&competitionId=1&seasonYear=current&stats=minutes_played_official%2Cmatches_appearance%2Ctop_speed%2Cdistance_covered&order=DESC&phase=TOURNAMENT&limit=500&offset=_OFFSET"
    player_list = []
    for offset in (0, 500):
        url = base_url.replace("_OFFSET", str(offset))
        response = requests.request(
            "GET",
            url,
        )
        player_list.extend(response.json())

    df = pd.DataFrame(player_list)
    df["statistics"] = df["statistics"].apply(
        lambda x: {y["name"]: float(y["value"]) for y in x}
    )
    df = flatten_dict_cols(df, True)
    df = df[[x for x in df.columns if "translations" not in x]]
    for col in df.columns:
        if any(col.lower().endswith(x) for x in ["id", "height", "age"]):
            df[col] = df[col].astype(float).astype('Int64')
    cnx = create_engine(postgres_prod_str)

    fast_write_sql(df, 'uefa_speed_measurements', cnx, if_exists='replace', index=False)