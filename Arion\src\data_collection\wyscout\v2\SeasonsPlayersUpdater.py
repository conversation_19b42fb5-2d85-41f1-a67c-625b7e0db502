import traceback
from typing import List, <PERSON>, Tu<PERSON>, Dict, Optional
import json

import aiohttp
from sqlalchemy import types
import asyncio
import pandas as pd
import numpy as np
from tenacity import retry, wait_fixed, stop_after_attempt

from src.data_collection.wyscout.v2.SeasonsXUpdater import SeasonsXUpdater
from src.helper_funcs import cloud_log_struct


class SeasonsPlayersUpdater(SeasonsXUpdater):
    def __init__(self, table_name, id_name, only_active=True):
        super().__init__(table_name, id_name, only_active)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/players"
        self.object_type = self.base_url.split("/")[-1]
        self.dtype = {
            "playerId": types.Integer,
            "shortName": types.Text,
            "firstName": types.Text,
            "middleName": types.Text,
            "lastName": types.Text,
            "height": types.Integer,
            "weight": types.Integer,
            "birthDate": types.Text,
            "foot": types.Text,
            "currentTeamId": types.Integer,
            "currentNationalTeamId": types.Integer,
            "gender": types.Text,
            "status": types.Text,
            "birthArea_id": types.Integer,
            "birthArea_alpha2code": types.Text,
            "birthArea_alpha3code": types.Text,
            "birthArea_name": types.Text,
            "passportArea_id": types.Integer,
            "passportArea_alpha2code": types.Text,
            "passportArea_alpha3code": types.Text,
            "passportArea_name": types.Text,
            "role_name": types.Text,
            "role_code2": types.Text,
            "role_code3": types.Text,
        }

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Optional[Union[Tuple[List[Dict], int], Tuple[None, int]]]:
        payload = json.loads(resp)
        if payload is None:
            return None
        return self.format_response(payload, code)

    def format_response(
        self, payload, code
    ) -> Union[Tuple[List[Dict], int], Tuple[None, int]]:
        page_count = payload["meta"]["page_count"]
        body = payload[self.object_type]
        if len(body) == 0:
            return None, 1
        for x in body:
            x.update({"seasonId": code})
        return body, page_count

    def append_all_pages_of_season(
        self,
        url: str,
        code: int,
        page_count: int,
        params: dict,
        player_pages: List[dict],
    ):
        # add the new pages for collection only if it is the first page we are visiting,
        # i.e. first time we are hitting this season:

        if page_count > 1 and params["page"] == 1:
            for page_num in range(2, page_count + 1):
                new_params = params.copy()
                new_params["page"] = page_num
                player_pages.append(
                    {"url": url, "params": new_params, "code": code}
                )

    @retry(wait=wait_fixed(10), stop=stop_after_attempt(2))
    async def process_response(
        self,
        session: aiohttp.ClientSession,
        code: int,
        player_pages: List[dict],
        params: dict = None,
    ) -> list:
        """Seasons-players endpoints is paginated so first we need to collect
        first page, then get the number of pages and create a list of all seasons and pages
        that need to be additionally fecthed

        Args:
            session (aiohttp.ClientSession):
            url (str): request url
            params (dict, optional): request params. Defaults to None.
            player_pages(int, optional):

        Returns:
            list: list with items returned from api response

        """
        url = self.base_url.replace("_ID", str(code))
        if params is None:
            params = {"limit": 100, "page": 1, "sort": "wyId"}
        try:
            payload, status = await self.fetch_response(
                session, code, url, params
            )
            if payload is None:
                body, page_count = None, 1
            else:
                body, page_count = payload
            if status in (504, 429):
                self.handle_request_timeout(url)
                # if max retries not exceeded, try again
                return await self.process_response(
                    session, code, player_pages, params
                )
        except:
            cloud_log_struct(
                self.logger,
                {
                    "updater": self.__class__.__name__,
                    "action": "failed processing request",
                    "error": traceback.format_exc(),
                    "status": status,
                    "url": url,
                },
            )
            # re-raise after logging
            raise Exception(traceback.format_exc())
        self.append_all_pages_of_season(
            url, code, page_count, params, player_pages
        )
        # if we have managed to get a request without a timeout
        # and the last request had timed out, clear the status:
        self.clear_timeout_if_happening(status)
        return body

    async def collect(self) -> Union[list, None]:
        """Collects a batch of ids from Wyscout endpoint

        Returns:
            list: A list of responses
        """
        tasks = []
        player_pages: List[dict] = []
        session = aiohttp.ClientSession()
        for code in self.collection_list:
            await asyncio.sleep(self.sleep_time)
            await self.check_timeouts()
            # loop through seasons as usual:
            tasks.append(
                asyncio.create_task(
                    self.process_response(
                        session,
                        code,
                        player_pages=player_pages,
                    )
                )
            )
        # collect results form tasks:
        results = await asyncio.gather(*tasks)
        # filter for None responses (i.e. empty/broken responses)
        results = list(filter(lambda x: x is not None, results))
        if results:
            results = list(np.concatenate(results))

        pages_tasks = []
        for page in player_pages:
            await asyncio.sleep(self.sleep_time)
            # once we have collected all pages for all seasons we create tasks and
            # execute for those. This needs to happen as a separate asyncio.gather
            # because otherwise requests are fired simultaneously and rate limit
            # is violated
            await self.check_timeouts()
            pages_tasks.append(
                asyncio.create_task(
                    self.process_response(
                        session,
                        page["code"],
                        params=page["params"],
                        player_pages=player_pages,
                    )
                )
            )

        # collect results form tasks:
        sub_results = await asyncio.gather(*pages_tasks)
        await session.close()
        # filter for None responses (i.e. empty/broken responses)
        sub_results = list(filter(lambda x: x is not None, sub_results))
        if sub_results:
            sub_results = list(np.concatenate(sub_results))
        if not results + sub_results:
            return None
        return results + sub_results

    def prep(self, results):

        df = pd.DataFrame(results)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df = df.rename(columns={"wyId": "playerId"})
        for col in ["firstName", "lastName", "shortName"]:
            df[col] = df[col].str.replace("(\\r|\\n)", "", regex=True)
        for col, col_type in self.dtype.items():
            if col_type == types.Integer:
                df[col] = df[col].replace("", np.nan).astype("Int64")
        seasons_players = df[
            ["seasonId", "playerId", "currentTeamId", "currentNationalTeamId"]
        ]
        players = df[
            [x for x in df.columns if x != "seasonId"]
        ].drop_duplicates(subset="playerId")

        return seasons_players, players
