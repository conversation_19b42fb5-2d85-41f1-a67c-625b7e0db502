from datetime import datetime
import numpy as np
import pandas as pd
from src.data_collection.wyscout.v2.Updater import Updater


class SeasonsUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = (
            "https://apirest.wyscout.com/v3/competitions/_ID/seasons"
        )
        self.object_type = self.base_url.split("/")[-1]
        self.if_exists = "replace"
        self.id_name = "seasonId"
        self.create_unknown_records_flag = (
            True  # applies to all tables that are being dropped and re-created
        )
        self.write_in_loop = (
            True  # applies to all tables that are being dropped and re-created
        )

    def prep(self, results):
        df = pd.DataFrame(results)
        df = self.flatten_dict_cols(df, keep_parent_col_name=False)
        df = df.drop(columns=["wyId"])
        df["seasonId"] = df["seasonId"].astype(int)
        df["endDate"] = np.where(
            df["endDate"] == "0000-00-00", np.nan, df["endDate"]
        )
        df["ongoing"] = np.where(
            (pd.to_datetime(df["startDate"]) < datetime.now())
            & (pd.to_datetime(df["endDate"]) > datetime.now()),
            True,
            False,
        )
        return df
