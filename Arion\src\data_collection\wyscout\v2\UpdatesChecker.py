from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict

import pytz

import pandas as pd
from sqlalchemy import create_engine


from src.helper_funcs import (
    get_wyscout_response,
    fast_write_sql,
    dedupe_table,
    get_cloud_logger,
    cloud_log_struct,
    check_existance,
)
from settings import postgres_prod_str


class UpdatesChecker:
    def __init__(
        self,
        table_name: str,
        object_type: str,
        id_name: str,
        from_scratch: bool = False,
        empty_payload: str = "true",
        all_objects_table: str = None,
        only_active: bool = False,
        all_objects_schema: str = "wyscout",
        custom_ids_list: list = None,
    ):
        """Class for updating objects with changes

        Args:
            table_name (str): name of table where ids for collection are stored
            id_name (str): playerId, matchId, etc
            object_type (str): match, transfers, etc
            from_scratch(bool, optional): whether we are collecting everything
            from scratch or checking wyscout changed objects endpoint
            empty_payload (str, optional): param for ws query. Defaults to 'true'.
            all_objects_table(str, optional): table name such as seasons_teams;
            needs to be passed if collecting from scratch
            needs to be passed if collecting from scratch
            only_active(bool, optional): if collecting sesaons_x type of object,
            whether to add to collection table only active seasons
        """
        self.cnx_prod = create_engine(postgres_prod_str)
        self.base_url = "https://apirest.wyscout.com/v3/updatedobjects"
        self.object_type = object_type
        self.empty_payload = empty_payload
        self.table_name = table_name
        self.id_name = id_name
        self.from_scratch = from_scratch
        if from_scratch:
            if all_objects_table is None:
                raise ValueError(
                    "If collecting from scratch, you need to pass all objects"
                    " table name and id name"
                )
            self.all_objects_table = all_objects_table
            self.only_active = only_active
            self.all_objects_schema = all_objects_schema
        self.custom_ids_list = custom_ids_list

        self.resp: Dict[str, Dict[int, Dict]] = {}
        self.updated_ids = None
        self.update_timestamps_table = "updates_timestamps"
        self.last_updated = None
        self.last_updated_new = None
        self.written_to_db = False
        self.wyscout_tz = pytz.timezone("Europe/Rome")
        self.never_updated = False
        self.logger = get_cloud_logger()

    def check_last_update(self):
        """Gets the last time this object has been updated"""
        self.last_updated = pd.read_sql(
            f"""SELECT last_updated
                                             FROM  meta.{self.update_timestamps_table}
                                             WHERE object_type = '{self.object_type}' """,
            self.cnx_prod,
            parse_dates=["last_updated"],
        )
        max_back = datetime.now(self.wyscout_tz).replace(
            tzinfo=None
        ) - timedelta(days=6.99)
        if len(self.last_updated) > 0:
            self.last_updated = self.last_updated.squeeze()
        else:
            self.last_updated = max_back
            self.never_updated = True
        # if we havent updated in 7 days, cap it at that since this is ws's max:
        self.last_updated = max(
            self.last_updated,
            max_back,
        )
        self.last_updated = self.last_updated.strftime("%Y-%m-%d %H:%M:%S")

    def get_changed_objects(self):
        """
        Gets response from ws changed objects api endpoint
        """
        params = {
            "updated_since": self.last_updated,
            "emptyPayload": self.empty_payload,
            "type": self.object_type,
        }

        self.resp = get_wyscout_response(self.base_url, params)
        self.last_updated_new = datetime.now(self.wyscout_tz).replace(
            tzinfo=None
        )

    def setup_collection_table(self):
        setup = f"""CREATE TABLE IF NOT EXISTS meta.{self.table_name} ("{self.id_name}" integer);
                    CREATE INDEX IF NOT EXISTS idx_{self.table_name}_{self.id_name} 
                    ON meta.{self.table_name}("{self.id_name}");"""
        self.cnx_prod.execute(setup)

    def get_all_objects_from_scratch(self):
        """Get possible ids for a given object if collecting from scratch and
        write them in the respective meta.x_for_collection table
        """
        base = f"""INSERT INTO meta.{self.table_name}
                                (SELECT cl."{self.id_name}"
                                FROM {self.all_objects_schema}.{self.all_objects_table} cl
                                """

        join_clause = f""" LEFT JOIN wyscout.{self.object_type} tg
            ON  cl."{self.id_name}" = tg."{self.id_name}" """

        # check if taget table exists because if we are just starting, we dont need
        # to check if there are already collected records:
        existance = check_existance(self.cnx_prod, self.object_type, "wyscout")
        where_clause = (
            f""" WHERE tg."{self.id_name}" IS NULL """
            if existance
            else """WHERE true """
        )
        query = base + join_clause + where_clause + ")"
        if self.only_active:
            with open("src/queries/select_active_seasons.sql") as f:
                act_seasons_query = f.read()
            #  when we are collecting only active, we want to go through seasons that are already collected,
            # otherwise we wont be collecting anything new for those that have already started
            # (e.g. a new player in a season so the players have increased from 800 to 1000)
            query = f"""INSERT INTO meta.{self.table_name}
                    ({act_seasons_query} )"""
        self.cnx_prod.execute(query)
        self.last_updated_new = datetime.now(self.wyscout_tz).replace(
            tzinfo=None
        )
        self.written_to_db = True

    def prep_changed_objects(self):
        """
        Converts it into a table for writing to db
        """
        self.updated_ids = pd.DataFrame(
            [x for x in self.resp[self.object_type]], columns=[self.id_name]
        )
        self.updated_ids[self.id_name] = self.updated_ids[self.id_name].astype(
            int
        )

    def update_last_updated(self):
        """Updates the time stamp of last updated object"""
        if not self.written_to_db:
            raise ValueError("Changed objects havent been written to db yet")

        if self.never_updated:
            sql = f"""INSERT INTO meta.{self.update_timestamps_table}
                        VALUES ('{self.object_type}', '{self.last_updated_new}') """
        else:
            sql = f"""UPDATE meta.{self.update_timestamps_table}
                    SET last_updated = '{self.last_updated_new}'
                    WHERE object_type = '{self.object_type}'  """

        self.cnx_prod.execute(sql)

    def get_custom_list_objects(self):
        if self.custom_ids_list is None:
            raise ValueError("No custom id list provided")
        temp_df = pd.DataFrame({self.id_name: self.custom_ids_list})
        fast_write_sql(
            temp_df,
            self.table_name,
            self.cnx_prod,
            schema="meta",
            if_exists="replace",
        )
        dedupe_table(self.cnx_prod, self.table_name, "meta", [self.id_name])
        return

    def write_to_db(self):
        """
        Writes updated df to db
        """
        fast_write_sql(
            self.updated_ids,
            self.table_name,
            self.cnx_prod,
            schema="meta",
            if_exists="append",
        )
        cloud_log_struct(
            self.logger,
            {
                "updater": self.__class__.__name__,
                "object_type": self.object_type,
                "action": "write_updated_ids",
                "ids": self.updated_ids[self.id_name]
                .astype(float)
                .unique()
                .tolist(),
            },
        )
        self.written_to_db = True

    def get_updates(self):
        self.setup_collection_table()
        if self.custom_ids_list is not None:
            self.get_custom_list_objects()
            return
        self.check_last_update()
        # if from strach we directly update the collections table through sql insert:
        if self.from_scratch:
            self.get_all_objects_from_scratch()
        else:
            # if we are updating changed objects, we call the wyscout api, prep and write
            self.get_changed_objects()
            self.prep_changed_objects()
            self.write_to_db()
        # deduping the for collection table:
        dedupe_table(self.cnx_prod, self.table_name, "meta", [self.id_name])
        # updating the last updated timestamp if everything else is gucci:
        self.update_last_updated()
