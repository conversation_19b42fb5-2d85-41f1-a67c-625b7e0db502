from abc import ABC

from sqlalchemy import create_engine

from src.helper_funcs import get_cloud_logger, cloud_log_struct, fast_read_sql
from settings import postgres_prod_str


class Filler(ABC):
    """Generic abstract class that is used to fill gaps where some related data was not collected for some reason

    Args:
        ABC ([type]): [description]
    """

    def __init__(self):
        self.logger = get_cloud_logger()
        self.cnx_prod = create_engine(postgres_prod_str)
        self.schema = "wyscout"
        self.gaps_map = {}
        self.dependencies_map = [
            {
                "table": "areas",
                "id": "area_id",
                "child_tables": ["competitions"],
            },
            {
                "table": "competitions",
                "id": "competitionId",
                "child_tables": ["seasons", "matches"],
            },
            {
                "table": "teams",
                "id": "teamId",
                "child_tables": [
                    "lineups",
                    "formations",
                    "seasons_teams",
                    "cups_standings",
                    "leagues_standings",
                    {"table": "matches", "column": "home_teamId"},
                    {"table": "matches", "column": "away_teamId"},
                    {"table": "players", "column": "currentTeamId"},
                    {"table": "players", "column": "currentNationalTeamId"},
                ],
            },
            {
                "table": "players",
                "id": "playerId",
                "child_tables": [
                    "lineups",
                    "advanced_stats",
                    "formations",
                    "player_match_positions",
                    "seasons_players",
                    "transfers",
                ],
            },
            {
                "table": "seasons",
                "id": "seasonId",
                "child_tables": [
                    "matches",
                    "seasons_matches",
                    "seasons_teams",
                    "seasons_players",
                    "cups_standings",
                    "leagues_standings",
                ],
            },
            {
                "table": "matches",
                "id": "matchId",
                "child_tables": [
                    "lineups",
                    "formations",
                    "events",
                    "advanced_stats",
                    "seasons_matches",
                ],
            },
        ]

    def get_gaps(self):
        for dep in self.dependencies_map:
            parent_name = dep["table"]
            parent_id = dep["id"]
            parent_schema = dep.get("parent_schema", self.schema)
            for child in dep["child_tables"]:
                if isinstance(child, str):
                    child_name = child
                    child_id = parent_id
                    child_schema = parent_schema
                else:
                    child_name = child["table"]
                    child_id = child["column"]
                    child_schema = child.get("child_schema", self.schema)

                query = f"""SELECT "{child_id}"
                            FROM   {child_schema}.{child_name} ch
                            WHERE  NOT EXISTS (
                            SELECT
                            FROM   {parent_schema}.{parent_name}
                            WHERE  "{parent_id}" = ch."{child_id}"
                            )
                        """
                missing_ids = fast_read_sql(query, self.cnx_prod)
                missing_ids = missing_ids.rename(columns={child_id: parent_id})
                missing_ids = missing_ids[parent_id].unique().tolist()
                print(parent_name, child_name, len(missing_ids))

                if len(missing_ids) > 0:
                    cloud_log_struct(
                        self.logger,
                        {
                            "action": "found_missing_ids",
                            "parent_table": parent_name,
                            "child_table": child_name,
                            "ids": missing_ids,
                        },
                    )
                if parent_name not in self.gaps_map:
                    self.gaps_map[parent_name] = {parent_id: missing_ids}
                else:
                    self.gaps_map[parent_name][parent_id] += missing_ids
