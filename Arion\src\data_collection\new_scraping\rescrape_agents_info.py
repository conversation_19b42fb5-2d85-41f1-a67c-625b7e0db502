# It will be used in a DAG in the future
# Running an example script:
# Collecting data for given players and populating the required tables
from async_timeout import asyncio
from src.helper_funcs import fast_write_sql
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine

from src.data_collection.new_scraping.scraping.scrape_agents_info import TmAgentsScraper

SCHEMA = "meta_scraping"
RAW_DATA_TABLE = "raw_agents_info"

engine = create_engine(postgres_prod_str)
# asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def run(scraper: TmAgentsScraper):
    df = asyncio.run(scraper.loop_through_urls())
    fast_write_sql(
       df,
       RAW_DATA_TABLE,
       cnx=engine,
       if_exists="replace",
       schema=SCHEMA
    )
    # df.to_sql(RAW_DATA_TABLE, engine, SCHEMA, if_exists="replace", index=False,
    # chunksize=1000, method='multi')


if __name__ == "__main__":
    scraper = TmAgentsScraper()
    run(scraper)
    engine.execute(f"delete from {SCHEMA}.raw_agents_info where agency is null ")
