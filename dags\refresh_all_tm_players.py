import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "refresh_all_tm_players",
    "start_date": datetime(2021, 12, 6),
    "schedule_interval": timedelta(days=7),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            # "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    populate_progresss_table = PostgresOperator(
        task_id="populate_progress_table",
        sql="""INSERT INTO meta_scraping.player_progress_table SELECT * FROM transfermarkt_players.tm_to_ws_ids""",
        database="wyscout_raw_production",
    )

    truncate_raw_tables = BashOperator(
        task_id="truncate_raw_tables",
                bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/truncate_raw_profile_tables_v2.py
                           """,
    )


    rescrape_players = BashOperator(
        task_id="rescrape_players",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/rescrape_player_profiles_v2.py
                           """,
    )

    save_cleaned_profiles = BashOperator(
        task_id="save_cleaned_profiles",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_profiles_v2.py
                           """,
    )

    save_cleaned_transfers = BashOperator(
        task_id="save_cleaned_transfers",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_transfers_v2.py
                           """,
    )

    save_cleaned_contracts = BashOperator(
        task_id="save_cleaned_contracts",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_historical_contracts_v2.py
                           """,
    )


    migrate_agents = PostgresOperator(
        task_id="migrate_agents",
        database="wyscout_raw_production",
        sql="""INSERT INTO transfermarkt_players.tm_historical_agents SELECT * FROM transfermarkt_players.raw_agent_history_table""",
    )

    migrate_profiles = BashOperator(
        task_id="migrate_profiles",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/migrate_profiles.py
                           """,
    )

(
    populate_progresss_table
    >> truncate_raw_tables
    >> rescrape_players
    >> [
        save_cleaned_contracts,
    ]
)
migrate_agents

(
    save_cleaned_transfers
    # >> map_transfer_types
)
(
    save_cleaned_profiles
    >> migrate_profiles
)
