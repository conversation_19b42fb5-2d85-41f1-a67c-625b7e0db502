from typing import Optional

from pydantic import BaseModel
from sqlalchemy import create_engine
import pandas as pd

from settings import postgres_prod_str
from src.helper_funcs import fast_read_sql, fast_write_sql
from src.reporting.TweetBot import TweetInfo


class PlayerAppearance(BaseModel):
    playerId: int
    age: int
    player_name: str
    role_name: str
    matchId: int
    teamId: int
    team_name: str
    normalised_team_name: str
    competition_name: str
    label: str
    date: str
    minutesTagged: int
    goals: int
    tm_link: str


class TalentHunter:
    def __init__(self):
        self.cnx_prod = create_engine(postgres_prod_str)

    def get_next_record(self) -> Optional[PlayerAppearance]:
        rec = fast_read_sql(
            """SELECT 	
                ypa.*, 
                f_unaccent("team_name") as normalised_team_name,
                case 
                    when tp2."playerId" is null then 0
                    else 1
                end as previously_posted
                FROM derived_tables.young_players_appearances  ypa
                LEFT JOIN derived_tables.twitter_posts tp
                using ("matchId", "playerId")
                LEFT JOIN derived_tables.twitter_posts tp2
                using ("playerId")
                where tp."playerId" is null 
                ORDER BY   previously_posted, date desc, "divisionLevel" asc, goals desc, age       
                LIMIT 1 """,
            self.cnx_prod,
        )
        if len(rec) == 0:
            return None
        return PlayerAppearance(**rec.to_dict(orient="records")[0])

    @staticmethod
    def generate_content(rec: PlayerAppearance) -> str:
        if rec.goals == 0:
            goals_str = ""
        else:
            goals_str = (
                "scored 1 goal in"
                if rec.goals == 1
                else f"scored {rec.goals} goals in"
            )
        hashtag_team_name = rec.normalised_team_name.replace(" ", "")
        full_str = (
            f"{rec.player_name} ({rec.age} years) played "
            f"{rec.minutesTagged} minutes for {rec.team_name} as a "
            f"{rec.role_name}. \nGame: {rec.label} from {rec.competition_name}"
            f"\nDate: {rec.date}"
        )
        if goals_str:
            full_str = full_str.replace("played", goals_str)
        for s in (f" {rec.tm_link}", f" #{hashtag_team_name}", " #scouting", " #football"):
            if len(full_str.strip().replace("nan", "")) + len(s) <= 280:
                full_str += s
        return full_str.strip().replace("nan", "")

    def save_twitter_post(self, rec: PlayerAppearance, tweet_info: TweetInfo):
        export_dict = tweet_info.dict()
        export_dict.update(
            {
                k: v
                for k, v in rec.dict().items()
                if k in {"playerId", "matchId"}
            }
        )
        df = pd.DataFrame([export_dict])
        fast_write_sql(
            df,
            "twitter_posts",
            self.cnx_prod,
            schema="derived_tables",
            if_exists="append",
        )
