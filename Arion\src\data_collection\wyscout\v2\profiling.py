from datetime import datetime
import sys
import pandas as pd
import numpy as np
import line_profiler

sys.path.append("/home/<USER>/ArionFlow/Arion/")
from src.data_collection.wyscout.v2.AdvancedStatsPrepper import (
    AdvancedStatsPrepper,
)
from src.helper_funcs import get_wyscout_response, fast_read_sql

et = get_wyscout_response(
    "https://apirest.wyscout.com/v3/matches/2658345/events",
    params={
        "fetch": "match,formations",
        "exclude": "names,positions,formations",
    },
)

# run with kernprof -l -v  --scirpt name--
@profile
def flatten_dict_cols(
    df: pd.DataFrame, keep_parent_col_name: bool, subset: List[str] = None
) -> pd.DataFrame:
    """Takes a pandas df and flattens all nested dict columns

    Args:
        df (pd.DataFrame):
        keep_parent_col_name (bool): if true then resulting columns will be parent-column_subcolumns,
        otherwise then column will be directly equal to the subcolumn name
        subset (list, optional): if a list is passed this is parformed only on a subset of columns. Defaults to None.


    Returns:
        pd.DataFrame: flattened df
    """
    cols = df.columns if subset is None else subset
    df_list = []
    cols_to_drop = []
    for col in cols:
        first_non_null = (
            df[col].dropna().values[0] if df[col][0] is None else df[col][0]
        )
        if isinstance(first_non_null, dict):
            keys = first_non_null.keys()
            filled = df[col].where(
                df[col].notnull(), lambda x: {k: np.nan for k in keys}
            )
            flat_col_df = pd.DataFrame(filled.tolist())
            if keep_parent_col_name:
                flat_col_df.columns = [
                    f"{col}_{subcol}" for subcol in flat_col_df.columns
                ]
            df_list.append(flat_col_df)
            cols_to_drop.append(col)
    df = df.drop(columns=cols_to_drop)
    df_list.append(df)
    df = pd.concat(df_list, axis=1)
    return df


df = pd.DataFrame(et["events"])
ch = flatten_dict_cols(df, True)
