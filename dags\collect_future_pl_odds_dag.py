import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "collect_future_pl_odds_dag",
    "start_date": datetime(2021, 12, 30),
    "schedule_interval": timedelta(days=7),
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    rescrape_upcoming_odds = BashOperator(
        task_id="rescrape_upcoming_odds",
                bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          python3 src/data_collection/new_scraping/oddsportal/rescrape_upcoming_odds.py
                           """,
    )
    move_past_matches_to_past_matches_table = BashOperator(
                task_id="move_past_matches_to_past_matches_table",
                bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          python3 src/data_collection/new_scraping/oddsportal/check_upcoming_matches.py
                           """,
    )

(
    move_past_matches_to_past_matches_table
    >> rescrape_upcoming_odds 
)