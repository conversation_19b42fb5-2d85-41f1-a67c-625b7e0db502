from settings import postgres_prod_str
from sqlalchemy.engine import create_engine


def main():
    engine = create_engine(postgres_prod_str)
    with engine.begin() as con:

        con.execute("""DELETE FROM transfermarkt.transfermarkt_data td 
                    USING meta_scraping.player_migration_table pmt
                    WHERE td.\"playerId\" = pmt.\"playerId\"""")

        con.execute(
            "INSERT INTO transfermarkt.transfermarkt_data SELECT * FROM"
            " meta_scraping.player_migration_table"
        )
        # When the migr table is full move all the data to the existing table
        query = """DELETE FROM meta_scraping.player_migration_table tmt
                    USING transfermarkt.transfermarkt_data tmp
                    WHERE tmt.\"playerId\" = tmp.\"playerId\""""
        con.execute(query)

if __name__ == "__main__":
    main()