import pandas as pd
from typing import Type

from src.helper_funcs import get_sql_array_str, cloud_log_struct, fast_write_sql
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.Updates<PERSON>hecker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.data_collection.wyscout.v2.Updater import Updater


class TransfersOrchestrator(Orchestrator):
    def __init__(
        self,
        batch_size: int,
        updates_checker: Type[UpdatesChecker],
        updater: Type[Updater],
        from_scratch=False,
        skip_empty_games: bool = True,
        phantom_objects: list = None,
    ):
        super().__init__(
            batch_size=batch_size,
            updates_checker=updates_checker,
            updater=updater,
            from_scratch=from_scratch,
            skip_empty_games=skip_empty_games,
            phantom_objects=phantom_objects,
        )

    def handle_write_single_table(
        self,
        df: pd.DataFrame,
        ids: str,
        object_type: str,
        connection,
        cursor,
        dtype=None,
    ):
        """This method id concerned with writing a single table. In match objects orchestrattion,
        this will be called once for formations, lineups, events, match, advanced_stats. For seasons_x
        collection from scratch this will be called once for seasons and oncer for the object (e.g. players)
        """
        try:
            if not self.from_scratch:
                # this because of wyscout's fucked up updates tracking,
                # we need to delete both all records with playerId
                # and the transferId because a transfer can have a player changed
                # and then unless we check for the transfer ids
                # we can get a duplicate pkey error:
                child_ids = get_sql_array_str(df[self.updater.id_name].tolist())
                # updating later, deleting first
                self.drop_updated_existing_records(
                    cursor, object_type, parent_ids=ids, child_ids=child_ids
                )
            fast_write_sql(
                df,
                object_type,
                self.cnx_prod,
                schema="wyscout",
                dtype=dtype,
                if_exists="append",
                transaction=True,
                cursor=cursor,
                connection=connection,
            )

            cloud_log_struct(
                self.logger,
                {
                    "orchestrator": self.__class__.__name__,
                    "updater": self.updater.__class__.__name__,
                    "object_type": object_type,
                    "action": "write_df_before_commit",
                    "ids": df[self.updater.id_name]
                    .astype(float)
                    .unique()
                    .tolist(),
                },
            )
        except:
            self.rollback_failed_write(connection, cursor)

    def drop_updated_existing_records(
        self,
        cursor,
        object_type: str,
        parent_ids: str,
        child_ids: str,
    ):
        """Deleting old records that will be updated

        Args:
            cursor ([type]): [description]
            object_type ([type]): [description]
            ids ([type]): [description]
        """
        cursor.execute(
            f"""DELETE FROM wyscout.{object_type} 
                WHERE "{self.updates_checker.id_name}" IN {parent_ids}
                OR "{self.updater.id_name}" IN {child_ids} """
        )

    async def setup_collection(self):
        """Set active collection status and generate the collection list"""
        self.set_collection_status(True)
        self.setup_empty_ids_table()
        self.get_objects_for_collection()
        # for transfers the check for phantom parent objects happens at the begining,
        # because once we have a list of the players with the changed player ids,
        # we can already know if we have someone missing:
        if self.phantom_objects is not None:
            await self.handle_phantoms(
                self.phantom_objects[0], self.collection_list
            )
