# Import the Secret Manager client library.
import os
from google.cloud import secretmanager

def main():
    # os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.path.join(os.getcwd(), 'src/models/player_valuation/credentials.json')
    project_id = "footballanalytics"
    # relevant_prefix = ['BASE', 'PV', 'ADMIN']
    relevant_prefix = ['BASE', 'PV']

    # Create the Secret Manager client.
    client = secretmanager.SecretManagerServiceClient()

    # Build the parent name from the project.
    parent = f"projects/{project_id}"

    # List all secrets.
    for secret in client.list_secrets(request={"parent": parent}):
        name = secret.name
        
        name_check = [x in name for x in relevant_prefix]
        if any(name_check):
            version = f'{name}/versions/latest'
            resp =  client.access_secret_version(request={"name": version})
            value = resp.payload.data.decode("UTF-8")
            env_name = name.split('/')[-1]
            os.environ[env_name] = value
