import pandas as pd

from src.helper_funcs import get_wyscout_response
from src.data_collection.wyscout.v2.Updater import Updater


class AreasUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/areas"
        self.object_type = self.base_url.split("/")[-1]
        self.if_exists = "replace"
        self.id_name = "area_id"
        self.create_unknown_records_flag = (
            True  # applies to all tables that are being dropped and re-created
        )
        self.write_in_loop = True

    def collect(self):
        return get_wyscout_response(self.base_url)

    def prep(self, resp) -> pd.DataFrame:
        df = pd.DataFrame(resp[self.object_type])
        df = df.rename(columns={"id": "area_id"})
        return df
