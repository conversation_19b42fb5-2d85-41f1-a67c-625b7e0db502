CREATE TABLE derived_tables.team_segment_bands AS
SELECT
	CASE WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) <= 20 THEN
		1
	WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) BETWEEN 21 AND 60 THEN
		2
	WHEN rank() OVER (ORDER BY base.median_player_value DESC nulls LAST) BETWEEN 61 AND 600 THEN
		3
	ELSE
		4
	END AS team_rank,
	base."teamId",
	base.name
FROM (
	SELECT
		percentile_disc(0.5) WITHIN GROUP (ORDER BY tm.current_value) AS median_player_value,
		t."teamId",
		t."name"
	FROM
		transfermarkt.transfermarkt_data tm,
		wyscout.players pl,
		wyscout.teams t
	WHERE
		pl."currentTeamId" = t."teamId"
		AND tm."playerId" = pl."playerId" GROUP BY
			t."teamId",
			t."name"
		HAVING
			count(*) > 10
		ORDER BY
			percentile_disc(0.5) WITHIN GROUP (ORDER BY tm.current_value) DESC nulls LAST) base;

ALTER TABLE derived_tables.team_segment_bands
	ADD PRIMARY KEY ("teamId");

ALTER TABLE derived_tables.team_segment_bands
	ADD CONSTRAINT "team_segment_bands_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES wyscout."teams" ("teamId") DEFERRABLE;

GRANT ALL ON positions_view TO elvan, kliment;

