# Automatically generated by https://github.com/damnever/pigar.
apache-airflow==2.0.1
PyYAML
SQLAlchemy
Unidecode 
aiohttp 
autopep8
black
beautifulsoup4 
bgtasks
# cdn 
django-admin-material 
django-cache-management 
django-condition-chain 
django-generic-counter 
django-river 
django-zoneke-contrib 
djlime-settings 
edx-opaque-keys 
fuzzywuzzy 
google_cloud_logging 
google-cloud-storage
joblib 
js2xml 
levenshtein 
line_profiler 
lxml 
numpy 
pandas 
picorg 
# pv 
psycopg2-binary<2.9 #very important so that fast_write_sql works
pycountry_convert 
pydantic 
python_dotenv 
pytrends 
pytz 
ratelimit 
requests 
scipy 
sklearn 
spark-yarn-submit 
statsmodels 
tenacity 
trade-common 
tweepy 
google-cloud-storage
darts
openpyxl
xlsxwriter