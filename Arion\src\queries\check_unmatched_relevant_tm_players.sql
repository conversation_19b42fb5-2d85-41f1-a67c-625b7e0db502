TRUNCATE TABLE meta_scraping.unmatched_tm_players;
INSERT INTO meta_scraping.unmatched_tm_players SELECT
    cnt."playerId"
FROM
    (
        SELECT
            adv."playerId"
        FROM
            wyscout.player_match_info adv, 
			wyscout.players ap
        WHERE
            adv."date" > '2018-01-01'
			AND ap."status" = 'active'
			AND ap."playerId" = adv."playerId"
        GROUP BY
            adv."playerId"
        HAVING
            count(adv."matchId") > 7
    ) cnt
    LEFT JOIN transfermarkt.tm_to_ws_ids tm ON tm."playerId" = cnt."playerId"
WHERE
    tm."playerId" IS NULL