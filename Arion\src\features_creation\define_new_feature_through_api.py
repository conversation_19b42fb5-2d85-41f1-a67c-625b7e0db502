import requests
import json

def main():
    var_list = []
    regular = [
        "inplay_shot_assists",
        "second_shot_assist",
        "third_shot_assist",
        "interceptions",
        "inplay_ground_interceptions",
        "non_clearance_interceptions",
        "opponent_half_recoveries",
        "linkup_plays",
        "through_passes",
        "counterpressing_recoveries",
        "second_assists",
        "shot_assists",
        "progressive_runs",
        "shot_assists_penalty_area",
    ]

    ratio = [
        {
            "numerator": "defensive_duels_stopped_progress",
            "denominator": "defensive_duels",
        },
        {
            "numerator": "gk_saves_inplay_inside_penalty",
            "denominator": "gk_shots_against",
        },
        # {
        #     "numerator": "gk_saves_inplay_outside_penalty",
        #     "denominator": "shots_on_target_against_outside_penalty",
        # },
        {"numerator": "gk_bad_saves", "denominator": "gk_saves"},
        # {
        #     "numerator": "interception_accurate_passes",
        #     "denominator": "interception_total_passes",
        # },
        {"numerator": "active_recoveries", "denominator": "recoveries"},
        {"numerator": "positional_recoveries", "denominator": "recoveries"},
        {"numerator": "reactions_after_loss", "denominator": "losses"},
    ]

    strength = [
        {"numerator": "shots_on_target", "denominator": "shots"},
        {
            "numerator": "left_foot_shots_on_target",
            "denominator": "left_foot_shots",
        },
        {
            "numerator": "right_foot_shots_on_target",
            "denominator": "right_foot_shots",
        },
        {
            "numerator": "shots_after_inplay_cross_on_target",
            "denominator": "shots_after_inplay_cross",
        },
        {
            "numerator": "successful_ground_forward_passes_final_third",
            "denominator": "ground_forward_passes_final_third",
        },
        {"numerator": "aerial_duels_won", "denominator": "aerial_duels"},
        {"numerator": "defensive_duels_won", "denominator": "defensive_duels"},
        {
            "numerator": "defensive_duels_won_own_third",
            "denominator": "defensive_duels_own_third",
        },
        {
            "numerator": "defensive_duels_won_midfield",
            "denominator": "defensive_duels_midfield",
        },
        {
            "numerator": "defensive_duels_won_final_third",
            "denominator": "defensive_duels_final_third",
        },
        {
            "numerator": "defensive_duels_won_central_own_third",
            "denominator": "defensive_duels_central_own_third",
        },
        {
            "numerator": "successful_actions_under_pressure",
            "denominator": "actions_under_pressure",
        },
        {
            "numerator": "successful_dribbles_no_foul",
            "denominator": "dribbles_no_foul",
        },
        {
            "numerator": (
                "successful_long_aerial_diagonal_passes_opposition_half"
            ),
            "denominator": "long_aerial_diagonal_passes_opposition_half",
        },
        {
            "numerator": "successful_long_aerial_passes_not_clearance",
            "denominator": "long_aerial_passes_not_clearance",
        },
        {
            "numerator": "successful_passes_to_final_third",
            "denominator": "passes_to_final_third",
        },
        {
            "numerator": "successful_passes_to_penalty_area",
            "denominator": "passes_to_penalty_area",
        },
        {"numerator": "successful_crosses", "denominator": "crosses"},
        {"numerator": "gk_save_with_reflexs", "denominator": "gk_saves"},
        {"numerator": "gk_saves", "denominator": "gk_shots_against"},
    ]

    for x in regular:
        var_list.append(
            {
                "var_name": f"{x}_reg",
                "var_def": json.dumps({
                    "numerator": x,
                    "denominator": "",
                    # "exp_var": "",
                    "table": "player_match_info",
                    "feature_type": "regular",
                }),
            }
        )
    for x in ratio:
        var_list.append(
            {
                "var_name": f"{x['numerator']}_to_{x['denominator']}_rat",
                "var_def": json.dumps({
                    "numerator": x["numerator"],
                    "denominator": x["denominator"],
                    # "exp_var": "",
                    "table": "player_match_info",
                    "feature_type": "ratio",
                }),
            }
        )
    for x in strength:
        var_list.append(
            ({
                "var_name": f"{x['denominator']}_str",
                "var_def": json.dumps({
                    "numerator": x["numerator"],
                    "denominator": x["denominator"],
                    "exp_var": x["denominator"],
                    "table": "player_match_info",
                    "feature_type": "strength",
                    "accuracy_vs_activity": "activity",
                    "use_scaled_exp": False,
                }),
            })
        )
    for definition in var_list:
        try:
            r = requests.put(
                "https://rank-api-aolebn4toq-ew.a.run.app/define_variables",
                params=definition,
            )
            r.raise_for_status()
            print(definition["var_name"], ' done')
        except:
            print(definition["var_name"], ' exists')
            r = requests.patch(
                "https://rank-api-aolebn4toq-ew.a.run.app/update_variables",
                params=definition,
            )
            if r.status_code != 200:
                print(r.text)
            r.raise_for_status()
            print(definition["var_name"], ' done')


if __name__ == "__main__":
    main()


