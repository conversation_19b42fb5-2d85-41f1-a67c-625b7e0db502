from sqlalchemy import create_engine
from settings import postgres_prod_str
from src.helper_funcs import get_sql_array_str, fast_read_sql


def main():
    cnx = create_engine(postgres_prod_str)
    schema_list = ['wyscout', 'derived_tables', 'transfermarkt', 'meta']
    schema_str = get_sql_array_str(schema_list)
    df = fast_read_sql(f'''SELECT table_schema, table_name FROM information_schema.tables 
    WHERE table_schema IN {schema_str} order by table_schema''', cnx)
    tables = list(zip(df["table_schema"], df["table_name"]))
    for t in tables:
        cnx.execution_options(isolation_level="AUTOCOMMIT").execute(f'vacuum full  verbose analyze {t[0]}.{t[1]};')

    # return '\n '.join(f'vacuum full  verbose analyze {t[0]}.{t[1]};' for t in tables)


if __name__ == '__main__':
    main()