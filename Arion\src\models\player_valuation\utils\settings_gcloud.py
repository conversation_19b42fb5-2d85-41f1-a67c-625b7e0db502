import os
from src.models.player_valuation.utils import get_secrets

get_secrets.main()
PG_PRODUCTION_DB = os.environ.get("BASE_PG_PRODUCTION_DB")
PG_RESEARCH_DB = os.environ.get("BASE_RESEARCH_DB")
PG_USR = os.environ.get('PV_USR')
PG_PASS = os.environ.get("PV_PASS")
PG_HOST = os.environ.get("BASE_PG_HOST")
PG_PORT = os.environ.get("BASE_PG_PORT")
DB_SOCKET_DIR = os.environ.get("DB_SOCKET_DIR", "/cloudsql")
CLOUD_SQL_CONN_NAME = os.environ.get("BASE_CLOUD_SQL_CONN_NAME")

postgres_prod_str_unix = f'postgresql+psycopg2://{PG_USR}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_PRODUCTION_DB}' \
                    f'?host={DB_SOCKET_DIR}/{CLOUD_SQL_CONN_NAME}'
postgres_prod_str = f'postgresql://{PG_USR}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_PRODUCTION_DB}'
