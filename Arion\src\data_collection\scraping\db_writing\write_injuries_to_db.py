from sqlalchemy import create_engine, types

from src.data_collection.scraping.db_writing.BaseDbWriter import BaseDbWriter
from src.helper_funcs import read_config
from settings import postgres_dev_str


class InjuryWriter(BaseDbWriter):
    def prep_for_writing(self):

        self.merged_df = self.merged_df.rename(
            columns={
                "player_url": "player_injuries_url",
            }
        )
        self.dtypes = {
            "season": types.String(),
            "injury": types.String(),
            "injured_from": types.Date(),
            "injured_until": types.Date(),
            "duration": types.Integer(),
            "games_missed": types.Integer(),
            "playerId": types.BigInteger(),
            "player_injuries_url": types.String(),
        }
        self.merged_df = self.merged_df[list(self.dtypes.keys())]


def main():
    cnx = create_engine(postgres_dev_str)
    config = read_config("config.yml")
    writer = InjuryWriter(
        config["db_tables"]["transfermarkt_injuries"],
        config["db_tables"]["tm_to_ws_ids"],
        config["scraping"]["directories"]["cleaned_player_injuries"],
        cnx,
    )
    writer.read_df_to_match()
    writer.read_tm_ws_ids()
    writer.merge_tables()
    writer.prep_for_writing()
    writer.write_to_db()


if __name__ == "__main__":
    main()
