from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash_operator import BashOperator

# from dag_settings import workdir
workdir = '/home/<USER>/Projects/player-quality'

dag_params = {
    "dag_id": "one_off_random_shit",
    "start_date": datetime(2022, 8, 10),
    "schedule_interval": None,
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 0,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    one_off_random_shit = BashOperator(
        task_id="one_off_random_shit",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 /home/<USER>/Projects/player-quality/src/misc/create_reports.py""",
    )

    one_off_random_shit
