CREATE
OR REPLACE VIEW wyscout.player_info AS
SELECT
	*,
	p."birthDate" AS "birth_date",
	p."passportArea_name" AS "passport",
	p."birthArea_name" AS "birth_area",
	pr.primary_position as player_role, 
	tm.current_value AS tm_value,
	tm.player_url AS tm_link,
	tm.agent AS agency
FROM
	wyscout.players p
	LEFT JOIN (
		select
			"teamId",
			name as team_name,
			"divisionLevel",
			area_name as team_area_name,
			segment,
			category,
			smoothed_rating
		from
			wyscout.team_info
	) t ON (p."currentTeamId" = t."teamId")
	LEFT JOIN derived_tables.player_roles pr USING ("playerId")
	LEFT JOIN (
		select
			"playerId",
			player_url,
			citizenship,
			date_joined_current_team,
			contract_expiry,
			league_tier,
			league_country,
			current_value,
			player_value_last_update,
			highest_value,
			highest_value_date,
			agent,
			agent_id
		from
			transfermarkt.transfermarkt_data
	) tm USING ("playerId")
	LEFT <PERSON>IN (
		SELECT
			STRING_AGG(DISTINCT "injury", ', ') AS "all_injuries",
			"playerId",
			SUM(
				CASE
					WHEN injured_until IS NULL THEN 1
					ELSE 0
				END
			) AS "currently_injured",
			MAX("injured_until") AS "latest_injury",
			COUNT("playerId") AS "num_injuries",
			MAX("games_missed") AS "most_games_missed",
			MAX("duration") AS "longest_duration",
			SUM("games_missed") AS "total_games_missed",
			SUM("duration") AS "total_days_missed",
			AVG("duration") AS "average_recovery_time"
		FROM
			transfermarkt.transfermarkt_injuries
		GROUP BY
			"playerId"
	) tmi USING ("playerId")