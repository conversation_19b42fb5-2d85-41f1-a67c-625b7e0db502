from typing import Optional, Type, List, Union, Dict

import pandas as pd
import numpy as np

from src.helper_funcs import (
    fast_read_sql,
    fast_write_sql,
    get_sql_array_str,
    get_cloud_logger,
    cloud_log_text,
    cloud_log_struct,
    dedupe_table,
)
from src.data_collection.wyscout.v2.Updater import Updater
from src.data_collection.wyscout.v2.UpdatesChecker import UpdatesChecker
from src.data_collection.wyscout.v2.PlayersUpdater import PlayersUpdater
from src.data_collection.wyscout.v2.TeamsUpdater import TeamsUpdater
from src.data_collection.wyscout.v2.MatchesUpdater import MatchesUpdater


class Orchestrator(Updater):
    def __init__(
        self,
        batch_size: int,
        updates_checker: Type[UpdatesChecker],
        updater: Type[Updater],
        from_scratch: bool = False,
        skip_empty_games: bool = True,
        phantom_objects: List[str] = None,
    ):
        """Orchestrates update of object type where the updates_checker will check
        the changed objects API for changed objects, and then the updater will looop
        through batches of those ids and collect the new objects, finally if the existing
        objects exist already, they will be dropped from db and the new version will be inserted

        Args:
            batch_size (int): size of batches
            updates_checker (Type[UpdatesChecker]): object to perform update checking
            updater (Type[Updater]): object to update the new/changed records
            from_scratch(bool): if from scratch, updater will not check the changed the changed

            objects API but instead collect all available ids
        """
        super().__init__(None)
        self.batch_size = batch_size
        self.collection_list = None
        self.updates_checker = updates_checker
        self.updater = updater
        self.from_scratch = from_scratch
        self.skip_empty_games = skip_empty_games
        self.logger = get_cloud_logger()
        self.empty_data_table = f"empty_data_{self.updates_checker.object_type}"
        self.phantom_objects = phantom_objects

    def get_objects_for_collection(self):
        # the updates checker will populate with ids depending on whether it is from scrath or not and
        # then we just read them from the for_collection table regardless
        self.updates_checker.get_updates()
        if self.skip_empty_games and self.from_scratch:
            self.drop_empty_objects_from_collection()

        super().get_objects_for_collection(
            self.updates_checker.table_name,
            "meta",
            self.updates_checker.id_name,
        )
        cloud_log_struct(
            self.logger,
            {
                "orchestrator": self.__class__.__name__,
                "updater": self.updater.__class__.__name__,
                "collection_list_len": len(self.collection_list),
            },
        )

    def setup_empty_ids_table(self):
        self.cnx_prod.execute(
            f"""CREATE TABLE IF NOT EXISTS meta.{self.empty_data_table} 
        ("{self.updates_checker.id_name}" INT) """
        )

    def get_no_data_games_set(self):
        """Get a list of all objects with no data - this is relevant for when collecting for first time
        and the pipeline crashes for some reason.
        """
        # this is done to bootstrap the first time this is run
        self.empty_games_set = set(
            fast_read_sql(
                f'SELECT "{self.updates_checker.id_name}" from'
                f" meta.{self.empty_data_table}",
                self.cnx_prod,
            )[self.updates_checker.id_name].tolist()
        )

    def drop_empty_objects_from_collection(self):
        """This should run at the wrap up of all batches. This is not a problem since we are subseting
        the empty objects when we start, so we are not looping through them.
        """
        try:
            query = f"""DELETE FROM meta.{self.updates_checker.table_name} 
                WHERE "{self.updates_checker.id_name}" IN (
                    SELECT "{self.updates_checker.id_name}" FROM meta.{self.empty_data_table}
                ); COMMIT; """
            self.cnx_prod.execute(query)
        except Exception as e:  # TODO this exception can be handled in a more specific way
            print(e)
            cloud_log_text(
                self.logger, "No empty to be deleted from collecion table"
            )
            # this is done to bootstrap the first time this is run

    def handle_empty_ids(self):
        if self.from_scratch and len(self.updater.no_data_ids_list) > 0:
            no_ids_df = pd.DataFrame(
                {self.updates_checker.id_name: self.updater.no_data_ids_list}
            )
            fast_write_sql(
                no_ids_df,
                self.empty_data_table,
                self.cnx_prod,
                schema="meta",
                if_exists="append",
            )
            self.cnx_prod.execute(
                f"""CREATE INDEX IF NOT EXISTS idx_{self.empty_data_table}_{self.updates_checker.id_name} 
            ON meta.{self.empty_data_table}("{self.updates_checker.id_name}"); """
            )
            cloud_log_struct(
                self.logger,
                {
                    "orchestrator": self.__class__.__name__,
                    "updater": self.updater.__class__.__name__,
                    "object_type": "empty_data_objects",
                    "action": "write_empty_ids",
                    "ids": self.updater.no_data_ids_list,
                },
            )

    def get_prepped_ids_in_batch(
        self, batch: List[int], batch_i: int
    ) -> Union[str, None]:
        prepped_ids_in_batch = [
            x for x in batch if x in self.updater.prepped_ids_list
        ]
        if prepped_ids_in_batch:
            return get_sql_array_str(prepped_ids_in_batch)

        # if we have no ids, log empty batch:
        cloud_log_text(self.logger, f" batch {batch_i} empty")
        return None

    def handle_write_single_table(
        self,
        df: pd.DataFrame,
        ids: str,
        object_type: str,
        connection,
        cursor,
        dtype=None,
    ):
        """This method id concerned with writing a single table. In match objects orchestrattion,
        this will be called once for formations, lineups, events, match, advanced_stats. For seasons_x
        collection from scratch this will be called once for seasons and oncer for the object (e.g. players)
        """
        try:
            if not self.from_scratch:
                # updating later, deleting first
                self.drop_updated_existing_records(cursor, object_type, ids)
            fast_write_sql(
                df,
                object_type,
                self.cnx_prod,
                schema="wyscout",
                dtype=dtype,
                if_exists="append",
                transaction=True,
                cursor=cursor,
                connection=connection,
            )
            batch_ids = df[self.updater.id_name].astype(float).unique().tolist()
            for i in range(0, len(batch_ids), 1000):
                batch = batch_ids[i : i + 10000]
                cloud_log_struct(
                    self.logger,
                    {
                        "orchestrator": self.__class__.__name__,
                        "updater": self.updater.__class__.__name__,
                        "object_type": object_type,
                        "action": "write_df_before_commit",
                        "ids": batch,
                    },
                )
        except:
            self.rollback_failed_write(connection, cursor)

    def set_batch_for_collection(self, i: int) -> List[int]:
        cloud_log_text(self.logger, f"Start of batch {i}")
        if self.collection_list is None:
            raise ValueError("Undefined collection list")
        batch = self.collection_list[i : i + self.batch_size]
        self.updater.collection_list = batch
        return batch

    def drop_updated_existing_records(self, cursor, object_type: str, ids: str):
        """# deleting old records that will be updated

        Args:
            cursor ([type]): [description]
            object_type ([type]): [description]
            ids ([type]): [description]
        """
        cursor.execute(
            f"""DELETE FROM wyscout.{object_type} WHERE "{self.updates_checker.id_name}" IN {ids}"""
        )

    def change_unsolvable_phantom_ids(
        self,
        phantom_cols: List[str],
        updater_resp: pd.DataFrame,
        unsolvable_phantoms: List[int],
    ) -> pd.DataFrame:
        for col in phantom_cols:
            updater_resp[col] = np.where(
                ~updater_resp[col].isin(unsolvable_phantoms),
                updater_resp[col],
                -1,
            )
        return updater_resp

    def get_phantom_cols_list(
        self, source_object: str, phantom_object: str
    ) -> List[str]:
        """Get the names of the ids columns in the updater resp that need to be checked and/or set to -1

        Returns:
            List[str]: [description]
        """
        special_case_cols_dict = {
            "matches": ["home_teamId", "away_teamId"],
            "players": ["currentTeamId", "currentNationalTeamId"],
        }
        return special_case_cols_dict.get(
            source_object,
            [self.get_id_name_from_object_name(phantom_object)],
        )

    def get_prepped_ids_for_phantom_checking(
        self, updater_resp: pd.DataFrame, phantom_cols: List[str]
    ) -> List[int]:
        ids = []
        for col in phantom_cols:
            ids += updater_resp[col].astype(float).dropna().tolist()
        return ids

    async def loop_through_phantom_relationships(
        self,
        phantom_objects: Optional[List[str]],
        source_object: str,
        updater_resp: Union[pd.DataFrame, List[Dict[str, pd.DataFrame]]],
    ):
        if phantom_objects is not None:
            for phantom_object in phantom_objects:
                phantom_cols = self.get_phantom_cols_list(
                    source_object, phantom_object
                )
                ids = self.get_prepped_ids_for_phantom_checking(
                    updater_resp, phantom_cols
                )
                unsolvable_phantoms = await self.handle_phantoms(
                    phantom_object, ids
                )
                if unsolvable_phantoms:
                    self.change_unsolvable_phantom_ids(
                        phantom_cols, updater_resp, unsolvable_phantoms
                    )

    async def write_to_db(
        self, updater_resp, prepped_ids_in_batch: str, connection, cursor
    ):
        """Writes the ids in transaction that. The transaction stays open until
        the updated ids are dropped from the meta table and then a commit is attempted.

        Args:
            updater_resp ([type]): [description]
            prepped_ids_in_batch str: [description]
            connection ([type]): [description]
            cursor ([type]): [description]
        """
        obj = self.updates_checker.object_type
        df = updater_resp
        phantom_objects = self.phantom_objects
        await self.loop_through_phantom_relationships(phantom_objects, obj, df)

        self.handle_write_single_table(
            updater_resp,
            prepped_ids_in_batch,
            self.updates_checker.object_type,
            connection,
            cursor,
        )

    async def wrap_up(
        self,
        something_to_write: bool,
        updater_resp,
        prepped_ids_in_batch: str,
        batch_i: int,
    ):
        """Perform wrap up at end of batch: writing of new data,
        droping collecting ids from collection meta tables,
        clearing ids lists and logging

        Args:
            something_to_write (bool): [description]
            updater_resp ([type]): [description]
            prepped_ids_in_batch str: [description]
            batch_i (int): [description]
        """
        # if we have actually prepped ids, go on with the writing stuff:
        if something_to_write:
            connection, cursor = self.start_transaction()
            await self.write_to_db(
                updater_resp, prepped_ids_in_batch, connection, cursor
            )
            # removing records from the for_collection table since they are now collected
            cursor.execute(
                f"""DELETE FROM meta.{self.updates_checker.table_name} 
                WHERE "{self.updates_checker.id_name}" IN {prepped_ids_in_batch}"""
            )
            cloud_log_struct(
                self.logger,
                {
                    "orchestrator": self.__class__.__name__,
                    "updater": self.updater.__class__.__name__,
                    "object_type": self.updates_checker.table_name,
                    "action": "delete_collected_ids_from_for_collection_table",
                    "ids": prepped_ids_in_batch,
                },
            )
            # we have dropped old ids, written new ones, delete ids from _for_collection table
            # and now we can try to commit and close; if we fail at the point of commiting
            # (e.g. some fkey contraint got fucked), catch exception log the issue and then raise
            self.commit_transaction(connection, cursor)
        # even if we didnt have something to write, resetting lists for next batch and log end of batch:
        self.updater.prepped_ids_list = []
        self.updater.no_data_ids_list = []
        cloud_log_text(self.logger, f"End  of batch {batch_i}")

    def start_transaction(self):
        """Init a transaction by deferring constraints and return cursos and connection

        Returns:
            [type]: [description]
        """
        connection = self.cnx_prod.raw_connection()
        cursor = connection.cursor()
        cursor.execute("SET CONSTRAINTS ALL DEFERRED;")
        return connection, cursor

    def set_collection_status(self, status):
        """Some orchestrators may need it such as MatchObjectsOrchestrator,
        to signal that there are games for prepping for the AdvancedStatsPrepper

        Args:
            status ([type]): [description]
        """
        pass

    @staticmethod
    def get_id_name_from_object_name(object_name) -> str:
        if object_name.endswith("es"):  # TODO put this in a func
            return object_name[:-2] + "Id"
        elif object_name.endswith("s"):
            return object_name[:-1] + "Id"
        else:
            raise ValueError("Wrong phantom object name")

    async def handle_phantoms(
        self, phantom_object: str, collected_ids: List[int]
    ) -> List[int]:
        objects_map = {
            "matches": MatchesUpdater,
            "players": PlayersUpdater,
            "teams": TeamsUpdater,
        }
        phantom_id = self.get_id_name_from_object_name(phantom_object)
        phantoms = self.check_phantoms(
            phantom_object, phantom_id, collected_ids
        )
        phantomes_checker = UpdatesChecker(
            table_name=f"{phantom_object}_for_collection",
            object_type=phantom_object,
            id_name=phantom_id,
            from_scratch=self.from_scratch,
            custom_ids_list=phantoms,
        )
        phantomes_updater = objects_map[phantom_object]()
        # this is required because if we have reference to a phantom player, the player can have a phantom team:
        second_phantom_objects = (
            ["teams"]
            if self.updates_checker.object_type in {"matches", "transfers"}
            else None
        )
        phantoms_orc = Orchestrator(
            batch_size=2000,
            updates_checker=phantomes_checker,
            updater=phantomes_updater,
            from_scratch=self.from_scratch,
            phantom_objects=second_phantom_objects,
        )
        await phantoms_orc.loop()
        # check second time and if we still have those, this means that ids point towards an non-existing object
        # because fuck wyscout, e.g. player has currentTeamId of team that returns 404 from ws api
        # so we then we make the current teamId -1 until they get their shit together
        return self.check_phantoms(phantom_object, phantom_id, collected_ids)

    async def setup_collection(self):
        """Set active collection status and generate the collection list.
        Function is async because this the case for TransfersOrchestrator because
        of phantom handling in the setup"""
        self.set_collection_status(True)
        self.setup_empty_ids_table()
        self.get_objects_for_collection()

    def check_phantoms(
        self, object: str, id_name: str, collected_ids: List[int]
    ) -> list:
        existing_objects = set(
            fast_read_sql(
                f'select "{id_name}" from wyscout.{object}', self.cnx_prod
            )[id_name]
        )
        phantoms = [x for x in collected_ids if x not in existing_objects]
        if phantoms:
            cloud_log_struct(
                self.logger,
                {
                    "action": "phantom objects detected",
                    "child_object": self.updater.__class__.__name__,
                    "parent_object": object,
                    "ids": phantoms,
                },
            )
        return phantoms

    def wrap_up_all_batches(self):
        """Dedupe empty data table and set collection status to False"""
        # dedupe the empty data table and delete empty ids from for_collection table
        #  after all batches are done. This is done at the end again so that
        # we dont have to loop through empty ids when start with the refreshes:
        dedupe_table(
            self.cnx_prod,
            self.empty_data_table,
            "meta",
            [self.updates_checker.id_name],
        )
        if self.from_scratch:
            self.drop_empty_objects_from_collection()
        self.set_collection_status(False)

    async def loop(self):
        """Organise the entire collection, writing, logging, deleting etc."""
        await self.setup_collection()
        if len(self.collection_list) == 0:
            cloud_log_text(self.logger, "No new ids to update")
            return
        for i in range(0, len(self.collection_list), self.batch_size):
            batch = self.set_batch_for_collection(i)
            updater_resp = await self.updater.loop()
            # regardless of whether we were successful or not, if there are empty data dfs we should write those;
            # this doesnt need to be in the transaction
            # and we have already rolled it back anyways; note that is applicable
            # only if collecting from scratch, if are collecting changed objects, this shouldnt be of concern:
            self.handle_empty_ids()

            prepped_ids_in_batch = (
                None
                if updater_resp is None
                else self.get_prepped_ids_in_batch(batch, i)
            )
            # if we have something collected and prepped, we go to the writing operations
            something_to_write = prepped_ids_in_batch is not None
            await self.wrap_up(
                something_to_write, updater_resp, prepped_ids_in_batch, i
            )
        self.wrap_up_all_batches()
