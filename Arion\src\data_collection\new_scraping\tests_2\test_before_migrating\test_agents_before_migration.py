import unittest
import pandas as pd
from sqlalchemy.engine import create_engine

from src.helper_funcs import fast_read_sql


class TestAgentBeforeMigration(unittest.TestCase):
    engine = create_engine(
        "***********************************************************/wyscout_raw_production"
    )
    with engine.begin() as con:
        df = fast_read_sql(
            """Select * from meta_scraping.raw_agent_history_table""",
            engine,
        )

    def test_player_ids(self):
        player_ids = [i for i in self.df["playerId"] if pd.isnull(i)]
        self.assertEqual(
            0,
            len(player_ids),
            f"There are {len(player_ids)} player/s with NULL id/s",
        )
