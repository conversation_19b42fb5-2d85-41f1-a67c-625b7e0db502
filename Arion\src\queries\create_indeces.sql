-- player_match_positions
CREATE INDEX IF NOT EXISTS idx_positions_player ON player_match_positions("playerId");

CREATE INDEX IF NOT EXISTS idx_positions_match ON player_match_positions("matchId");

CREATE INDEX IF NOT EXISTS idx_positions_code ON player_match_positions("code");

CREATE INDEX IF NOT EXISTS idx_positions_percent ON player_match_positions("percent");

-- derived_advanced_stats
CREATE INDEX IF NOT EXISTS idx_derived_player ON derived_advanced_stats("playerId");

CREATE INDEX IF NOT EXISTS idx_derived_match ON derived_advanced_stats("matchId");

CREATE INDEX IF NOT EXISTS idx_derived_clearances ON derived_advanced_stats("clearances");

-- advanced_stats
CREATE INDEX IF NOT EXISTS idx_adv_date ON advanced_stats_total("date");

CREATE INDEX IF NOT EXISTS idx_adv_player ON advanced_stats_total("playerId");

CREATE INDEX IF NOT EXISTS idx_adv_match ON advanced_stats_total("matchId");

CREATE INDEX IF NOT EXISTS idx_adv_comp ON advanced_stats_total("competitionId");

CREATE INDEX IF NOT EXISTS idx_adv_minutes ON advanced_stats_total("minutesTagged");

-- advanced_stats percentage
CREATE INDEX IF NOT EXISTS idx_adv_pct_player ON advanced_stats_percentage("playerId");

CREATE INDEX IF NOT EXISTS idx_adv_pct_match ON advanced_stats_percentage("matchId");

-- advanced_stats average
CREATE INDEX IF NOT EXISTS idx_adv_avg_player ON advanced_stats_average("playerId");

CREATE INDEX IF NOT EXISTS idx_adv_avg_match ON advanced_stats_average("matchId");

-- formations
CREATE INDEX IF NOT EXISTS idx_formations_player ON formation_events("playerId");

CREATE INDEX IF NOT EXISTS idx_formations_match ON formation_events("matchId");

--all_players
CREATE INDEX IF NOT EXISTS idx_players_player ON all_players("playerId");

CREATE INDEX IF NOT EXISTS idx_players_team ON all_players("currentTeamId");

CREATE INDEX   IF NOT EXISTS idx_players_birth ON all_players("birthDate");

CREATE INDEX  IF NOT EXISTS idx_players_birth_area ON all_players("birthArea_name");

CREATE OR REPLACE FUNCTION public.f_unaccent(text)
  RETURNS text AS
$func$
SELECT public.unaccent('public.unaccent', $1)  -- schema-qualify function and dictionary
$func$  LANGUAGE sql IMMUTABLE PARALLEL SAFE STRICT;

CREATE INDEX IF NOT EXISTS idx_trgm_players_full_name ON all_players 
USING GIST ((f_unaccent("firstName") || ' ' || f_unaccent("lastName")) gist_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_trgm_players_last_name ON all_players 
USING GIST (f_unaccent("lastName") gist_trgm_ops);


--competitions
CREATE INDEX IF NOT EXISTS idx_competitions_competition ON competitions("competitionId");

CREATE INDEX IF NOT EXISTS idx_competitions_country ON competitions("area_name");

CREATE INDEX IF NOT EXISTS idx_competitions_division ON competitions("divisionLevel");

CREATE INDEX IF NOT EXISTS idx_competitions_category ON competitions("category");

CREATE INDEX IF NOT EXISTS idx_competitions_type ON competitions("type");

CREATE INDEX IF NOT EXISTS idx_competitions_gender ON competitions("gender");

--seasons
CREATE INDEX IF NOT EXISTS idx_seasons_season ON seasons("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_start_date ON seasons("seasonstartDate");

CREATE INDEX IF NOT EXISTS idx_seasons_format ON seasons("competition_format");

CREATE INDEX IF NOT EXISTS idx_seasons_competition ON seasons("seasoncompetitionId");

CREATE INDEX IF NOT EXISTS idx_seasons_area_name ON seasons("competition_area_name");


--seasons_teams
CREATE INDEX IF NOT EXISTS idx_seasons_teams_season ON seasons_teams("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_teams_team ON seasons_teams("teamId");

--seasons_players
CREATE INDEX IF NOT EXISTS idx_seasons_players_season ON seasons_players("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_players_player ON seasons_players("playerId");

--seasons_matches
CREATE INDEX IF NOT EXISTS idx_seasons_matches_season ON seasons_matches("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_matches_match ON seasons_matches("matchId");

--transfermarkt_data
CREATE INDEX IF NOT EXISTS idx_tm_player ON transfermarkt_data("playerId");

CREATE INDEX IF NOT EXISTS idx_tm_agent ON transfermarkt_data("agent");

--tm_injuries
CREATE INDEX IF NOT EXISTS idx_tm_injuries_player ON transfermarkt_injuries("playerId");

--team_matches_lineups
CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_player ON team_matches_lineups("playerId");

CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_match ON team_matches_lineups("matchId");

CREATE INDEX IF NOT EXISTS idx_team_matches_lineups_team ON team_matches_lineups("teamId");

--leagues_standings
CREATE INDEX IF NOT EXISTS ids_leagues_standings_season ON leagues_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_team ON leagues_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_placement ON leagues_standings("placement");

--cups_standings
CREATE INDEX IF NOT EXISTS ids_cups_standings_season ON cups_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_team ON cups_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_round_name ON cups_standings("roundName");

--player_roles
CREATE INDEX IF NOT EXISTS idx_player_roles_player ON player_roles("playerId");

--player_match_roles
CREATE INDEX IF NOT EXISTS idx_player_match_roles_player ON player_match_roles("playerId");

CREATE INDEX IF NOT EXISTS idx_player_match_roles_match ON player_match_roles("matchId");

--transfers 
CREATE INDEX IF NOT EXISTS idx_transfers_player ON transfers("playerId");

CREATE INDEX IF NOT EXISTS idx_transfers_type ON transfers("type");


--scaling_attributes

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_player ON scaling_attributes("playerId");

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_match ON scaling_attributes("matchId");


