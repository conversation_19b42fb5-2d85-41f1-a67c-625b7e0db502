import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
    "dag_id": "refresh_player_injuries_v2_dag",
    "start_date": datetime(2021, 12, 7),
    "schedule_interval": timedelta(days=14),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    populate_progresss_table = PostgresOperator(
        task_id = "populate_progress_table",
        sql = """INSERT INTO meta_scraping.injuries_progress_table SELECT * FROM transfermarkt.tm_to_ws_ids""",
        database = "wyscout_raw_production"
    )

    truncate_raw_tables = BashOperator(
        task_id="truncate_raw_tables",
                bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/truncate_raw_injuries_tables.py
                           """,
    )

    rescrape_injuries = BashOperator(
        task_id = "rescrape_injuries",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            export REFRESH="{{params.refresh}}"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/rescrape_player_injuries.py
                           """,
    )

    save_cleaned_injuries = BashOperator(
        task_id="save_cleaned_injuries",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 src/data_collection/new_scraping/cleaning/save_cleaned_injuries.py
                           """,
    )

    check_last_week_data = PostgresOperator(
        task_id = "check_last_week_data",
        database = "wyscout_raw_production",
        sql=open(workdir + "/src/queries/check_last_week_injuries.sql").read(),
    )

    check_if_injuries_are_ready_for_migration = BashOperator(
        task_id = "check_if_injuries_are_ready_for_migration",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          arionflow_venv/bin/python3 -m unittest src/data_collection/new_scraping/tests_2/test_before_migrating/test_injuries_before_migration.py
                           """,
    )


(
    populate_progresss_table
    >> truncate_raw_tables
    >> rescrape_injuries
    >> [check_if_injuries_are_ready_for_migration]
)

(
    check_if_injuries_are_ready_for_migration
    >> check_last_week_data
    >> save_cleaned_injuries
)