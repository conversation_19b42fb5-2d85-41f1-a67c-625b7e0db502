-- First delete rows where we have the same thing matched twice, 
-- here we keep one row because we know the match is correct
WITH
cte
AS
(
SELECT ctid,
       row_number() OVER (PARTITION BY "playerId",
                                       "tm_player_id"
                          ) rn
       FROM meta_scraping.raw_tm_to_ws_ids
)
DELETE FROM meta_scraping.raw_tm_to_ws_ids mtr
       USING cte
       WHERE cte.rn > 1
             AND cte.ctid = mtr.ctid;

-- Second delete rows where we have a player matched two times to 2 different guys, 
-- here we drop all rows because we can't know which one is correct:
WITH dupes
AS(
    SELECT "playerId"
    FROM meta_scraping.raw_tm_to_ws_ids
    GROUP BY 
    "playerId"
    HAVING COUNT("playerId")>1

)
DELETE FROM meta_scraping.raw_tm_to_ws_ids
    USING dupes
    WHERE meta_scraping.raw_tm_to_ws_ids."playerId" = dupes."playerId";

WITH dupes
AS(
    SELECT "tm_player_id"
    FROM meta_scraping.raw_tm_to_ws_ids
    GROUP BY 
    "tm_player_id"
    HAVING COUNT("tm_player_id")>1

)
DELETE FROM meta_scraping.raw_tm_to_ws_ids
    USING dupes
    WHERE meta_scraping.raw_tm_to_ws_ids."tm_player_id" = dupes."tm_player_id";