import aiohttp
import asyncio
import pandas as pd
import numpy as np
from typing import Union, List

from src.data_collection.wyscout.v2.Updater import Updater


class CompetitionsUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/competitions"
        self.object_type = self.base_url.split("/")[-1]
        self.if_exists = "replace"
        self.id_name = "competitionId"
        self.create_unknown_records_flag = (
            True  # applies to all tables that are being dropped and re-created
        )
        self.write_in_loop = True

    async def collect(self) -> Union[List[dict], None]:
        """Collects a batch of ids from Wyscout endpoint

        Returns:
            list: A list of responses
        """
        tasks = []
        async with aiohttp.ClientSession() as session:
            for code in self.collection_list:
                await asyncio.sleep(self.sleep_time)
                # putting zero in since this will not break the logic of the default fetch_result -
                # the base_url has no _ID in it so the replace will return the same string
                tasks.append(
                    asyncio.create_task(
                        self.process_response(session, 0, {"areaId": code})
                    )
                )
            # collect results form tasks:
            results = await asyncio.gather(*tasks)
            # filter for None responses (i.e. empty/broken responses)
            results = list(filter(lambda x: x is not None, results))
            if results:
                return list(np.concatenate(results))
            else:
                return None

    def prep(self, results: Union[List[dict], None]) -> pd.DataFrame:
        df = pd.DataFrame(results)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df = df.rename(
            columns={
                "wyId": "competitionId",
            }
        )
        # fixing US and Mexico division before writing to db:
        df["divisionLevel"] = np.where(
            (df["area_name"].isin(["United States", "Mexico"]))
            & (~df["competitionId"].isin([617, 869])),
            0,
            df["divisionLevel"],
        )
        return df
