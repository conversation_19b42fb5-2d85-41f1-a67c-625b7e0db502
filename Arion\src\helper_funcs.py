import glob
import tempfile
from datetime import datetime
import itertools
import math
import requests
import json
from io import StringIO
import random
import string
import csv
import traceback
from typing import Tuple, List, Dict, Iterable
from dataclasses import dataclass

import yaml
import numpy as np
import pandas as pd
from scipy import stats
from statsmodels.stats.proportion import binom_test
from sqlalchemy import engine
from statsmodels.distributions.empirical_distribution import ECDF
from google.cloud import logging
from google.cloud.logging_v2.logger import Logger
from google.cloud import storage

from tenacity import retry, wait_fixed, stop_after_attempt

from settings import WYSCOUT_USR, WYSCOUT_PASS, USERS, SERVICE_ACCS


def read_config(config_file="config.yml"):
    with open(config_file, "r") as yamlfile:
        return yaml.load(yamlfile, Loader=yaml.Loader)


def compile_query(query):
    try:
        query_compiled = query.compile()
        params = query_compiled.params
        query_s = f"""{str(query_compiled)}"""
        for k, v in params.items():
            replace_str = f""" '{v}'  """ if isinstance(v, str) else str(v)
        return query_s.replace(":" + str(k), replace_str)
    except:
        raise Exception(
            "SQL read is neither string, nor alchemyselectable and thus the"
            " fast sql read failed"
        )


def fast_read_sql(query, cnx):
    query_s = compile_query(query) if not isinstance(query, str) else query
    with tempfile.TemporaryFile() as tmpfile:
        copy_sql = f"COPY ({query_s}) TO STDOUT WITH CSV HEADER"
        conn = cnx.raw_connection()
        cur = conn.cursor()
        cur.copy_expert(copy_sql, tmpfile)
        tmpfile.seek(0)
        return pd.read_csv(tmpfile)


def load_config(config_file):
    with open(config_file, "r") as stream:
        try:
            return yaml.safe_load(stream)
        except yaml.YAMLError as exc:
            print(exc)


def chunked_iterable(iterable, size):
    """Turns an iterable into iterable chunks of given size"""
    it = iter(iterable)
    while True:
        chunk = tuple(itertools.islice(it, size))
        if not chunk:
            break
        yield chunk


def pvalue_population(x):
    # Calculates p-values of the data assuming no sampling
    # x is a numpy array of numeric values
    # x = stats.zscore(x)
    x = (x - x.mean(axis=0)) / (x.std(axis=0))
    return stats.norm.sf(-x)


def empirical_cdf(x, min_v=None, max_v=None):
    """Calculates empirical p-values of the data assuming no sampling
    No distribution assumption
    x: Numpy array of numeric values
    """
    x = np.array(x)
    if min_v is not None:
        x = np.append(x, [min_v])

    if max_v is not None:
        x = np.append(x, [max_v])

    ecdf = ECDF(x, side="left")
    return ecdf(x)


def binomial_test(x, baseline_p=None, alt="smaller"):
    """
    Performs binomial test for proportions.
    Parameters:
    x (array of integers): Holds the success frequency and the attempt frequency.
    Success frequency must be less than or equal to attempt frequency.
    baseline_p (float): Baseline ratio to compare to. Must be in range [0,1]
    alt (string): Alternative hypothesis. Accepted values ['two-sided', 'smaller', 'larger']

    Details:
    This is a one-sided hypothesis test that gives the likelihood of the ratio being greater than baseline_p.
    baseline_p is assumed to be 0.5 unless explicitly provided.
    """
    if baseline_p is None:
        baseline_p = 0.5

    return binom_test(x[0], x[1], prop=baseline_p, alternative=alt)


def calculate_odds(p):
    """
    Takes a probability value and turns it into odds.
    Parameters:
    p (float): Probability that an event is positive (i.e. The likelihood of player being greater than the rest)

    Returns:
    odd (float): p/(1-p)
    """
    if p > 0:
        odd = p / (1 - p)
    else:
        raise ValueError("Probability must be non-zero")

    return odd


def log1p_base(x, base=10):
    if x == 0:
        x = 1
    return math.log(x, base)


def pvalue_bootstrap_sample(x):
    # Calculates p-values of the data assuming no sampling
    # x is a numpy array of numeric values
    x = (x - x.mean(axis=0)) / (x.std(axis=0) / np.sqrt(x.size))
    return stats.norm.sf(-x)


def smooth_prob(p, c=0.9):
    """Smooths the probability values without changing their order
    Useful when probability distribution needs to move further towards a
    more condensed one (around 0.5)
    Args:
        p ([type]): [description]
        c (float, optional): [description]. Defaults to 0.9.

    Returns:
        [type]: [description]"""
    return c * p + (1 - c) * ((math.exp(p) / (math.exp(p) + math.exp(1 - p))))


def get_derivation_features(config, position, xp_vars=True):
    pos_features = list(config["positions_weights"][position].keys())
    derivation_features_subset = {
        feat: config["advanced_variables_definitions"][feat] for feat in pos_features
    }
    denoms = [derivation_features_subset[x]["denominator"] for x in pos_features]
    numers = [derivation_features_subset[x]["numerator"] for x in pos_features]
    xp_vars = (
        [derivation_features_subset[x]["exp_var"] for x in pos_features]
        if xp_vars
        else []
    )
    features_list = denoms + numers + xp_vars
    flat_feat_list = []
    for x in features_list:
        if isinstance(x, list):
            flat_feat_list += [var for var in x]
        else:
            flat_feat_list.append(x)
    return list({x for x in flat_feat_list if str(x) != "nan" and x is not None})


def get_progression(var, timedeltas=None):
    x = timedeltas if timedeltas is not None else np.arange(len(var))
    y = np.array(var)
    return np.polyfit(x, y, 1)[0]


def get_consistency(var, roll_window, detrend=False):
    raw = var.copy()
    var = var.dropna().rolling(window=roll_window, center=True).mean().dropna()
    if not detrend:
        return np.std(var)
    trend = get_progression(var)
    detrended = raw - np.arange(len(raw)) * trend
    return np.std(detrended)


def get_time_stamp(ms=False):
    now = datetime.now()
    if not ms:
        return f"""{now.year}{now.month}{now.day
        }_{now.hour}{now.minute}"""
    return f"""{now.year}{now.month}{now.day
        }_{now.hour}{now.minute}{now.second}{now.microsecond}"""


def concat_batch_files(path, extension=".csv", encoding=None):
    if not path.endswith("/"):
        path = path + "/"
    files = [x for x in glob.glob(f"{path}*{extension}")]
    df_list = [pd.read_csv(x, encoding=encoding) for x in files]
    return pd.concat(df_list, sort=True, axis=0).reset_index(drop=True)


def min_max_scale(x, min_value, max_value):
    return min_value + (max_value - min_value) * (x - np.min(x)) / (
        np.max(x) - np.min(x)
    )


def get_wyscout_response(url, params=None):
    resp = requests.get(url, auth=(WYSCOUT_USR, WYSCOUT_PASS), params=params)
    return json.loads(resp.text)


def get_sql_array_str(x: Iterable) -> str:
    return str(tuple(x)).replace(",)", ")")


def check_existance(cnx: engine.base.Engine, table_name: str, schema: str) -> bool:
    return cnx.execute(
        f"""SELECT EXISTS(
        SELECT * 
        FROM information_schema.tables 
        WHERE 
        table_schema = '{schema}' AND 
        table_name = '{table_name}'); """
    ).first()[0]


def fast_write_sql(
    df: pd.DataFrame,
    table_name: str,
    cnx: engine.base.Engine,
    schema: str = "public",
    if_exists: str = "replace",
    sep: str = "\x01",
    encoding: str = "utf-16",
    dtype: dict = None,
    grant: bool = True,
    transaction: bool = False,
    cursor=None,
    connection=None,
):
    def create_random_table_name() -> str:
        """This thing is done so that we can use DBAPI cursor as a part of transaction
        but still utilize pandas/sqlalchemy dtyping, idea is to create a temp table
        through the normal connection, because the cursor may hold a lock on the table we
        want to create, then we create empty table with proper dtypes, go back to cursor,
        drop original table, and create it new with the dtypes of the temp table and then
        drop the temp table as well
        Returns:
            str: name of temp table
        """
        while True:
            letters = string.ascii_lowercase
            temp_name = "".join(random.choice(letters) for _ in range(20))
            random_existance = check_existance(cnx, temp_name, schema)
            if not random_existance:
                break
        return temp_name

    # Check if table exists:
    existance = check_existance(cnx, table_name, schema)

    if existance and if_exists == "fail":
        raise ValueError(f"Table {table_name} already exists")
    # if table doesnt exists or we overwrite, write first row:
    if (existance and if_exists == "replace") or not existance:
        if transaction:
            temp_name = create_random_table_name()
        df[:0].to_sql(
            table_name if not transaction else temp_name,
            cnx,
            if_exists=if_exists,
            index=False,
            schema=schema,
            dtype=dtype,
        )
        if transaction:
            cursor.execute(f"""DROP TABLE IF EXISTS {schema}.{table_name}  """)
            cursor.execute(
                f"""CREATE TABLE {schema}.{table_name} (LIKE {schema}.{temp_name} INCLUDING ALL)
 """
            )
            cursor.execute(f"""DROP TABLE {schema}.{temp_name} """)

    # Prep:
    # aligning columns if we are appending to an existing table:
    if existance and if_exists == "append":
        existing_df = pd.read_sql(f"select * from {schema}.{table_name} limit 1", cnx)
        df = df[existing_df.columns]
    try:
        output = StringIO()
        df.to_csv(
            output,
            header=False,
            encoding=encoding,
            index=False,
            sep=sep,
            quoting=csv.QUOTE_NONE,
            escapechar="\\",
        )

        output.seek(0)
        # Insert:
        schema_tablename = f"{schema}.{table_name}"
        if not transaction:
            connection = cnx.raw_connection()
            cursor = connection.cursor()
            cursor.copy_from(output, schema_tablename, sep=sep, null="")
            connection.commit()
            cursor.close()
        else:
            cursor.copy_from(output, schema_tablename, sep=sep, null="")
            # alternatively we may want to use connection if we doing the fast write as part of bigger transaction
            # note we do not commit since we may want to rollback if something in the transaction later fails
        # we should be granting only if creating a table that didnt exist before:
        if grant and (not existance or if_exists == "replace"):
            executor = cnx if not transaction else cursor
            executor.execute(
                f"""GRANT ALL ON TABLE {schema}.{table_name} TO {','.join(USERS)} WITH GRANT OPTION;
                            GRANT SELECT ON TABLE {schema}.{table_name} TO {','.join(SERVICE_ACCS)}"""
            )
    # if we fail somewhere, and the table didnt exist before we start,
    # clean by dropping the table and then re-raising:
    except:
        if not existance:
            cnx.execute(f"DROP TABLE IF EXISTS {schema}.{table_name}; ")
        raise Exception(traceback.format_exc())


def partial_func(f):
    return f()


def convert_float_to_eur(x):
    if x is not None:
        currency = "{:,.0f}€".format(x)
        return currency
    else:
        return x


def dedupe_table(
    cnx: engine.base.Engine, table_name: str, schema: str, dedupe_cols: list
):
    partition = ", ".join(f""" "{x}" """ for x in dedupe_cols)
    query = f"""
        WITH
        cte
        AS
        (
        SELECT ctid,
            row_number() OVER (PARTITION BY {partition} ) rn
            FROM {schema}.{table_name}
        )
        DELETE FROM {schema}.{table_name}
            USING cte
            WHERE cte.rn > 1
                    AND cte.ctid = {schema}.{table_name}.ctid;
        COMMIT;
        """
    cnx.execute(query)


def get_cloud_logger(logger_name: str = "data-collection"):
    logging_client = logging.Client(project="footballanalytics")
    return logging_client.logger(logger_name)


@retry(wait=wait_fixed(1), stop=stop_after_attempt(2))
def cloud_log_struct(logger: Logger, msg: dict):
    logger.log_struct(msg)


@retry(wait=wait_fixed(1), stop=stop_after_attempt(2))
def cloud_log_text(logger: Logger, msg: str):
    logger.log_text(msg)


def get_final_file_from_gcloud(folder_name):
    storage_client = storage.Client(project="footballanalytics")
    file_names = []
    creation_date = []
    # folder_name = 'package_files/'
    for item in storage_client.list_blobs("player-valuation", prefix=folder_name):
        file_names.append(item.name)
        creation_date.append(item.time_created)

    file_metadata = pd.DataFrame({"file_names": file_names, "file_date": creation_date})
    file_metadata = file_metadata[file_metadata["file_names"] != folder_name]
    file_metadata["file_names"] = file_metadata["file_names"].str.replace(
        folder_name, ""
    )
    file_metadata = file_metadata.sort_values(
        "file_date", ascending=False
    ).reset_index()
    return file_metadata["file_names"][0]


def download_blob(bucket_name, source_blob_name, destination_file_name):
    """Downloads a blob from the bucket."""
    # bucket_name = "your-bucket-name"
    # source_blob_name = "storage-object-name"
    # destination_file_name = "local/path/to/file"

    storage_client = storage.Client(project="footballanalytics")

    bucket = storage_client.bucket(bucket_name)

    # Construct a client side representation of a blob.
    # Note `Bucket.blob` differs from `Bucket.get_blob` as it doesn't retrieve
    # any content from Google Cloud Storage. As we don't need additional data,
    # using `Bucket.blob` is preferred here.
    blob = bucket.blob(source_blob_name)
    blob.download_to_filename(destination_file_name)

    print("Blob {} downloaded to {}.".format(source_blob_name, destination_file_name))


def upload_blob(bucket_name, source_file_name, destination_blob_name):
    """Uploads a file to the bucket."""
    # The ID of your GCS bucket
    # bucket_name = "your-bucket-name"
    # The path to your file to upload
    # source_file_name = "local/path/to/file"
    # The ID of your GCS object
    # destination_blob_name = "storage-object-name"

    storage_client = storage.Client(project="footballanalytics")
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)

    blob.upload_from_filename(source_file_name)

    print("File {} uploaded to {}.".format(source_file_name, destination_blob_name))


def convert_elo_to_win_prob(elo1: float, elo2: float) -> Tuple[float, float, float]:

    # First team
    dr1 = elo1 - elo2 + 100
    exponent1 = -dr1 / 400
    denominator1 = 10 ** exponent1 + 1
    wp1 = 1 / denominator1

    # Second Team
    dr2 = elo2 - elo1 + 100
    exponent2 = -dr2 / 400
    denominator2 = 10 ** exponent2 + 1
    wp2 = 1 / denominator2

    # Final probabilities
    dp = wp1 + wp2 - 1

    # total_prob = wp1 + dp + wp2
    wp11 = (1 - dp) * wp1 / (wp1 + wp2)
    wp22 = (1 - dp) * wp2 / (wp1 + wp2)
    return wp11, dp, wp22


def expandgrid(*itrs) -> Dict[str, List]:
    product = list(itertools.product(*itrs))
    return {"Var{}".format(i + 1): [x[i] for x in product] for i in range(len(itrs))}


@dataclass
class AuthProvider:
    usr: str
    pwd: str


def rename_pos(x):
    dd = {
        "fb": "Forward Back",
        "wb": "Wing Back",
        "agg_cb": "Aggresive Central Back",
        "stable_cb": "Stable Central Back",
        "attacking_winger": "Attacking Winger",
        "classic_winger": "Classic Winger",
        "holding_mid": "Holding Midfielder",
        "speedy_striker": "Speedy Striker",
        "tank": "Tank",
        "cam": "Central Attacking Midfielder",
        "linkup_striker": "Linkup Striker",
        "cdm": "Central Defensive Midfield",
        "regista": "Regista",
    }

    try:
        return dd[x]
    except:
        return x
