source('src/data_collection/wyscout/00_libs.R')
library(PlayerRatings)
library(lubridate)

con = make_connection(con_db = 'PRODUCTION_DB')
accounts_to_grant = read_json('postgres_accounts.json')
# accounts_to_grant = data.frame(accounts_to_grant)

comp_qry = read_file('src/queries/new_queries/get_base_competition_for_elo.sql')
match_qry = read_file('src/queries/new_queries/get_team_matches_for_elo.sql')
start_date = '2014-07-01'
end_date = as.character(Sys.Date())
config = configr::read.config('config.yml')
root_n = 1 / 3
min_rating = 1500
max_rating = 2500
unknown_raing = 1200
glicko_dev = 150
elo_k = 32
window_size = 5 # Elo smoothing window size

teams = dbGetQuery(con, 'select distinct "teamId" from wyscout.seasons_teams')


comp_df = get_relevant_competitions(comp_qry, con, start_date, end_date)
match_df = get_matches(match_qry, con, start_date, end_date)

comp_df = comp_df %>%
  filter(teamId %in% teams$teamId) %>%
  mutate(date = as.Date(date)) %>%
  group_by(teamId) %>%
  filter(date == min(date, na.rm = T))

match_df = match_df %>%
  filter(home_id %in% teams$teamId | away_id %in% teams$teamId)

comp_df = data.frame(comp_df)
match_df = data.frame(match_df)



##### ELO TEST
# set.seed(42)
# winning_tid = sample(comp_df$teamId[!is.na(comp_df$init_value)], 100)
# winning_tid = c(winning_tid, 1612)
# winning_tid = unique(winning_tid)
# compare_init_max = NULL
# for(alg in c('elo', 'glicko')){
#   for(prb in seq(from = 0.7, to = 1, by = 0.1)){
#     for(tid in winning_tid){
#       hw = rbinom(sum(match_df$home_id==tid), 1, prb)
#       match_df2 = match_df %>%
#         mutate(Score = ifelse(home_id==tid, hw,
#                               ifelse(away_id==tid, 1-hw, Score)))
#       ratings_test = get_ratings(comp_df, match_df2, unknown_raing, elo_k, glicko_dev, alg, get_history = F)
#       ratings_test = ratings_test$ratings
#       team_init_max = data.frame(teamId = tid, init=comp_df$init_value[comp_df$teamId==tid],
#                                  final = ratings_test$Rating[ratings_test$Player==tid], win_prob = prb,
#                                  algorithm = alg, stringsAsFactors = F)
#       compare_init_max = rbind(compare_init_max, team_init_max)
#     }
#   }
# }

# Minimum rating - Hard limit 0, soft limit around 600
# Maximum rating - Hard limit none, soft limit 3200




# match_df = match_df[!is.na(match_df$home_id) &
#                       !is.na(match_df$away_id),]
match_df$home_id[is.na(match_df$home_id)] = -1
match_df$away_id[is.na(match_df$away_id)] = -1
match_df$period = match_df$period - min(match_df$period, na.rm = T) + 1


comp_df2 = get_initialization(comp_df, min_rating, max_rating, root_n)
comp_df2 = comp_df2[!is.na(comp_df2$init_value), ]

ratings = get_ratings(comp_df2,
                      match_df,
                      unknown_raing,
                      elo_k,
                      glicko_dev,
                      'elo',
                      get_history = T)
hist_array = ratings$history
hist_array = hist_array[, , 1]
period = colnames(hist_array)
teamId = rownames(hist_array)

hist_array = data.frame(hist_array)
colnames(hist_array) = paste('period', period, sep = '_')
hist_array$teamId = as.integer(teamId)

# match_df = match_df
match_df_long = reshape2::melt(match_df[c('date',
                                          'matchId',
                                          'home_id',
                                          'away_id',
                                          'year',
                                          'week_of_year',
                                          'period')],
                               id.vars = setdiff(c('date',
                                                   'matchId',
                                                   'home_id',
                                                   'away_id',
                                                   'year',
                                                   'week_of_year',
                                                   'period'), c('home_id', 'away_id')),
                               variable.name = 'side')

match_df_long = match_df_long %>%
  mutate(side = gsub('_id', '', as.character(side))) %>%
  rename(teamId = value)

melted_df = reshape2::melt(hist_array, id.vars = 'teamId', variable.name = 'period')
melted_df$period = as.integer(gsub('period_', '', as.character(melted_df$period)))
melted_df = melted_df %>%
  rename(rating = value) %>%
  left_join(match_df_long)
melted_df = melted_df %>%
  filter(!is.na(matchId))


# Get column names from team_match_rating
cnames = colnames(dbGetQuery(con, 'select * from derived_tables.team_match_rating limit 1'))
melted_df[setdiff(cnames, colnames(melted_df))] = NA

melted_df = melted_df %>%
  group_by(teamId) %>%
  arrange(period) %>%
  mutate(smoothed_rating = get_smoothed_values(rating, window_size = window_size)) %>%
  ungroup() %>%
  data.frame(stringsAsFactors = F)

melted_df = melted_df[!duplicated(melted_df[c('matchId', 'teamId')]),]
    


owt = T
dbDisconnect(con)


con <- make_connection('PRODUCTION_DB')
# con2 = make_connection('DEVELOPMENT_DB')
dbExecute(con, "SET search_path = derived_tables")
dbExecute(con, 'delete from derived_tables.team_match_rating')
dbWriteTable(
  con,
  c('derived_tables', 'team_match_rating'),
  melted_df,
  append = T,
  row.names = F
)
# dbWriteTable(con2, 'derived_tables.team_match_rating', melted_df, overwrite = T, row.names = F)

table_qry = 'GRANT ALL PRIVILEGES ON TABLE derived_tables.tb_name TO username with grant option;'

for (usr in accounts_to_grant[['users']]) {
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'team_match_rating', tbl_qry))
  # dbGetQuery(con2, gsub('tb_name', 'team_match_rating',tbl_qry))
}

table_qry = 'GRANT SELECT ON TABLE derived_tables.tb_name TO username with grant option;'

for (usr in accounts_to_grant[['service_accs']]) {
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'team_match_rating', tbl_qry))
  # dbGetQuery(con2, gsub('tb_name', 'team_match_rating',tbl_qry))
}
