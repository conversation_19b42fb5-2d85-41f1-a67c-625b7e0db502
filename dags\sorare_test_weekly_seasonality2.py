import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir_sorare_test as workdir_sorare, config

sys.path.append(workdir_sorare)

dag_params = {
    "dag_id": "sorare_test_weekly_seasonality",
    "start_date": datetime(2022, 2, 7, 14),
    "schedule_interval": None,
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_sorare,
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    sorare_experiment1 = BashOperator(
        task_id="sorare_experiment1",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_backtest_weekend_seasonality.py
                           """,
    )


    sorare_experiment1
