from datetime import datetime
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.model_selection import RandomizedSearchCV
import math 
from scipy import stats 
import joblib
import sys 
sys.path.append('.')

from settings import  postgres_prod_str, postgres_dev_str

def get_timestamp():
    """ A function that returns time in a specific format, convinient
    for file naming. No arguments required, time supplied by shell """

    t = datetime.now()
    t = t.replace(second=0, microsecond=0)
    return str(t).replace(':', '_').replace(' ', '_').replace('-', '')   

def export_search_results(search, report_export_directory):
    """
    :param search:
    :param report_export_directory:
    :return: None, the function only writes a file """

    # derive a name for the exported file
    report_path = report_export_directory + str(get_timestamp()) + '.csv'
    report_path = report_path.replace(':', '_').replace(' ', '_')

    # writes the file
    pd.DataFrame(search.cv_results_).sort_values('rank_test_score').\
        to_csv(report_path)

def export_model(model, model_export_directory):
    """ A function to export a model outside of python for future use
    :param model: sk-learn model object
    :param model_export_directory: directory to be stored
    :return: None, it exports a model in joblib format """

    # design the name
    model_path = model_export_directory+str(get_timestamp())+'.joblib'
    model_path = model_path.replace(':', '_').replace(' ', '_')

    # export using joblib
    joblib.dump(model, model_path)


def main():
    cnx_prod = create_engine(postgres_prod_str)
    df = pd.read_sql('select * from player_speeds where speed> 0 and speed < 40', cnx_prod)

    df['angle'] = (abs(df.y_end - df.y_start) * 0.45) / \
     (abs(df.x_end - df.x_start) * 0.90).apply(lambda x: math.atan(x))

    df['time_elapsed'] = np.where(
            df['matchPeriod'] <= 2,
            ((df['matchPeriod'] - 1) * 2700 + df['eventSec']
            )  / 60, 
            (5400 + (df['matchPeriod'] - 3) * 900 + df['eventSec']
            )  / 60
        ) 

    inp = df[['x_start', 'y_start', 'time_elapsed', 'next_event',
       'prev_event', 'angle']]
    X = pd.get_dummies(inp, columns=['prev_event', 'next_event'])
    y = df['speed']
    X, y = X[df.angle < np.inf], y[df.angle < np.inf]
    fixed_params = {
                'n_estimators': 1000,
                'random_state': 42,
                'verbose': 2,
                'tol': 1e-05,
                'loss': 'ls',
                'subsample': 0.7
            }
    gbm = GradientBoostingRegressor(**fixed_params)
    param_dist_dict = {
                "max_depth": [2, 3, 5],
                "max_features": [2, 3, 5],
                "min_samples_split": stats.randint(500, 10000),
                'min_samples_leaf': np.arange(100, 2000, 100),
                "learning_rate": np.concatenate((np.arange(0.01, 0.2, 0.01), np.arange(0.001, 0.1, 0.005))),
                'n_iter_no_change': np.arange(10, 50, 10),
            }
    random_search = RandomizedSearchCV(gbm,
        param_distributions=param_dist_dict, n_iter=2,
        verbose=2, n_jobs=-1)
    random_search.fit(X, y)
    export_search_results(
            random_search,
            report_export_directory='output/'
        )

    export_model(
            random_search.best_estimator_,
            model_export_directory='output/'
        )
main()