from time import time
from sqlalchemy import create_engine
import pandas as pd
import numpy as np
from fuzzywuzzy import process
from settings import postgres_prod_str


def read_data(db_con):
    ys_query = """SELECT  p."playerId",  p."firstName", p."lastName", p."height", p."weight", p."passportArea_name", 
    p."birthDate", p."currentTeamId",  p."status",
    t."teamId", t."name", t."category", t."type", t."gender"
FROM all_players p, competition_teams t
WHERE p."currentTeamId"=t."teamId" and t."gender"='male' and t."category"='default' and t."type"='club' and p."status"='active'
 """

    # teams_init = pd.read_sql('''SELECT * FROM competition_teams;''', cnx)
    ys_init = pd.read_sql(ys_query, db_con)
    fm_init = pd.read_csv(
        "data/raw/playerProfiles_v1.1.csv",
        encoding="ISO-8859-1",
        error_bad_lines=False,
    )
    return ys_init, fm_init


def date_str_to_year_int(x):
    if (str(x) == "nan") or (str(x) == "None"):
        return np.nan
    else:
        return int(str(x).split("-")[0])


def prep_dfs(ys, fm):
    ys = ys.drop_duplicates(subset=["playerId"])
    ys["birthYear"] = ys["birthDate"].apply(date_str_to_year_int)
    fm["birthYear"] = fm["birthYear"].astype(int)
    fm = fm.rename(
        index=str,
        columns={
            "firstname": "firstName",
            "lastname": "lastName",
            "uniqueId": "fmUniqueId",
        },
    )
    ys = ys.rename(
        index=str,
        columns={"passportArea_name": "nationality", "name": "clubName"},
    )
    ys["fullName"] = ys["firstName"] + ys["lastName"]
    fm["fullName"] = fm["firstName"] + fm["lastName"]
    return ys, fm


def match_players(ys, fm, match_methods, threshold=89, verbose=True):
    start = time()
    fm = fm[fm["fullName"].notnull()]  # we cant match nan players anyways
    team_names_map_dict = {}  # fill fuzzy matched teams here
    # adding a col for matching type so that we can use to decide which potential duplicate to drop -
    # see dedupe and merge func for more info
    merge_df = pd.DataFrame(
        columns=list(ys.columns) + ["matching_type", "fmUniqueId"]
    )  # initiate df to be filled

    print("fuzzy matching teams")
    all_fm_teams = [
        x for x in list(fm["clubName"].unique()) if str(x) not in ["nan", "NaN"]
    ]
    if "fuzzy_teams" in match_methods:
        # fuzzy match team names if no exact match:
        for ys_team in [
            x for x in ys.clubName.unique() if "nan" not in str(x).lower()
        ]:
            if ys_team in all_fm_teams:
                team_names_map_dict[ys_team] = [ys_team]
                print(f"{ys_team} - exact match")
            else:
                team_names_map_dict[ys_team] = [
                    x[0] for x in process.extract(ys_team, all_fm_teams)
                ]
                print(f"{ys_team} - fuzzy matched")

    # these are for us to track and see how many we match with each check
    unique_name_exact_matches = 0
    within_team_matches = 0
    exact_stat_matches = 0

    # we are tracking rough matches as the total matches minus the sum of all categories

    # strange place to define this - but we will need this block two times below,
    # so defining it here, before we get into the loop
    if "rough_stats_match" in match_methods:

        def check_deviations():
            height_deviation = np.absolute(row["height"] - fm["height"])
            birth_deviation = np.absolute(
                float(row["birthYear"]) - fm["birthYear"]
            )
            fm_subset_rough = fm[
                (height_deviation <= 1) & (birth_deviation <= 1)
            ]
            if len(fm_subset_rough) > 0:
                match_rough = process.extractOne(
                    name, fm_subset_rough["fullName"].tolist()
                )
                # we assume that match above 90 is good enough
                if match_rough[1] > threshold:
                    if verbose:
                        print(match_rough)
                    merge_df.loc[i] = row
                    merge_df.at[i, "matching_type"] = 3
                    merge_df.at[i, "fmUniqueId"] = fm_subset_rough[
                        fm_subset_rough["fullName"] == match_rough[0]
                    ]["fmUniqueId"][0]
                    merge_df.at[i, "fmRowId"] = fm_subset_rough[
                        fm_subset_rough["fullName"] == match_rough[0]
                    ]["rowId"][0]

    for i, row in ys.iterrows():
        try:
            name = row["fullName"]
        except:  # having name declared before the try-except block
            name = "This should really never happen"
        try:
            if verbose:
                print(i, name)
            # if we have unique, exact, match, then we assume we are good to go
            if (
                len(fm[fm["fullName"] == name]) == 1
                and len(ys[ys["fullName"] == name]) == 1
            ):
                merge_df.loc[i] = row
                merge_df.at[i, "matching_type"] = 0
                merge_df.at[i, "fmUniqueId"] = fm[fm["fullName"] == name][
                    "fmUniqueId"
                ][0]
                merge_df.at[i, "fmRowId"] = fm[fm["fullName"] == name]["rowId"][
                    0
                ]

                unique_name_exact_matches += 1
            elif "fuzzy_teams" in match_methods:
                # then we go to option b: find a close match (90 or above similarity score WITHIN team
                team = row["clubName"]
                # get top 5 fuzzy matched teams - for most cases this is sufficient, usually it is top 1
                # we need this because team names are all over the place between fm and ys
                fm_subset_similar_teams = fm[
                    fm["clubName"].isin(team_names_map_dict[team])
                ]
                # this should never break, i.e. len(subset)>0 as we are getting top 5 matches:
                match_from_similar_team_names = process.extractOne(
                    name, fm_subset_similar_teams["fullName"].tolist()
                )
                # fuzywuzzy returns tuples with two items: 0 - matched item, 1 -similarity score
                if match_from_similar_team_names[1] > threshold:
                    # we have decent match from similar teams - good enough
                    merge_df.loc[i] = row
                    merge_df.at[i, "matching_type"] = 1
                    merge_df.at[i, "fmUniqueId"] = fm_subset_similar_teams[
                        fm_subset_similar_teams["fullName"]
                        == match_from_similar_team_names[0]
                    ]["fmUniqueId"][0]
                    merge_df.at[i, "fmRowId"] = fm_subset_similar_teams[
                        fm_subset_similar_teams["fullName"]
                        == match_from_similar_team_names[0]
                    ]["rowId"][0]
                    within_team_matches += 1
                    # if no good match, we go on to similar stats - same country, age, height;
                # if we want more matches, height can be dropped because we often have missing values that are imputed
                # with zero and the comparison fails, but then we open the door for false positives
                elif "exact_stats_match" in match_methods:
                    if verbose:
                        print(
                            row["height"], row["birthYear"], row["nationality"]
                        )
                    fm_subset_info = fm[
                        (fm["height"] == row["height"])
                        & (fm["birthYear"] == row["birthYear"])
                        & (fm["nationality"] == row["nationality"])
                    ]
                    if len(fm_subset_info) > 0:
                        match_from_similar_info = process.extractOne(
                            name, fm_subset_info["fullName"].tolist()
                        )
                        # we assume that match above 90 is good enough
                        if match_from_similar_info[1] > threshold:
                            merge_df.loc[i] = row
                            merge_df.at[i, "matching_type"] = 2
                            merge_df.at[i, "fmUniqueId"] = fm_subset_info[
                                fm_subset_info["fullName"]
                                == match_from_similar_info[0]
                            ]["fmUniqueId"][0]
                            merge_df.at[i, "fmRowId"] = fm_subset_info[
                                fm_subset_info["fullName"]
                                == match_from_similar_info[0]
                            ]["rowId"][0]
                            exact_stat_matches += 1
                        elif (
                            "rough_stats_match" in match_methods
                        ):  # last resort - we allow slight errors in height/birth
                            # third option - allow for small deviation, range can be increased slightly
                            # especially for height, to match more guys and potentially risk false positives
                            # defining this as a func cause we will use it twice - once if we did not find adequate
                            # match based on stats and 2 - if there was no one with matching stats in first place
                            check_deviations()  # calling it to check
                    elif "rough_stats_match" in match_methods:
                        check_deviations()
        # making sure that we do not break the loop for long runs, record player where error occured and reason why
        except Exception as e:
            print(f"something went wrong with {name}" + str(e))
    elapse_time = time() - start
    print(
        f"Matched {len(merge_df)} players in {elapse_time} seconds."
        f" {unique_name_exact_matches} unique exact matches,"
        f" {within_team_matches} within-team matches,"
        f" {exact_stat_matches} exact stat matches and"
        f" {len(merge_df) - unique_name_exact_matches - within_team_matches - exact_stat_matches} rough"
        " stat matches"
    )
    # Matched 794 players in 118.09047293663025 seconds. 631 unique exact matches131 within-team matches,
    # 24 exact stat matches and 8 rough stat matches
    return merge_df


def dedupe_and_merge(matched_merged, fm, return_ys=False):
    # the best practice would be to copy the df so not to make changes in place, but with larger datasets this would
    # take too much RAM; besides we wont need this file anywhere else in this pipeline for the moment

    # sorting by matching type - so when dropping potential dupes, the highest priority will be for the most legit way
    # of matching players
    matched_merged = matched_merged.sort_values("matching_type")
    matched_merged = matched_merged.drop_duplicates(
        subset=["fmUniqueId"], keep="first"
    )
    # merged_w_id = pd.merge(matched_merged, pd.Series(match_id_dict, name='fmUniqueId'), left_index=True, right_index=True)
    fm_data = pd.merge(
        fm,
        matched_merged[["playerId", "fmUniqueId"]],
        how="left",
        on="fmUniqueId",
    )
    if not return_ys:
        return fm_data
    else:
        return fm_data, matched_merged


def output_fm_table(fm, con, table_name, output="return", if_exists="replace"):
    if output == "write":
        try:
            fm.to_sql(table_name, if_exists=if_exists, con=con)
        except Exception as e:
            print("Failed to write to db " + str(e))
    elif output == "return":
        return fm
    else:
        raise Exception("Invalid output type")


def main():

    cnx = create_engine(postgres_prod_str)
    ys_init, fm_init = read_data(db_con=cnx)
    ys_prepped, fm_prepped = prep_dfs(ys=ys_init, fm=fm_init)
    merge_df = match_players(
        ys=ys_prepped,
        fm=fm_prepped,
        match_methods=["fuzzy_teams", "exact_stats_match"],
    )
    fm_matched, full_data = dedupe_and_merge(
        merge_df, fm_prepped, return_ys=True
    )

    # following  lines are used to make df identical to what was previously stored in the db
    # merging back those who we couldnt match

    # ys_w_fm_ids = pd.merge(ys_prepped, ys_w_fm_ids[[
    #                        'playerId', 'fmUniqueId', 'fmRowId']], on='playerId', how='left')

    # fixing column names and dropping unnecessary cols
    fm_matched = fm_matched.rename(
        index=str, columns={"nationality": "passportArea_name"}
    )

    fm_matched = fm_matched.drop(columns=["birthYear", "fullName"])

    # fm_matched = output_fm_table(fm_matched, con=None, output='return')
    output_fm_table(
        fm_matched,
        con=cnx,
        table_name="matched_fm",
        output="write",
        if_exists="replace",
    )

    # return fm_matched, full_data


if __name__ == "__main__":
    main()
    # non_fm, df = main()
    # TODO export when directories are relatively settled
