import asyncio


from src.data_collection.wyscout.v2.MatchesUpdater import MatchesUpdater
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.Updates<PERSON>hecker import UpdatesChecker


async def main():

    collection_mode = "initial"
    if collection_mode == "initial":
        from_scratch = True
        phantom_objects = None
    elif collection_mode == "update":
        from_scratch = False
        phantom_objects = ["teams"]
    else:
        raise ValueError("Invalid collection mode")

    matches_checker = UpdatesChecker(
        table_name="empty_matches_for_collection",
        object_type="matches",
        id_name="matchId",
        from_scratch=from_scratch,
        all_objects_table="empty_data_matches",
        all_objects_schema="meta",
    )
    matches_updaters = MatchesUpdater()
    orc = Orchestrator(
        batch_size=2000,
        updates_checker=matches_checker,
        updater=matches_updaters,
        from_scratch=from_scratch,
        skip_empty_games=False,
        phantom_objects=phantom_objects,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
