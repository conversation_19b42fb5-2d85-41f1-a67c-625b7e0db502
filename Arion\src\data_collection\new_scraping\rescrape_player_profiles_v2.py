# It will be used in a DAG in the future
# Running an example script:
# Collecting data for given players and populating the required tables
from settings import postgres_prod_str
from sqlalchemy.engine import create_engine
from src.data_collection.new_scraping.orchestrators.tm_orchestrator import (
    TMOrchestrator,
)
from src.data_collection.new_scraping.scraping.scrape_tm_player_profiles import (
    PlayerProfilesScraper,
)

PROG_TABLE = "player_progress_table"
FAILED_TABLE = "failed_players"
RAW_DATA_TABLE = "raw_data"
RAW_TRANSFERS_TABLE = "raw_transfers_table"
RAW_AGENT_HISTORY_TABLE = "raw_agent_history_table"
RAW_TM_HISTORICAL_CONTRACTS = "raw_tm_historical_contracts"


engine = create_engine(postgres_prod_str)
# asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def run(limit):
    # Get players IDs from initial table
    orchestrator.get_urls_from_progress(limit, "tm_player_url")  # Add limit

    # Second
    # Scrape the players data
    orchestrator.get_df()
    # Third
    # Save the collected players data in the raw table.
    orchestrator.save_scraped_data()
    # Update the progress table
    orchestrator.update_progress("tm_player_id","tm_player_id", "tm_player_id", "tm_player_id")

    return bool(orchestrator.check_progress())


if __name__ == "__main__":
    orchestrator = TMOrchestrator(
        PROG_TABLE,
        FAILED_TABLE,
        RAW_DATA_TABLE,
        RAW_TRANSFERS_TABLE,
        RAW_AGENT_HISTORY_TABLE,
        RAW_TM_HISTORICAL_CONTRACTS,
        engine=engine,
        scraper=PlayerProfilesScraper,
    )

    # Scrape players
    while True:
        if not run(limit=1000):
            break
