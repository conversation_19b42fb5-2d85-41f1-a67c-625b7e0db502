import requests
import json
from datetime import datetime
import pandas as pd
from sqlalchemy import create_engine

from settings import postgres_prod_str
from src.helper_funcs import fast_write_sql

cnx = create_engine(postgres_prod_str)


def get_req(url):
    resp = requests.get(url)
    # print(resp.status_code)
    if resp.status_code != 200:
        print(resp.text)
    return json.loads(resp.text)["data"]


def get_tournaments():
    url = "https://service.instatfootball.com/feed.php?id=753921&key=0wrM5yDp&tpl=230&tournaments=&lang_id=1&lang=en&format=json"
    trs = get_req(url)
    return pd.DataFrame(trs["tournament"])


def main():
    comps = get_tournaments()
    fast_write_sql(
        comps,
        "competitions",
        cnx,
        schema="instat",
        if_exists="replace",
    )
    comp_id_dict = {
        "Germany": 31,
        "England": 39,
        "Russia": 1,
        "Italy": 24,
        "Portugal": 28,
        "Argentina": 93,
        "Turkey": 35,
        # "Brazil": 42,
        "Bulgaria": 75,
        # "Vietnam": 924,
    }

    s_id = 26
    cnx.execute("drop table if exists instat.competitions_matches")
    for comp in comp_id_dict.values():
        url = f"http://service.instatfootball.com/feed.php?id=753921&key=0wrM5yDp&tpl=35&tournament_id={comp}&season_id={s_id}&date_start=&date_end=&lang_id=1&lang=&format=json"
        games_df = pd.DataFrame(get_req(url)["row"])
        fast_write_sql(
            games_df,
            "competitions_matches",
            cnx,
            schema="instat",
            if_exists="append",
        )
    try:
        matches_for_collection = pd.read_sql(
            "select id from instat.competitions_matches where status_name ="
            " 'Breakdown completed' and id not in (select distinct match_id"
            " from instat.events) ",
            cnx,
        )["id"]
        print(len(matches_for_collection))
    except Exception as e:
        print(e)
        matches_for_collection = pd.read_sql(
            "select id from instat.competitions_matches where status_name ="
            " 'Breakdown completed' ",
            cnx,
        )["id"]
    cols = pd.read_sql("select * from instat.events limit 1", cnx).columns
    df_list = [pd.DataFrame(columns=cols)]
    for i, match in enumerate(matches_for_collection):
        ev_url = f"http://service.instatfootball.com/feed.php?id=753921&key=0wrM5yDp&tpl=36&start_ms=0&match_id={match}&lang_id=1&lang=&format=json"
        try:
            events = pd.DataFrame(get_req(ev_url)["row"])
            events["match_id"] = match
            df_list.append(events)
            print(f" match {match} collected")
        except Exception as e:
            print(f" match {match} failed: \n{e}")
        if (i + 1) % 50 == 0:
            ev_df = pd.concat(df_list)
            fast_write_sql(
                ev_df, "events", cnx, schema="instat", if_exists="append"
            )
            print(f"batch {i}", datetime.now())
            df_list = [pd.DataFrame(columns=cols)]
