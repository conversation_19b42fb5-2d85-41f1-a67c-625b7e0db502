import sys
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.postgres_operator import PostgresOperator

from dag_settings import workdir_sorare, config

sys.path.append(workdir_sorare)
experiment_name = 'production'

dag_params = {
    "dag_id": "sorare_tweets_modelling_dag",
    "start_date": datetime(2022, 2, 25, 16),
    "schedule_interval": "1 */1 * * *",
    "default_view": "tree",
    "catchup": False,
    "params": {
        "workdir": workdir_sorare,
        "experiment_name": f'"{experiment_name}"'
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    model_tweet_count_batch_1 = BashOperator(
        task_id="model_tweet_count_batch_1",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_production.py 5 1 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    model_tweet_count_batch_2 = BashOperator(
        task_id="model_tweet_count_batch_2",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_production.py 5 2 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    model_tweet_count_batch_3 = BashOperator(
        task_id="model_tweet_count_batch_3",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_production.py 5 3 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    model_tweet_count_batch_4 = BashOperator(
        task_id="model_tweet_count_batch_4",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_production.py 5 4 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    model_tweet_count_batch_5 = BashOperator(
        task_id="model_tweet_count_batch_5",
        bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                          python3 src/modelling/calculate_tweet_thresholds_production.py 5 5 "src/configs/config_production.json" {{params.experiment_name}}
                           """,
    )

    # model_tweet_count_batch_1_noretweet = BashOperator(
    #     task_id="model_tweet_count_batch_1_noretweet",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_no_retweet_tweet_thresholds.py 5 1 "src/configs/config_production.json" {{params.experiment_name}}
    #                        """,
    # )

    # model_tweet_count_batch_2_noretweet = BashOperator(
    #     task_id="model_tweet_count_batch_2_noretweet",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_no_retweet_tweet_thresholds.py 5 2 "src/configs/config_production.json" {{params.experiment_name}}
    #                        """,
    # )

    # model_tweet_count_batch_3_noretweet = BashOperator(
    #     task_id="model_tweet_count_batch_3_noretweet",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_no_retweet_tweet_thresholds.py 5 3 "src/configs/config_production.json" {{params.experiment_name}}
    #                        """,
    # )

    # model_tweet_count_batch_4_noretweet = BashOperator(
    #     task_id="model_tweet_count_batch_4_noretweet",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_no_retweet_tweet_thresholds.py 5 4 "src/configs/config_production.json" {{params.experiment_name}}
    #                        """,
    # )

    # model_tweet_count_batch_5_noretweet = BashOperator(
    #     task_id="model_tweet_count_batch_5_noretweet",
    #     bash_command=""" export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
    #                         cd {{ params.workdir }}
    #                       python3 src/modelling/calculate_no_retweet_tweet_thresholds.py 5 5 "src/configs/config_production.json" {{params.experiment_name}}
    #                        """,
    # )




    [model_tweet_count_batch_1, model_tweet_count_batch_2,model_tweet_count_batch_3, model_tweet_count_batch_4, model_tweet_count_batch_5]
    # [model_tweet_count_batch_1_noretweet, model_tweet_count_batch_2_noretweet, model_tweet_count_batch_3_noretweet, model_tweet_count_batch_4_noretweet, model_tweet_count_batch_5_noretweet]
    

