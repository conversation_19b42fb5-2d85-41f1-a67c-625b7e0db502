# Collect transfers
# The transfers need to be collected for all players and appended each time because there is no way to query the transfers incrementally

source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)

con = make_connection()
player_ids = dbGetQuery(con, 'select distinct "playerId" from seasons_players')
transfers = list()

for (i in 1:nrow(player_ids)) {
  pid = player_ids$playerId[i]
  Sys.sleep(0.1)
  resp = GET(
    paste0(
      "https://apirest.wyscout.com/v2/players/",
      pid, '/transfers'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  cont = content(resp, type = 'text')
  cont = jsonlite::fromJSON(cont)
  transfer = cont$transfer
  transfer$playerId = pid
  transfers[[length(transfers) + 1]] = transfer
}

transfers = lapply(transfers, data.frame, stringsAsFactors = F)
transfers_df = do.call(plyr::rbind.fill, transfers)

dbDisconnect(con)
con <- make_connection()
collected_transfers <- dbGetQuery(con, 'select distinct "transferId" from player_transfers')
transfers_df = transfers_df %>%
  anti_join(collected_transfers)
dbWriteTable(con, 'player_transfers', transfers_df, overwrite = T, row.names = F)
dbDisconnect(con)
