{"positions": [{"target_position": "lb", "variables": {"interceptions_reg": 3, "shot_assists_reg": 4, "fouls_reg": 1, "forward_passes_str": 1, "progressive_runs_reg": 2, "recoveries_to_fouls_rat": 2, "new_defensive_duels_str": 4, "crosses_str": 4}, "comparable_positions": ["lb5", "lb", "lwb"], "age_range": {"lower": 20, "upper": 30}, "height_range": {"lower": 170, "upper": 200}}, {"target_position": "rb", "variables": {"interceptions_reg": 3, "shot_assists_reg": 4, "looseballs_str": 3, "fouls_reg": 1, "forward_passes_str": 1, "progressive_runs_reg": 2, "recoveries_to_fouls_rat": 2, "new_defensive_duels_str": 4, "crosses_str": 4}, "comparable_positions": ["rb5", "rb", "rwb"], "age_range": {"lower": 20, "upper": 30}, "height_range": {"lower": 170, "upper": 200}}], "filters": {"top_n": 10, "competitions": [{"country": "England", "division_level": 1}, {"country": "England", "division_level": 2}, {"country": "Germany", "division_level": 1}, {"country": "Germany", "division_level": 2}], "passport": ["Germany", "France", "Italy", "Belgium", "Sweden"]}, "model_params": {"min_num_games": 7, "min_tag_threshold": 10, "min_pct_played": 75, "time_decay_coef": 1, "n_days_window": 60, "min_scale_log_base": 2, "competition_scaling": "root3", "competitions_included": ["Domestic league", "Domestic cup"]}}