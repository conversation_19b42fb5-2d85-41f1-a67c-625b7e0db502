# Overwrite season retlated structures
# # Create Database Structure
source('src/data_collection/wyscout/00_libs.R')

owt = as.logical(Sys.getenv("OVERWRITE_SEASON_TABLES"))

try({dbDisconnect(con)})
con = make_connection()

competition_seasons = dbReadTable(con, 'seasons')
competition_seasons = competition_seasons %>%
  filter(seasonstartDate <= Sys.Date()) %>%
  select(seasonId, seasoncompetitionId) %>%
  rename(competitionId = seasoncompetitionId)
competitions = dbReadTable(con, 'competitions')
competitions = competitions %>%
  filter(gender == 'male')
competition_seasons = competition_seasons[competition_seasons$competitionId%in%competitions$competitionId,]

if(!owt){
  
  downloaded_seasons = dbGetQuery(con, 'select distinct "seasonId" from seasons_matches')

  # Get final season for each competition
  final_seasons = competition_seasons %>%
    group_by(competitionId) %>%
    summarise(seasonId = max(seasonId, na.rm = T))

  competition_seasons = competition_seasons[!competition_seasons$seasonId%in%downloaded_seasons$seasonId,]

  # Combine missing seasons and final competition seasons
  competition_seasons = rbind(competition_seasons, final_seasons)

  # Get unique seasons
  competition_seasons = unique(competition_seasons)
  
  # Get written matches/endpoint id for each end-point
  written_season_matches = dbGetQuery(con, 'select distinct "seasonId", "matchId" from seasons_matches')
  written_season_players = dbGetQuery(con, 'select distinct "seasonId", "playerId" from seasons_players')
  written_season_teams = dbGetQuery(con, 'select distinct "seasonId", "teamId" from seasons_teams')

}

seasons_matches = list()
seasons_players = list()
seasons_teams = list()
seasons_no_matches = NA
seasons_missing_players = NA
cnt = 0
for (comp_id in competition_seasons$seasonId[1:length(competition_seasons$seasonId)]) {
  cnt = cnt + 1
  # print(cnt)
  try({
    Sys.sleep(0.08)
    
    ## Get Matches
    resp = GET(
      paste0(
        'https://apirest.wyscout.com/v2/seasons/',
        comp_id,
        '/matches'
      ),
      authenticate(wyscout_username, wyscout_pass)
    )
    
    if(resp$status_code == 200){
      cont = content(resp, as = 'parsed')
      if(length(cont$matches)>0){
        try({
          mtch = cont$matches
          mtch = lapply(mtch, remove_null)
          com_matches = plyr::ldply(mtch, data.frame, stringsAsFactors = F)
          
          home_away = sapply(strsplit(com_matches[['label']], split = ', ', fixed = T), function(x)
            x[[1]])
          home = sapply(strsplit(home_away, split = ' - ', fixed = T), function(x)
            x[[1]])
          away = sapply(strsplit(home_away, split = ' - ', fixed = T), function(x)
            x[[2]])
          
          score = sapply(strsplit(com_matches[['label']], split = ', ', fixed = T), function(x)
            x[[2]])
          home_score = as.numeric(sapply(strsplit(
            score, split = '-', fixed = T
          ), function(x)
            x[[1]]))
          away_score = as.numeric(sapply(strsplit(
            score, split = '-', fixed = T
          ), function(x)
            x[[2]]))
          
          com_matches['home_team'] = home
          com_matches['away_team'] = away
          com_matches['home_score'] = home_score
          com_matches['away_score'] = away_score
          com_matches['home_win'] = as.numeric(com_matches[['home_score']] > com_matches[['away_score']])
          com_matches['draw'] = as.numeric(com_matches[['home_score']] == com_matches[['away_score']])
          com_matches['seasonId'] = comp_id
          seasons_matches[[length(seasons_matches) + 1]] = com_matches
        })
        
      }else{
        # print(paste('Matches missing for', comp_id))
        seasons_no_matches = c(seasons_no_matches, comp_id)
      }
    }
    
    
    Sys.sleep(0.08)
    ## Get Players
    try({
      resp = GET(
        paste0(
          'https://apirest.wyscout.com/v2/seasons/',
          comp_id,
          '/players'
        ),
        authenticate(wyscout_username, wyscout_pass)
      )
      #cnt = cnt + 1
      if(resp$status_code == 200){
        cont = content(resp)
        if(length(cont$players)>0){
          
          cps = cont$players
          cps = lapply(cps, remove_null)
          cps = lapply(cps, function(x) lapply(x, remove_null))
          comp_players = plyr::ldply(cps, flatten_squad_data)
          comp_players['seasonId'] = comp_id
          
          colnames(comp_players) = gsub('wyId', 'playerId', colnames(comp_players))
          colnames(comp_players) = gsub('.', '_', colnames(comp_players), fixed = T)
          seasons_players[[length(seasons_players) + 1]] = comp_players
          
          # RPostgreSQL::dbWriteTable(
          #   con,
          #   'competition_players',
          #   comp_players,
          #   overwrite = owt,
          #   append = !owt,
          #   row.names = F,
          #   rownames = F
          # )
        }else{
          seasons_missing_players = c(seasons_missing_players, comp_id)
        }
      }
      
      
    })
    
    Sys.sleep(0.08)
    ## Get Teams
    resp = GET(
      paste0(
        'https://apirest.wyscout.com/v2/seasons/',
        comp_id,
        '/teams'
      ),
      authenticate(wyscout_username, wyscout_pass)
    )
    try({
      if(resp$status_code ==200){
        cont = content(resp)
        if(length(cont$teams)>0){
          tms = cont$teams
          tms = lapply(tms, remove_null)
          comp_teams = plyr::ldply(tms, data.frame, stringsAsFactors = F)
          #comp_teams['competition'] = comp_id # Ask Daniel What to do with the children teams
          comp_teams = comp_teams[!grepl('children', colnames(comp_teams))]
          comp_teams['seasonId'] = comp_id
          # if(!is.null(seasons_teams)){
          #   seasons_teams[setdiff(names(comp_teams), names(seasons_teams))] = NA
          #   comp_teams[setdiff(names(seasons_teams), names(comp_teams))] = NA
          # }
          seasons_teams[[length(seasons_teams) + 1]] = comp_teams
        }
      }
      
    })
  })
  
  
}

if(length(seasons_matches) > 0){
  seasons_matches = do.call(plyr::rbind.fill, seasons_matches)
  
  colnames(seasons_matches) = gsub('.', '_', colnames(seasons_matches), fixed = T)
  colnames(seasons_matches) = gsub('wyId', 'matchId', colnames(seasons_matches))
  seasons_matches = seasons_matches[!duplicated(seasons_matches[c('matchId')]),]
  
  if(!owt){
    seasons_matches = seasons_matches %>%
      anti_join(written_season_matches)
  }
  
  try({dbDisconnect(con)})
  con = make_connection()
  
  RPostgreSQL::dbWriteTable(
    con,
    'seasons_matches',
    seasons_matches,
    overwrite = owt,
    append = !owt,
    row.names = F,
    rownames = F
  )
  
  
  
}


if(length(seasons_players) > 0){
  seasons_players = do.call(plyr::rbind.fill, seasons_players)
  
  colnames(seasons_players) = gsub('.', '_', colnames(seasons_players), fixed = T)
  colnames(seasons_players) = gsub('wyId', 'playerId', colnames(seasons_players))
  seasons_players = seasons_players[!duplicated(seasons_players[c('playerId', 'seasonId')]),]
  
  if(!owt){
    seasons_players = seasons_players %>%
      anti_join(written_season_players)
  }
  
  try({dbDisconnect(con)})
  con = make_connection()
  
  colnames(seasons_players) = gsub('.', '_', colnames(seasons_players), fixed = T)
  RPostgreSQL::dbWriteTable(
    con,
    'seasons_players',
    seasons_players,
    overwrite = owt,
    append = !owt,
    row.names = F,
    rownames = F
  )
  
}

if(length(seasons_teams) > 0){
  seasons_teams = do.call(plyr::rbind.fill, seasons_teams)
  
  colnames(seasons_teams) = gsub('.', '_', colnames(seasons_teams), fixed = T)
  colnames(seasons_teams) = gsub('wyId', 'teamId', colnames(seasons_teams))
  seasons_teams = seasons_teams[!duplicated(seasons_teams[c('teamId', 'seasonId')]),]
  
  if(!owt){
    seasons_teams = seasons_teams %>%
      anti_join(written_season_teams)
  }
  
  
  try({dbDisconnect(con)})
  con = make_connection()
  
  colnames(seasons_teams) = gsub('.', '_', colnames(seasons_teams), fixed = T)
  RPostgreSQL::dbWriteTable(
    con,
    'seasons_teams',
    seasons_teams,
    overwrite = owt,
    append = !owt,
    row.names = F,
    rownames = F
  )
  
}


try({dbDisconnect(con)})

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'seasons_teams',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'seasons_players',tbl_qry))
  dbGetQuery(con, gsub('tb_name', 'seasons_matches',tbl_qry))
}

dbDisconnect(con)
