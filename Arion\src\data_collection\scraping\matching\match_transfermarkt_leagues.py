import pandas as pd
from sqlalchemy import create_engine

from settings import postgres_prod_str
from src.helper_funcs import read_config
from src.data_collection.scraping.cleaning import fix_league_levels


def read_ws_comps(cnx):
    comps_query = """ select *
                FROM competitions 
                WHERE "gender"='male' and "category"='default' and "type"='club'
                """
    return pd.read_sql(comps_query, cnx)


def match_leagues(tm, ws, merge_cols):
    mg = pd.merge(
        left=ws, right=tm, how="left", on=merge_cols, suffixes=("", "_tm")
    )
    mg = mg.drop_duplicates(subset=["competitionId"], keep=False)
    mg = mg.drop_duplicates(subset=["competitionId_tm"], keep=False)
    return mg


def save_leagues(df, config):
    out_dir = config["scraping"]["directories"]["matched_leagues"]
    df.to_csv(out_dir)


def main(take_df=None, return_df=False):
    cnx = create_engine(postgres_prod_str)
    config = read_config()
    ws = read_ws_comps(cnx)
    ws = fix_league_levels.fix_league_levels(take_df=ws, return_df=True)
    if take_df is None:
        read_loc = config["scraping"]["directories"]["cleaned_league_details"][
            "imputed_league_mean_player_values"
        ]
        tm = pd.read_csv(read_loc)
    else:
        tm = take_df.copy()
    mg = match_leagues(tm, ws, merge_cols=["area_name", "divisionLevel"])
    save_leagues(mg, config)
    if return_df:
        return mg


if __name__ == "__main__":
    main()
