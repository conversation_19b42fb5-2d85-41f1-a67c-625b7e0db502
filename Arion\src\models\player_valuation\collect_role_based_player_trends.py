import pandas as pd
from pytrends.request import TrendReq
from datetime import datetime
import src.models.player_valuation.utils.database_connect as dbconnect
from utils.helper_funcs import fast_write_sql
import re
from settings import PROXIES
from time import sleep
from random import randint
import re

cnx_prod = dbconnect.main()
cnx_prod.execute('delete from player_valuation.role_based_trends')

competitions = pd.read_sql_table('wyscout.competitions', cnx_prod)
tm_to_ws_players = pd.read_sql('select distinct * from wyscout.players ap, transfermarkt.tm_to_ws_ids tm where tm."playerId" = ap."playerId"', cnx_prod)
tm_competitions = pd.read_sql('''
select tm_player_id, joined_name, joined_alt, "date" as "join_date", mv as current_value
from (select distinct on(tt.tm_player_id) * 
from transfermarkt.tm_transfers tt 
order by tt.tm_player_id, "date" desc) iq
where joined_name not in ('Retired', 'Dead')
''', cnx_prod)
tm_to_ws_players = tm_to_ws_players.merge(tm_competitions,
    on='tm_player_id', how='left')
tm_to_ws_players = tm_to_ws_players[tm_to_ws_players.role_name != '']
tm_to_ws_players = tm_to_ws_players.drop_duplicates(subset = ['tm_player_id'])
tm_to_ws_players = tm_to_ws_players.sort_values('current_value', ascending = False)
tm_to_ws_players['role_name'] = tm_to_ws_players['role_name'].str.replace(pat = 'Forward', repl = 'Striker', case = False)

pytrend = TrendReq(proxies=[PROXIES], backoff_factor=0.1, timeout=(3, 10))
# pytrend = TrendReq(backoff_factor=0.1, timeout=(3, 10))

player_trends = pd.DataFrame()

start_time = datetime.now()
for i, row in tm_to_ws_players.reset_index().iterrows():

    successful = False
    while not successful:
        
        sleep(randint(1, 5))
        role = row['role_name']
        # player_name = row['firstName'] + ' ' + row['lastName']
        player_name = row['shortName']
        print(f'Requesting Trends for {player_name}')
        if player_name == 'C. Ronaldo':
            player_name = 'Cristiano Ronaldo'
        else:
            player_name = re.sub('[A-z]\.\s', '' , player_name)
            
        try:
            pytrend.build_payload(kw_list=[role, player_name], 
                timeframe='today 5-y', cat=294)

            df = pytrend.interest_over_time()
            df = df.reset_index()
            df.columns = ['date', 'role_trend', 'player_trend', 'is_partial']
            df['role_name'] = role
            df['playerId'] = row['playerId'].unique()[0]
            df['tm_player_id'] = row['tm_player_id']
            
            player_trends = pd.concat([player_trends, df], axis=0, ignore_index=True)
            successful = True
        
        except Exception as e:
            successful = False
            sleep(randint(60, 120))
            pytrend = TrendReq(proxies=[PROXIES], backoff_factor=0.1, timeout=(3, 10))

        
    if i%10 == 0:
        fast_write_sql(player_trends, 'role_based_trends', cnx_prod, schema='player_valuation', if_exists='append')
        cnx_prod.execute('grant all on player_valuation.role_based_trends to kliment, elvan, airflow')
        end_time = datetime.now()
        player_trends = pd.DataFrame()
        print(f'Iteration took {end_time - start_time}')
        start_time = end_time