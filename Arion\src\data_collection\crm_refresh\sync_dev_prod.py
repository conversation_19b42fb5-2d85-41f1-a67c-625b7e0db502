from pymongo import MongoClient

from settings import MONGO_URL_DEV, MONGO_URL_PROD


def main():
    collections = ["players", "team_requests", "contacts", "users"]
    prod, dev = MongoClient(MONGO_URL_PROD, uuidRepresentation="standard").get_default_database(), MongoClient(MONGO_URL_DEV, uuidRepresentation="standard").get_default_database()
    for collection in collections:
        prod_coll = prod[collection]
        docs = prod_coll.find({})
        dev.drop_collection(collection)
        dev_coll = dev[collection]
        dev_coll.insert_many(docs)
        print(f"{collection} synced, {dev_coll.count_documents({})} documents")


if __name__ == "__main__":
    main()