import requests
from bs4 import BeautifulSoup
import pandas as pd
from requests.api import get
from sqlalchemy import create_engine
from src.helper_funcs import dedupe_table
from settings import PROXIES

URL = "https://www.transfermarkt.com/spieler-statistik/legionaere/statistik/stat/land_id/28/land/"
headers = {
    "User-Agent": (
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko)"
        " Chrome/47.0.2526.106 Safari/537.36"
    )
}


class ExampleScraper:
    def __init__(self, url, headers):
        self.url = url
        self.headers = headers
        self.dict = {"tm_player_id": [], "tm_player_url": []}
        self.start = 19
        self.end = 201

    def scrape_data(self):
        for i in range(self.start, self.end):
            if i == 40 or i == 127:
                continue
            elif i == 30:
                i = 266
            new_url = f"{self.url}{i}"
            self.add_data(new_url)
        print(self.dict)
        for c in range(1, 6):
            new_url = f"https://www.transfermarkt.com/spieler-statistik/legionaere/statistik/stat/ajax/yw1/land_id/28/land/40/page/{c}"
            self.add_data(new_url)
        print(self.dict)
        for g in range(1, 3):
            new_url = f"https://www.transfermarkt.com/spieler-statistik/legionaere/statistik/stat/ajax/yw1/land_id/28/land/127/page/{g}"
            self.add_data(new_url)
        print(self.dict)
        return pd.DataFrame(self.dict)

    def add_data(self, url):
        new_url = url
        soup = get_soup_from_url(new_url, proxies=PROXIES)
        if soup.find("table", attrs={"class": "items"}):
            ll = soup.find_all("td", attrs={"class": "hauptlink"})
            for s in range(0, len(ll), 3):
                a = ll[s].find("a")["href"]
                self.dict["tm_player_id"].append(a.split("/")[-1])
                self.dict["tm_player_url"].append(a)


def get_soup_from_url(
    url, session=None, header=True, proxies=None, payload=None, method="get"
):
    if proxies is not None:
        proxies = {"https": PROXIES}
    if session is not None:
        req = session.get(url, proxies=proxies)
        text = req.text
    else:
        if header:
            header = {
                "User-Agent": (
                    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML,"
                    " like Gecko) Chrome/49.0.2623.112 Safari/537.36"
                )
            }
            if method == "get":
                req = requests.get(
                    url, headers=header, proxies=proxies, data=payload
                )
            elif method == "post":
                req = requests.post(
                    url, headers=header, proxies=proxies, data=payload
                )
            else:
                raise ValueError("Invalid method type")
            text = req.text
    if req.status_code >= 400:
        return
    soup = BeautifulSoup(text, "html.parser")
    return soup


# ex = ExampleScraper(URL, headers)
# df = ex.scrape_data()

def main():
    engine = create_engine(
        f"postgresql://postgres:password123@localhost:5433/demo_scraper"
    )
    # with engine.begin() as connection:
    # df.to_sql("bg_tm_urls", con=connection, if_exists="append", )


    dedupe_table(engine, "bg_tm_urls", "public", ["tm_player_id"])

if __name__ == "__main__":
    main()