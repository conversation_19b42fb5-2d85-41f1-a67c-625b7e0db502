from datetime import datetime, timed<PERSON>ta
import sys
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir, config

sys.path.append(workdir)

dag_params = {
"dag_id": "crm_spocs_requests_transfer",
    "start_date": datetime(2021, 12, 8),
    "schedule_interval": timedelta(minutes=30),
    "catchup": False,
    "default_view": "tree",
    "params": {
        "workdir": workdir,
        "config": config,
        "refresh": "True",
        "timestamp": datetime.today().strftime("%Y_%m_%d_%H%M"),
    },
    "max_active_runs": 1,
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": [
            "<EMAIL>",
            "<EMAIL>",
        ],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 0,
        "retry_delay": timed<PERSON>ta(minutes=5),
    },
}

with DAG(**dag_params) as dag:
    transfer_spocs_requests = BashOperator(
        task_id="transfer_spocs_requests",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/data_collection/new_scraping/adhoc/crm_spocs_requests_transfer.py """,
    )

    (transfer_spocs_requests)