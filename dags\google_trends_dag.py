from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir

dag_params = {
    "dag_id": "google_trend_collection",
    "start_date": datetime(2021, 6, 1),
    "schedule_interval": timedelta(days=14),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 0,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    role_based_player_trends = BashOperator(
        task_id="collect_role_based_player_trends",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/collect_role_based_player_trends.py""",
    )

    role_based_player_trends
