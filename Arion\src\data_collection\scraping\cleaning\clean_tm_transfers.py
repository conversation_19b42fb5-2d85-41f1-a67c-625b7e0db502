import re
import numpy as np
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import postgres_prod_str, postgres_dev_str
from src.data_collection.scraping.cleaning.clean_player_profiles import (
    conv_str_to_float_col,
)


class Cleaner:
    def __init__(self, cnx_dev, raw_table_name, prepped_table_name):
        self.cnx_dev = cnx_dev
        self.raw_df, self.df = None, None
        self.raw_table_name = raw_table_name
        self.prepped_table_name = prepped_table_name

    def read_raw_table(self):
        """
        --
        """
        self.raw_df = pd.read_sql(
            f"SELECT * FROM {self.raw_table_name}", self.cnx_dev
        )

    @staticmethod
    def get_transfer_type(x):
        """
        Get transfer type from fee column in tm transfers table
        """
        if x is None:
            return np.nan
        search = re.search(
            "^\s*[A-Za-z]+(?:\s+[A-Za-z]+)*\s*", x, re.IGNORECASE
        )
        if search is not None:
            transfer_type = search.group(0)
        else:
            value = conv_str_to_float_col(x)
            transfer_type = "Transfer" if not np.isnan(value) else np.nan
        return str(transfer_type).lower()

    def prep_table(self):
        """
        Fixes date, crates type col, fixed monetary values
        """
        df = self.raw_df
        df["date"] = pd.to_datetime(df["date"], errors="coerce")
        df["type"] = df["fee"].apply(lambda x: self.get_transfer_type(x))
        for id_col in ["left_id", "joined_id"]:
            df[id_col] = df[id_col].fillna(0).astype(int)
        for col in ["fee", "mv"]:
            df[col] = df[col].apply(lambda x: conv_str_to_float_col(x))
        # df['fee'] = np.where(df['type'] != 'Free transfer', df['fee'], 0)
        self.df = df

    def write_table(self):
        """
        Write prepped table to db
        """
        self.df.to_sql(
            self.prepped_table_name,
            self.cnx_dev,
            if_exists="replace",
            index=False,
        )


def main():
    cnx_dev = create_engine(postgres_dev_str)
    clean = Cleaner(cnx_dev, "raw_tm_transfers", "tm_transfers")
    clean.read_raw_table()
    clean.prep_table()
    clean.write_table()


if __name__ == "__main__":
    main()
