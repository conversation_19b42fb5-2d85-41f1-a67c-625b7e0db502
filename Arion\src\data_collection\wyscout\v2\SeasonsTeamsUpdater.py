import pandas as pd

from src.data_collection.wyscout.v2.SeasonsXUpdater import SeasonsXUpdater


class SeasonsTeamsUpdater(SeasonsXUpdater):
    def __init__(self, table_name, id_name, only_active=True):
        super().__init__(table_name, id_name, only_active)
        self.base_url = "https://apirest.wyscout.com/v3/seasons/_ID/teams"
        self.object_type = self.base_url.split("/")[-1]

    @staticmethod
    def format_response(payload, code):
        for x in payload:
            x.update({"seasonId": code})
        return payload

    def prep(self, result):
        df = pd.DataFrame(result)
        df = self.flatten_dict_cols(df, keep_parent_col_name=True)
        df.columns = [x.replace("wyId", "teamId") for x in df.columns]
        seasons_teams = df[["seasonId", "teamId"]]
        teams = df[[x for x in df.columns if x != "seasonId"]].drop_duplicates(
            subset=["teamId"]
        )
        return seasons_teams, teams
