# # Create Database Structure

source('src/data_collection/wyscout/00_libs.R')
dbDisconnect(con)
con = make_connection()

competitions = dbReadTable(con, 'competitions')
competition_seasons = NULL
for (comp_id in competitions$competitionId) {
  Sys.sleep(0.1)
  resp = GET(
    paste0(
      'https://apirest.wyscout.com/v2/competitions/',
      comp_id,
      '/seasons'
    ),
    authenticate(wyscout_username, wyscout_pass)
  )
  cont = content(resp)
  if(length(cont$seasons)>0){
    seasons = lapply(cont$seasons, function(x) remove_null(x[['season']]))
    season = plyr::ldply(seasons, function(x) data.frame(x, stringsAsFactors = F))
    competition_seasons = rbind(competition_seasons, season)
  }

}


colnames(competition_seasons) = gsub('wyId', 'seasonId', colnames(competition_seasons))
competition_seasons$seasonId = as.integer(competition_seasons$seasonId)
colnames(competition_seasons) = gsub('.', '_', colnames(competition_seasons), fixed = T)

RPostgreSQL::dbWriteTable(
  con,
  'competition_seasons',
  unique(competition_seasons),
  overwrite = T,
  append = F,
  row.names = F,
  rownames = F
)

dbDisconnect(con)

con <- make_connection()
table_qry = 'GRANT ALL PRIVILEGES ON TABLE tb_name TO username;'

for(usr in c('elvan', 'kliment', 'daniel', 'postgres')){
  tbl_qry = gsub('username', usr, table_qry)
  dbGetQuery(con, gsub('tb_name', 'competition_seasons',tbl_qry))
}

dbDisconnect(con)
