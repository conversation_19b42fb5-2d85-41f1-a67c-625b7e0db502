import json

from sqlalchemy import create_engine
from settings import postgres_prod_str


def main():
    cnx_prod = create_engine(postgres_prod_str)
    with open("src/reporting/talent_hunter/talent_hunter_base_query.sql") as f:
        base_query = f.read()

    with open("src/reporting/talent_hunter/talent_hunter_comps_list.json") as f:
        comps_list = json.load(f)
    comp_filter_str = "\n OR ".join(
        f"""(cs.area_name = '{x['area_name']}'
            and cs."divisionLevel" > 0 and cs."divisionLevel" <= {x["Lowest division"]} )"""
        for x in comps_list
    )
    base_query = base_query.replace("_COMP_FILTER", comp_filter_str).replace(
        "%", "%%"
    )
    cnx_prod.execute(
        f"""DROP TABLE IF EXISTS derived_tables.young_players_appearances;
                {base_query};
                GRANT ALL ON derived_tables.young_players_appearances to elvan;
                """
    )


if __name__ == "__main__":
    main()
