import yaml
workdir = '/airflow/ArionFlow/Arion'
#workdir = "/mnt/c/Projects/ArionFlow/Arion"
workdir_v2 = '/airflow/ArionFlow/Arion'
workdir_ff = '/home/<USER>/Projects/FantasyFootball'
workdir_sorare = '/home/<USER>/Projects/sorare'
workdir_sorare_test = '/home/<USER>/Projects/working_branches/sorare'
workdir_speed = '/home/<USER>/Projects/quicksilver'
workdir_pv = '/home/<USER>/Projects/player-valuation'
# loading it this way instead of with the helper func so that we dont load the helper funcs, 
#  because then we will need to import modules, also using yaml.Loader instead of FullLoader
# because FullLoader is supported only by later version and upgrading yaml is tricky due to Distutils
with open(workdir + '/config.yml', 'r') as yamlfile:
    cf = yaml.load(yamlfile, Loader=yaml.Loader)
    
db_config = cf['db_tables']
config = cf['scraping']['directories']