-- wyscout.player_match_positions
CREATE INDEX IF NOT EXISTS idx_positions_player ON wyscout.player_match_positions("playerId");

CREATE INDEX IF NOT EXISTS idx_positions_match ON wyscout.player_match_positions("matchId");

CREATE INDEX IF NOT EXISTS idx_positions_code ON wyscout.player_match_positions("position");

CREATE INDEX IF NOT EXISTS idx_positions_percent ON wyscout.player_match_positions("percent");

--wyscout.matches
CREATE INDEX IF NOT EXISTS idx_matches_match ON wyscout.matches("matchId");

CREATE INDEX IF NOT EXISTS idx_matches_date ON wyscout.matches("date");

CREATE INDEX IF NOT EXISTS idx_matches_status ON wyscout.matches("status");

CREATE INDEX IF NOT EXISTS idx_matches_competition ON wyscout.matches("competitionId");

CREATE INDEX IF NOT EXISTS idx_matches_season ON wyscout.matches("seasonId");

CREATE INDEX IF NOT EXISTS idx_matches_home_team ON wyscout.matches("home_teamId");

CREATE INDEX IF NOT EXISTS idx_matches_away_team ON wyscout.matches("away_teamId");

-- advanced_stats
-- CREATE INDEX IF NOT EXISTS idx_adv_date ON wyscout.advanced_stats("date");

CREATE INDEX IF NOT EXISTS idx_adv_player ON wyscout.advanced_stats("playerId");

CREATE INDEX IF NOT EXISTS idx_adv_match ON wyscout.advanced_stats("matchId");

-- CREATE INDEX IF NOT EXISTS idx_adv_comp ON wyscout.advanced_stats("competitionId");

-- CREATE INDEX IF NOT EXISTS idx_adv_minutes ON wyscout.advanced_stats("minutesTagged");

-- events
CREATE INDEX IF NOT EXISTS idx_events_match ON wyscout.events("matchId");

-- formations
CREATE INDEX IF NOT EXISTS idx_formations_player ON wyscout.formations("playerId");

CREATE INDEX IF NOT EXISTS idx_formations_match ON wyscout.formations("matchId");

--wyscout.players
CREATE INDEX IF NOT EXISTS idx_players_player ON wyscout.players("playerId");

CREATE INDEX IF NOT EXISTS idx_players_team ON wyscout.players("currentTeamId");

CREATE INDEX   IF NOT EXISTS idx_players_birth ON wyscout.players("birthDate");

CREATE INDEX  IF NOT EXISTS idx_players_birth_area ON wyscout.players("birthArea_name");

CREATE INDEX  IF NOT EXISTS idx_players_role_code2 ON wyscout.players("role_code2");

CREATE OR REPLACE FUNCTION public.f_unaccent(text)
  RETURNS text AS
$func$
SELECT public.unaccent('public.unaccent', $1)  -- schema-qualify function and dictionary
$func$  LANGUAGE sql IMMUTABLE PARALLEL SAFE STRICT;

CREATE INDEX IF NOT EXISTS idx_trgm_players_full_name ON wyscout.players 
USING GIST ((f_unaccent("firstName") || ' ' || f_unaccent("lastName")) gist_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_trgm_players_last_name ON wyscout.players 
USING GIST (f_unaccent("lastName") gist_trgm_ops);

--teams
CREATE INDEX IF NOT EXISTS idx_teams_team ON wyscout.teams("teamId");

CREATE INDEX IF NOT EXISTS idx_trgm_teams_name ON wyscout.teams 
USING GIST (f_unaccent("name") gist_trgm_ops);

--competitions
CREATE INDEX IF NOT EXISTS idx_competitions_competition ON wyscout.competitions("competitionId");

CREATE INDEX IF NOT EXISTS idx_competitions_country ON wyscout.competitions("area_name");

CREATE INDEX IF NOT EXISTS idx_competitions_division ON wyscout.competitions("divisionLevel");

CREATE INDEX IF NOT EXISTS idx_competitions_category ON wyscout.competitions("category");

CREATE INDEX IF NOT EXISTS idx_competitions_type ON wyscout.competitions("type");

CREATE INDEX IF NOT EXISTS idx_competitions_gender ON wyscout.competitions("gender");

--seasons
CREATE INDEX IF NOT EXISTS idx_seasons_season ON wyscout.seasons("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_start_date ON wyscout.seasons("startDate");

CREATE INDEX IF NOT EXISTS idx_seasons_end_date ON wyscout.seasons("endDate");

CREATE INDEX IF NOT EXISTS idx_seasons_status ON wyscout.seasons("active");

CREATE INDEX IF NOT EXISTS idx_seasons_competition ON wyscout.seasons("competitionId");

--seasons_teams
CREATE INDEX IF NOT EXISTS idx_seasons_teams_season ON wyscout.seasons_teams("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_teams_team ON wyscout.seasons_teams("teamId");

--seasons_players
CREATE INDEX IF NOT EXISTS idx_seasons_players_season ON wyscout.seasons_players("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_players_player ON wyscout.seasons_players("playerId");

--seasons_matches
CREATE INDEX IF NOT EXISTS idx_seasons_matches_season ON wyscout.seasons_matches("seasonId");

CREATE INDEX IF NOT EXISTS idx_seasons_matches_match ON wyscout.seasons_matches("matchId");

--transfermarkt_data
CREATE INDEX IF NOT EXISTS idx_tm_player ON transfermarkt.transfermarkt_data("playerId");

CREATE INDEX IF NOT EXISTS idx_tm_agent ON transfermarkt.transfermarkt_data("agent");

--tm_injuries
CREATE INDEX IF NOT EXISTS idx_tm_injuries_player ON transfermarkt.transfermarkt_injuries("playerId");

--lineups
CREATE INDEX IF NOT EXISTS idx_lineups_player ON wyscout.lineups("playerId");

CREATE INDEX IF NOT EXISTS idx_lineups_match ON wyscout.lineups("matchId");

CREATE INDEX IF NOT EXISTS idx_lineups_team ON wyscout.lineups("teamId");

--leagues_standings
CREATE INDEX IF NOT EXISTS ids_leagues_standings_season ON wyscout.leagues_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_team ON wyscout.leagues_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_leagues_standings_placement ON wyscout.leagues_standings("placement");

--cups_standings
CREATE INDEX IF NOT EXISTS ids_cups_standings_season ON wyscout.cups_standings("seasonId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_team ON wyscout.cups_standings("teamId");

CREATE INDEX IF NOT EXISTS ids_cups_standings_round_name ON wyscout.cups_standings("roundName");

--player_roles
CREATE INDEX IF NOT EXISTS idx_player_roles_player ON derived_tables.player_roles("playerId");

--player_match_roles
CREATE INDEX IF NOT EXISTS idx_player_match_roles_player ON derived_tables.player_match_roles("playerId");

CREATE INDEX IF NOT EXISTS idx_player_match_roles_match ON derived_tables.player_match_roles("matchId");

--transfers 
CREATE INDEX IF NOT EXISTS idx_transfers_player ON wyscout.transfers("playerId");

CREATE INDEX IF NOT EXISTS idx_transfers_type ON wyscout.transfers("type");

CREATE INDEX IF NOT EXISTS idx_transfers_start_date ON wyscout.transfers("startDate");


--scaling_attributes

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_player ON derived_tables.scaling_attributes("playerId");

CREATE INDEX IF NOT EXISTS idx_scaling_attributes_match ON derived_tables.scaling_attributes("matchId");


--team_match_rating
CREATE INDEX IF NOT EXISTS idx_team_match_rating_team ON derived_tables.team_match_rating("teamId");

CREATE INDEX IF NOT EXISTS idx_team_match_rating_match ON derived_tables.team_match_rating("matchId");

CREATE INDEX IF NOT EXISTS idx_team_match_rating_date ON derived_tables.team_match_rating("date");

--team_match_rating

CREATE INDEX IF NOT EXISTS idx_team_rating_team ON derived_tables.team_rating("teamId");


--transfermarkt.tm_transfers
CREATE INDEX IF NOT EXISTS idx_tm_transfers_tm_id ON transfermarkt.tm_transfers("tm_player_id");


--player_match_info_mv

CREATE INDEX IF NOT EXISTS idx_pmi_player ON wyscout.player_match_info("playerId");

CREATE INDEX IF NOT EXISTS idx_pmi_adv_match ON wyscout.player_match_info("matchId");

CREATE INDEX IF NOT EXISTS idx_pmi_adv_comp ON wyscout.player_match_info("competitionId");

CREATE INDEX IF NOT EXISTS idx_pmi_adv_season ON wyscout.player_match_info("seasonId");

CREATE INDEX IF NOT EXISTS idx_pmi_adv_minutes ON wyscout.player_match_info("minutesTagged");

CREATE INDEX IF NOT EXISTS idx_pmi_adv_date ON wyscout.player_match_info("date");


--fixtures
CREATE INDEX IF NOT EXISTS idx_fixtures_match ON wyscout.fixtures("matchId");

CREATE INDEX IF NOT EXISTS idx_fixtures_season ON wyscout.fixtures("seasonId");

CREATE INDEX IF NOT EXISTS idx_fixtures_date ON wyscout.fixtures("date");

CREATE INDEX IF NOT EXISTS idx_fixtures_status ON wyscout.fixtures("status");

CREATE INDEX IF NOT EXISTS idx_fixtures_home_team ON wyscout.fixtures("home_teamId");

CREATE INDEX IF NOT EXISTS idx_fixtures_away_team ON wyscout.fixtures("away_teamId");


