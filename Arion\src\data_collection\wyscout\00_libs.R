library(readr)
library(dplyr)
library(tidyr)
library(readxl)
library(RPostgreSQL)
library(jsonlite)
library(httr)
library(snakecase)

con_db_name = 'DEVELOPMENT_DB'
wyscout_username = Sys.getenv('USR')
wyscout_pass = Sys.getenv('PASS')
make_connection = function(con_db = con_db_name){
  drv <- dbDriver("PostgreSQL")
  # creates a connection to the postgres database
  # note that "con" will be used later in each connection to the database
  
  con <- dbConnect(drv, dbname = Sys.getenv(con_db),
                   host = Sys.getenv("HOST"), port = 5432,
                   user = Sys.getenv("USR"), password = Sys.getenv("PASS"))
  con
}


remove_null = function(x){
  null_idx = which(sapply(x, is.null))
  if(length(null_idx)>0){
    x[null_idx] = NA}
  x
}

get_p_value = function(x, side = 'one sided'){
  x[is.nan(x)] = NA
  x[is.infinite(x)] = NA
  if(side == 'one sided'){
    pval = ifelse(!is.na(x), 1 - 2*pnorm(-abs(scale(x, center = F))), NA)
  }else{
    pval = ifelse(!is.na(x),
                  ifelse(x>mean(x, na.rm = T), 2*pnorm(-abs(scale(x, center = F))), 1 - 2*pnorm(-abs(scale(x, center = F)))), NA)
  }
  
  pval
}

get_p_value2 = function(x, n){
  x = (x - mean(x, na.rm = T))/(sd(x, na.rm = T)/sqrt(n))
  pval = pnorm(x)
  pval
}

gm_mean = function(x, na.rm=TRUE, zero.propagate = FALSE, replace_zero = FALSE){
  if(any(x < 0, na.rm = TRUE)){
    return(NaN)
  }
  if(zero.propagate){
    if(any(x == 0, na.rm = TRUE)){
      return(0)
    }
    exp(mean(log(x), na.rm = na.rm))
  } else {
    if(!replace_zero){
      x = x
    }else{
      x[x==0] = x[x==0] + 0.0001
    }
    exp(sum(log(x[x > 0]), na.rm=na.rm) / length(x))
    
  }
}

get_distance_to_target = function(df, target_df, wts = rep(1, ncol(df))){
  df = data.frame(df)
  target_df = data.frame(target_df)
  wts = wts[colnames(df)]
  distance = gower_dist(df, target_df, weights = wts)
  distance
}

flatten_squad_data = function(x){
  birth_area = data.frame(x[['birthArea']], stringsAsFactors = F)
  colnames(birth_area) = paste('birthArea', colnames(birth_area), sep = '_')
  
  pass_area = data.frame(x[['passportArea']], stringsAsFactors = F)
  colnames(pass_area) = paste('passportArea', colnames(pass_area), sep = '_')
  
  role = data.frame(x[['role']], stringsAsFactors = F)
  colnames(role) = paste('role', colnames(role), sep = '_')
  
  x = x[setdiff(names(x), c('birthArea', 'passportArea', 'role'))]
  null_fields = which(sapply(x, is.null))
  x[null_fields] = NA
  x = data.frame(x, stringsAsFactors = F)
  x = cbind(x, birth_area, pass_area, role)
  x
  
}

fix_lineup_passport_data = function(lnp_ins, rel_cname = 'passportArea'){
  lnp_ins[['player']][[rel_cname]][sapply(lnp_ins[['player']][[rel_cname]], is.null)] = NA
  
  lnp_ins
}

flatten_coach_data = function(x){
  birth_area = data.frame(x[['birthArea']], stringsAsFactors = F)
  colnames(birth_area) = paste('birthArea', colnames(birth_area), sep = '_')
  
  pass_area = data.frame(x[['passportArea']], stringsAsFactors = F)
  colnames(pass_area) = paste('passportArea', colnames(pass_area), sep = '_')
  
  
  x = x[setdiff(names(x), c('birthArea', 'passportArea', 'role'))]
  null_fields = which(sapply(x, is.null))
  x[null_fields] = NA
  x = data.frame(x, stringsAsFactors = F)
  x = cbind(x, birth_area, pass_area)
  x
  
}

fix_spellings = function(x){
  Encoding(x) = 'UTF-8'
  x = stringi::stri_trans_general(x, "Any-Latin")
  x = textclean::replace_non_ascii(x)
  x = gsub('[[:punct:]]+','',x)
  x = gsub('[0-9]+', '', x)
  x
}

get_position_variables = function(main_df, target_position){
  pos_variable_weights = main_df[main_df['Target Position'] == target_position, ]
  pos_attributes_to_engineer = pos_variable_weights[pos_variable_weights['Existing or derived'] == 'Derived', ]
  pos_attributes_to_use_asis = pos_variable_weights[pos_variable_weights['Existing or derived'] != 'Derived', ]
  return(list(pos_attributes_to_engineer, pos_attributes_to_use_asis))
  
}

get_shots_saved_str = function(df){
  df['shots_saved_ratio'] = df[['gksaves']] / df[['gkshotsagainst']]
  
  df_features = df %>%
    group_by(playerid) %>%
    summarise(avg_shots_saved = mean(shots_saved_ratio[!is.nan(shots_saved_ratio)&
                                                         !is.na(shots_saved_ratio)], na.rm = T),
              total_games_shots = sum(gkshotsagainst>0, na.rm = T),
              total_shots = sum(gkshotsagainst, na.rm = T))
  
  df_features['shots_saved_pval_regular'] = round(get_p_value(df_features$avg_shots_saved), 2)
  #df_features['shots_saved_str_sample_size'] = round(get_p_value2(df_features$avg_shots_saved, df_features$total_games_shots), 2)
  df_features['shots_saved_experience'] = round(get_p_value(df_features$total_games_shots), 2)
  df_features['shots_saved_str'] = df_features[['shots_saved_pval_regular']] * df_features[['shots_saved_experience']]
  return(df_features)
}

get_stat_str = function(df, success_stat_var, overall_stat_var, experience_var, scale_by_mins = T, pval_side){
  rat_var = paste(overall_stat_var, 'won_ratio', sep = '_')
  
  if(scale_by_mins){
    df[[overall_stat_var]] = df[[overall_stat_var]] * 90 / df[['minutesTagged']]
    df[[success_stat_var]] = df[[success_stat_var]] * 90 / df[['minutesTagged']]
    
    if(experience_var != overall_stat_var){
      df[[experience_var]] = df[[experience_var]]* 90 / df[['minutesTagged']]
    }
  }
  
  df[[overall_stat_var]][is.nan(df[[overall_stat_var]])] = NA
  df[[overall_stat_var]][is.infinite(df[[overall_stat_var]])] = NA
  
  df[[success_stat_var]][is.nan(df[[success_stat_var]])] = NA
  df[[success_stat_var]][is.infinite(df[[success_stat_var]])] = NA
  
  df[[experience_var]][is.nan(df[[experience_var]])] = NA
  df[[experience_var]][is.infinite(df[[experience_var]])] = NA
  
  df[rat_var] = df[[success_stat_var]] /df[[overall_stat_var]]
  
  df[[rat_var]][is.nan(df[[rat_var]])] = NA
  df[[rat_var]][is.infinite(df[[rat_var]])] = NA
  
  df_features = df %>%
    group_by(playerId) %>%
    summarise(avg_ratio = mean(get(rat_var), na.rm = T),
              avg_num = mean(get(overall_stat_var), na.rm = T),
              total_games_event = sum(get(overall_stat_var)>0, na.rm = T),
              total_num = n(),
              total_experience = sum(get(experience_var), na.rm = T))
  
  df_features[['avg_ratio']][is.nan(df_features[['avg_ratio']])] = NA
  df_features[['avg_ratio']][is.infinite(df_features[['avg_ratio']])] = NA
  
  df_features = data.frame(df_features)
  colnames(df_features) = c('playerId', paste(c('avg_ratio', 'avg_num', 'total_games'), 
                                              overall_stat_var, sep = '_'), 'total_games', paste('total', experience_var, sep = '_'))
  
  df_features[paste(overall_stat_var, 'pval_regular', sep = '_')] = round(get_p_value(df_features[[2]], pval_side), 2)
  df_features[paste(overall_stat_var, 'experience', sep = '_')] = round(get_p_value(df_features[,6], pval_side), 2)
  df_features[paste(overall_stat_var, 'str', sep = '_')] = df_features[[paste(overall_stat_var, 'pval_regular', sep = '_')]] *
    df_features[[paste(overall_stat_var, 'experience', sep = '_')]]
  str_var = paste(overall_stat_var, 'str', sep = '_')
  df_features[[str_var]][is.na(df_features[[str_var]]) | is.nan(df_features[[str_var]])] = 0
  #df_features[is.nan(df_features)] = 0
  return(df_features)
}

get_aerial_duels = function(df){
  df['aerial_duels_won_ratio'] = df[['aerialduelswon']] /df[['aerialduels']]
  df_features = df %>%
    group_by(playerid) %>%
    summarise(avg_aerial_duel_won = mean(aerial_duels_won_ratio, na.rm = T),
              avg_num_aerial_duels = mean(aerialduels, na.rm = T),
              total_games_aerial_duels = sum(aerialduels>0, na.rm = T),
              total_games = n(),
              total_num_aerial_duels = sum(aerialduels, na.rm = T))
  
  df_features['aerial_duels_pval_regular'] = round(get_p_value(df_features$avg_aerial_duel_won), 2)
  df_features['aerial_duels_experience'] = round(get_p_value(df_features$total_num_aerial_duels), 2)
  df_features['aerial_duels_str'] = df_features[['aerial_duels_pval_regular']] * df_features[['aerial_duels_experience']]
  #df_features[is.nan(df_features)] = 0
  return(df_features)
}

get_exits = function(df){
  df['exits_won_ratio'] = df[['gksuccessfulexits']] /df[['gkexits']]
  df_features = df %>%
    group_by(playerid) %>%
    summarise(avg_exits_won = mean(exits_won_ratio, na.rm = T),
              avg_num_exits = mean(gkexits, na.rm = T),
              total_games_exits = sum(gkexits>0, na.rm = T),
              total_games = n(),
              total_num_exits = sum(gkexits, na.rm = T))
  
  df_features['exits_pval_regular'] = round(get_p_value(df_features$avg_aerial_duel_won), 2)
  df_features['exits_experience'] = round(get_p_value(df_features$total_num_aerial_duels), 2)
  df_features['exits_str'] = df_features[['aerial_duels_pval_regular']] * df_features[['aerial_duels_experience']]
  #df_features[is.nan(df_features)] = 0
  return(df_features)
}

get_pass_ratios = function(df){
  df['passes_won_ratio'] = df[['successfulpasses']] /df[['passes']]
  df['long_passes_won_ratio'] = df[['successfulongpasses']] /df[['longpasses']]
  df_features = df %>%
    group_by(playerid) %>%
    summarise(avg_passes_won = mean(passes_won_ratio, na.rm = T),
              avg_num_passes = mean(passes, na.rm = T),
              total_games_passes = sum(passes>0, na.rm = T),
              total_games = n(),
              total_num_passes = sum(passes, na.rm = T),
              avg_longpasses_won = mean(long_passes_won_ratio, na.rm = T),
              avg_num_longpasses = mean(longpasses, na.rm = T),
              total_games_longpasses = sum(longpasses>0, na.rm = T),
              total_num_longpasses = sum(longpasses, na.rm = T))
  
  df_features['passes_pval_regular'] = round(get_p_value(df_features$passes_won_ratio), 2)
  df_features['passes_experience'] = round(get_p_value(df_features$total_num_passes), 2)
  df_features['passes_str'] = df_features[['passes_pval_regular']] * df_features[['passes_experience']]
  
  df_features['longpasses_pval_regular'] = round(get_p_value(df_features$long_passes_won_ratio), 2)
  df_features['longpasses_experience'] = round(get_p_value(df_features$total_num_longpasses), 2)
  df_features['longpasses_str'] = df_features[['longpasses_pval_regular']] * df_features[['longpasses_experience']]
  #df_features[is.nan(df_features)] = 0
  return(df_features)
  
}
get_events_df = function(evts){
  evt = evts[['events']]
  pos_df = plyr::ldply(evt$positions, get_start_end_positions)
  tag_df = plyr::ldply(evt$tags, get_event_tags)
  events_df = evt[c('id', 'playerId', 'teamId', 'matchId', 'matchPeriod', 'eventSec', 'eventId', 'eventName', 'subEventId', 'subEventName')]
  events_df = cbind(events_df, pos_df, tag_df)
  events_df
}

get_start_end_positions = function(pos){
  if(nrow(pos) ==2){
    pos_df = data.frame(x_start = pos[1, 1],
                        x_end = pos[2, 1],
                        y_start = pos[1, 2],
                        y_end = pos[2, 2])
  }else{
    pos_df = data.frame(x_start = pos[1, 1],
                        x_end = NA,
                        y_start = pos[1, 2],
                        y_end = NA)
  }
  
  pos_df
  
}

get_formations = function(evts) {
  formations = list()
  formation = evts$formations
  mid = unique(evts$events$matchId)
  
  for (k in 1:length(formation)) {
    forms = formation[[k]]
    tm = names(formation)[k]
    for (tm_form in forms) {
      for (j in 1:length(tm_form)) {
        tmp_data = data.frame(tm_form[[j]][[1]][c('scheme', 'matchPeriod', 'startSec')], stringsAsFactors = F)
        tmp_player_data = tm_form[[j]][[1]][c('players')]
        tmp_player_data = do.call(plyr::rbind.fill, tmp_player_data[[1]])
        tmp_player_data = tmp_player_data[!is.na(tmp_player_data$playerId),]
        
        tmp_data['teamId'] = tm
        tmp_data['matchId'] = mid
        
        tmp_player_data['teamId'] = tm
        tmp_player_data['matchId'] = mid
        
        tmp_data = tmp_player_data %>%
          left_join(tmp_data)%>%
          mutate(matchPeriod = ifelse(
            matchPeriod == '1H',
            1,
            ifelse(
              matchPeriod == '2H',
              2,
              ifelse(
                matchPeriod == 'E1',
                3,
                ifelse(matchPeriod == 'E2',
                       4,
                       ifelse(matchPeriod == 'P', 5, NA))
              )
            )
          )) %>%
          data.frame()
        
        formations[[length(formations) + 1]] = tmp_data
      }
    }
    
  }
  formations = do.call(rbind, formations)
  formations
}


get_event_tags = function(tag, available_event_tags = tag_list_for_events){
  tag_df = matrix(as.numeric(NA), ncol = nrow(available_event_tags), nrow = 1)
  tag_df = data.frame(tag_df, stringsAsFactors = F)
  colnames(tag_df) = available_event_tags[['Label']]
  colmns = colnames(tag_df)
  colmns = tolower(colmns)
  colmns = gsub(' ', '_', colmns)
  colmns = gsub('/', '_', colmns, fixed = T)
  colnames(tag_df) = colmns
  #tag_df = NULL
  if(nrow(tag)>0){
    colmns = tag[,2][['label']]
    colmns = tolower(colmns)
    colmns = gsub(' ', '_', colmns)
    colmns = gsub('/', '_', colmns, fixed = T)
    tag_df[colmns] = 1
  }
  
  tag_df
}


get_event_lineup = function(event_list){
  event_list = as.list(event_list)
  pllist = as.list(event_list[[1]][['players']])
  if(length(pllist) == 1) pllist = pllist[[1]]
  stln_pl = plyr::ldply(pllist, function(x) x[complete.cases(x),])
  stln_pl = stln_pl[c('playerId', 'position')]
  stln_pl['scheme'] = event_list[[1]][['scheme']]
  stln_pl['matchPeriod'] = event_list[[1]][['matchPeriod']]
  stln_pl['startSec'] = event_list[[1]][['startSec']]
  stln_pl
}

get_event_lineup_fh = function(event_list){
  pllist = as.list(event_list[[1]][['players']])
  stln_pl = plyr::ldply(pllist[[1]], function(x) x[complete.cases(x),])
  stln_pl = stln_pl[c('playerId', 'position')]
  stln_pl['scheme'] = event_list[[1]][['scheme']]
  stln_pl['matchPeriod'] = event_list[[1]][['matchPeriod']]
  stln_pl['startSec'] = event_list[[1]][['startSec']]
  stln_pl
}

get_sub_events = function(tmp_subs){
  pin = NA
  pout = NA
  tmp_subs = lapply(tmp_subs, remove_null)
  if('in' %in% names(tmp_subs)) pin = tmp_subs[['in']][['playerId']]
  if('out' %in% names(tmp_subs)) pout = tmp_subs[['out']][['playerId']]
  
  if(length(pin) > length(pout)) pout = c(pout, rep(NA, (length(pin) - length(pout))))
  if(length(pout) > length(pin)) pin = c(pin, rep(NA, (length(pout) - length(pin))))
  
  tmp_subs_df = data.frame(playerIn = pin,
                           playerOut = pout)
  tmp_subs_df
}

disk.usage <- function(path = '/mnt/disks/data-disk') {
  if(length(system("which df", intern = TRUE, ignore.stderr = TRUE))) {
    cmd <- sprintf("df %s", path)
    exec <- system(cmd, intern = TRUE, ignore.stderr = TRUE)
    exec <- strsplit(exec[length(exec)], "[ ]+")[[1]]
    exec <- as.numeric(exec[3:4])
    structure(exec, names = c("used", "available"))
  } else {
    stop("'df' command not found")
  }
}

scale_within_range = function(x, max_v, min_v){
  x = (x - min(x, na.rm = T)) / (max(x, na.rm = T) - min(x, na.rm = T))
  x = (max_v - min_v) * x + min_v
  x
}

get_relevant_competitions <- function(qry, con, start_date, end_date){
  
  qry_temp = gsub('minim_date', paste0("'", start_date, "'"), qry)
  qry_temp = gsub('max_date', paste0("'", end_date, "'"), qry_temp)
  
  df = dbGetQuery(con, qry_temp)
  
  df
}

get_matches <- function(qry, con, start_date, end_date){
  qry_temp = gsub('minim_date', paste0("'", start_date, "'"), qry)
  qry_temp = gsub('max_date', paste0("'", end_date, "'"), qry_temp)
  
  df = dbGetQuery(con, qry_temp)
  
  base_year = year(start_date)
  
  df['date'] = as.POSIXct(as.Date(df[['date']]))
  df['year'] = year(df[['date']])
  df['week_of_year'] = week(df[['date']])
  
  df = df %>%
    rename(Score = home_pts) %>%
    mutate(period = (year - base_year) * 52 + week_of_year)
  
  df
  
}

get_initialization = function(competition_df, min_value, max_value, root_n = root_n){
  competition_df['init_value'] = competition_df[['mean_player_value']] ^ root_n
  
  competition_df['init_value'] = scale_within_range(competition_df[['init_value']], max_value, min_value)
  competition_df
}

get_ratings = function(competition_df, match_df, unknown_rating, kfac, dev = NA, alg = 'elo', get_history = F){
  rating_values = NULL
  init_df = competition_df %>%
    filter(!is.na(init_value)) %>%
    select(teamId, init_value) %>%
    rename(Player = teamId,
           Rating = init_value)
  # print(head(match_df))
  relevant_matches = match_df %>%
    select(period, home_id, away_id, Score)
  
  if(alg == 'elo'){
    rating_values = elo(relevant_matches, status = init_df, init = unknown_rating, kfac = kfac, history = get_history)
  }else if(alg == 'glicko'){
    if(is.na(dev)) stop('Deviation needed for Glicko')
    init_df['Deviation'] = dev
    rating_values = glicko(relevant_matches, status = init_df, init = c(unknown_rating, dev), history = get_history)
  }else{
    stop('Wrong Algorithm input')
  }
  
  # df = rating_values$ratings %>%
  #   rename(teamId = Player)
  # 
  # df = df %>%
  #   filter(!duplicated(teamId))
  # 
  # df
  
  rating_values
  
}

get_smoothed_values <- function(x, window_size = 3, min_length = 15){
  if(length(x) >= min_length){
    sm_rating = zoo::rollmean(x, window_size, fill = NA, align = 'right')
    sm_rating[is.na(sm_rating)] = x[1:(window_size-1)]
  }else{
    sm_rating = x
  }
  sm_rating
}

