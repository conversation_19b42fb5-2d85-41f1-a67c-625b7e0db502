import traceback

from sqlalchemy.engine import create_engine

from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import TransfersPrep
from src.helper_funcs import fast_read_sql, fast_write_sql


def main():
    engine = create_engine(postgres_prod_str)
    SCHEMA = "transfermarkt_players"
    prep = TransfersPrep()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.raw_transfers_table", engine)
    connection = engine.raw_connection()
    cursor = connection.cursor()
    try:
        cursor.execute("DELETE FROM transfermarkt_players.tm_transfers;")
        fast_write_sql(
            prep.clean_df(df),
            "tm_transfers",
            cnx=engine,
            if_exists="append",
            grant=True,
            schema="transfermarkt_players",
            cursor=cursor,
            connection=connection,
            transaction=True,
        )
        # execute the query in transaction since we are inserting in place now:
        new_types_query = open("src/queries/map_transfer_types.sql").read()
        cursor.execute(new_types_query)
        cursor.execute(f"DELETE FROM {SCHEMA}.raw_transfers_table")
        connection.commit()
        cursor.close()

    except Exception as e:
        connection.rollback()
        cursor.close()
        print(traceback.format_exc())
        raise Exception()

if __name__ == "__main__":
    main()
