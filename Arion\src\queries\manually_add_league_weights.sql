WITH cte AS (
    SELECT
        ctid,
        row_number() OVER (
            PARTITION BY "competitionId"
            ORDER BY
                "competitionId"
        ) rn
    FROM
        competition_weights
)
DELETE FROM
    competition_weights USING cte
WHERE
    cte.rn > 1
    AND cte.ctid = competition_weights.ctid;

INSERT INTO
    competition_weights ("competitionId", mean_player_value)
VALUES
    (617, 1510000),
    (295, 492000),
    (886, 181000),
    (750, 556000),
    (287, 1170000),
    (551, 356000),
    (301, 128000),
    (152, 298000),
     (591, 150000),
    (1710, 161000),
    (483, 69000),
    (503, 32000),
    (867, 339000),
    (789, 260000);

UPDATE
    competition_weights
SET
    league_weight = cbrt(mean_player_value) / (
        SELECT
            MAX(cbrt(mean_player_value))
        FROM
            competition_weights
    );
GRANT ALL ON TABLE public.competition_weights TO daniel, elvan, kliment WITH GRANT OPTION;
