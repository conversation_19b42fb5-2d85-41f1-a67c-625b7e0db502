--bulgarians currently playing in a non-bg team
select distinct on ("playerId") b.* , tm.tm_player_url from (
select  p.*, t.name as "team_name", t."teamId", t."officialName" as "official_team_name", t.area_name as "team_country",  t."divisionLevel" from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where  p."passportArea_name" = 'Bulgaria'
and p.status = 'active'
and p."currentTeamId" is not null
and coalesce (t.area_name, 'no') != 'Bulgaria'
and t.name is not null
) b
left join transfermarkt.tm_to_ws_ids tm using("playerId")


--bulgarian not retired, without a team with last to non-bg team
select distinct on (a."playerId") a.*, tt."officialName" as "official_team_name", tt.area_name as "team_country", tt."divisionLevel", a.tm_player_url   from (select  * from (
select p.*from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where 
 p.status = 'active'
 and  p."passportArea_name" = 'Bulgaria'
 and p."currentTeamId" is null
--and t.area_name != 'Bulgaria'
) b
left join wyscout.transfers tr using("playerId")
left join transfermarkt.tm_to_ws_ids tm  using("playerId")
order by b."playerId", tr."startDate" desc)a,
wyscout.team_info tt
where a.type = 'Free Agent' 
and a."fromTeamId" = tt."teamId"
and tt.area_name != 'Bulgaria'
order by a."playerId", a."birthDate" desc nulls last



--active but last transfers is bulgaria
select * from (select distinct on (b."playerId")  * from (
select * from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where 
 p.status = 'active'
--and t.area_name != 'Bulgaria'
) b
left join wyscout.transfers tm using("playerId")
order by b."playerId", tm."startDate" desc)a
where a.type= 'Retired' 
order by a."playerId", a."birthDate" desc nulls last



--retired but last transfer is not retired
select * from (select distinct on (b."playerId")  * from (
select * from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where 
 p.status = 'retired'
--and t.area_name != 'Bulgaria'
) b
left join wyscout.transfers tm using("playerId")
order by b."playerId", tm."startDate" desc)a
where a.type != 'Retired'
order by a."birthDate" desc nulls last

--create view wyscout.player_info as 
select * from (select distinct on (b."playerId")  * from (
select * from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where 
 p.status = 'retired'
--and t.area_name != 'Bulgaria'
) b
left join wyscout.transfers tm using("playerId")
order by b."playerId", tm."startDate" desc)a
where a.type != 'Retired'
order by a."birthDate" desc nulls last


-- bulgarians playing abroad according to tm and not present in the wyscout list
select bg.* from transfermarkt.bg_tm_players bg 
left join transfermarkt.tm_to_ws_ids ws
on bg.player_id::int = ws.tm_player_id 
--left join wyscout.players p on concat(p."firstName" || ' ' || p."lastName" ) = bg.name
--where ws.tm_player_id is null
left join (
select  p.*, t.name as "team_name", t."teamId", t."officialName" as "official_team_name", t.area_name as "team_country",  t."divisionLevel" from 
wyscout.players p
left join wyscout.team_info t on t."teamId" = p."currentTeamId" 
where  p."passportArea_name" = 'Bulgaria'
and p.status = 'active'
and coalesce (t.area_name, 'no') != 'Bulgaria'
--and t.name is not null
)b on  bg.name = concat(b."firstName" || ' ' || b."lastName" ) and bg.birth_date = b."birthDate"
left join transfermarkt.tm_to_ws_ids tm on(ws."playerId" = tm."playerId")
where b."firstName" is  null
