GRANT SELECT ON ALL TABLES IN SCHEMA public  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, lookup_service;
GRANT SELECT ON ALL TABLES IN SCHEMA wyscout  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, ff_usr, lookup_service, quicksilver_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA transfermarkt  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, lookup_service, ff_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA meta  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT SELECT ON ALL TABLES IN SCHEMA derived_tables  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, ff_usr, lookup_service, quicksilver_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA instat  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT SELECT ON ALL TABLES IN SCHEMA statsbomb  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT SELECT ON ALL TABLES IN SCHEMA config  TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA wyscout TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA transfermarkt TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, lookup_service;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA meta TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA derived_tables TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, lookup_service;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA instat TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA statsbomb TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA config TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;

GRANT CREATE ON SCHEMA public TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA wyscout TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA transfermarkt TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA meta TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA derived_tables TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation, ff_usr;
GRANT CREATE ON SCHEMA instat TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA statsbomb TO player_role_service, ranks_api_usr, brexit_service, devin_gardner, rado, playervaluation;
GRANT CREATE ON SCHEMA config TO player_role_service, brexit_service, devin_gardner, rado, playervaluation;
GRANT ALL ON SCHEMA config TO ranks_api_usr;
GRANT ALL ON SCHEMA wyscout TO quicksilver_usr;
GRANT ALL ON SCHEMA derived_tables TO quicksilver_usr;




