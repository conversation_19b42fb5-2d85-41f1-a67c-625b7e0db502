import asyncio

from src.data_collection.wyscout.v2.TeamsUpdater import TeamsUpdater
from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.UpdatesChecker import UpdatesChecker


async def main():
    from_scratch = False  # here this is false because we are never updating teams, players, matches from scratch
    teams_checker = UpdatesChecker(
        table_name="teams_for_collection",
        object_type="teams",
        id_name="teamId",
        from_scratch=from_scratch,
    )
    teams_updater = TeamsUpdater()
    orc = Orchestrator(
        batch_size=1000,
        updates_checker=teams_checker,
        updater=teams_updater,
        from_scratch=from_scratch,
    )
    await orc.loop()


if __name__ == "__main__":
    asyncio.run(main())
