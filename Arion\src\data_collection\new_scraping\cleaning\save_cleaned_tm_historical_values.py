from sqlalchemy.engine import create_engine
from sqlalchemy.types import Date

from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import HistoricalValuesPrep
from src.helper_funcs import fast_read_sql
from src.helper_funcs import fast_write_sql


engine = create_engine(postgres_prod_str)
SCHEMA = "meta_scraping"


def main():
    prep = HistoricalValuesPrep()
    df = fast_read_sql(f"SELECT * FROM {SCHEMA}.tm_historical_values", engine)

    try:
        #fast_write_sql(
        #    prep.clean_df(df),
        #    "tm_historical_values",
        #    cnx=engine,
        #    if_exists="replace",
        #    schema="transfermarkt",
        #    dtype={x: Date for x in prep.DATE_COLUMNS},
        #)
        prep.clean_df(df).to_sql(name='tm_historical_values', con=engine, schema='transfermarkt', index=False, if_exists="replace", chunksize=1000, method='multi')
    except Exception as e:
        raise (e)


if __name__ == "__main__":
    main()