# Match transfer info to teams from wyscout
source('src/00_libs.R')
library(lubridate)
library(stringdist)
library(countrycode)

substrRight <- function(x, n){
  substr(x, nchar(x)-n+1, nchar(x))
}

compute_transfer_season <- function(date_array){
  date_month <- lubridate::month(date_array)
  date_year <- lubridate::year(date_array)
  
  season_name = ifelse(date_month < 5, paste(date_year-1, date_year, sep = '/'), paste(date_year, date_year+1, sep = '/'))
  season_name
}

fix_season_names = function(season_names){
  season_names = gsub('2020', '2x', season_names, fixed = T)
  season_names = gsub('20', '', season_names, fixed = T)
  season_names = gsub('2x', '20', season_names, fixed = T)
  season_names
}


transfer_df = dbReadTable(con, 'tm_transfers')
transfer_df = unique(transfer_df)

# transfer_df$joined = gsub('Inter ', 'Internazionale ', transfer_df$joined)
ws_transfers = dbReadTable(con, 'transfers')
# transfer_df = transfer_df %>%
#   filter(grepl('transfer', type, ignore.case = T))
teams_df = dbGetQuery(
  con,
  'select distinct "teamId", "name", "officialName", "area_id", "area_alpha3code", "area_name" from seasons_teams where "gender" = \'male\''
)
teams_df = teams_df[!duplicated(teams_df$teamId),]
teams_df_wc = data.frame(teamId = 0, 
                         name = 'Without Club', 
                         officialName = 'Without Club')
teams_df = plyr::rbind.fill(teams_df,teams_df_wc )
matched_players = dbGetQuery(con, 'select distinct "playerId", "tm_player_id" from tm_to_ws_ids')

ws_transfers = ws_transfers %>%
  select(-c('toTeamName', 'fromTeamName'))

ws_transfers = ws_transfers %>%
  left_join(teams_df[c('teamId', 'name', 'officialName')], by = c('fromTeamId' = 'teamId')) %>%
  rename('fromTeamName' = 'name',
         'fromOfficialTeamName' = 'officialName')%>%
  left_join(teams_df[c('teamId', 'name', 'officialName')], by = c('toTeamId' = 'teamId')) %>%
  rename('toTeamName' = 'name',
         'toOfficialTeamName' = 'officialName')

# Fix encodings on Windows ----
if(Sys.info()['sysname'] == 'Windows'){
  
  transfer_df$left <-
    iconv(
      transfer_df$left,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  transfer_df$joined <-
    iconv(
      transfer_df$joined,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  teams_df$name <-
    iconv(
      teams_df$name,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  teams_df$officialName <-
    iconv(
      teams_df$officialName,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  teams_df$area_name <-
    iconv(
      teams_df$area_name,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  
  ws_transfers$fromTeamName <- 
    iconv(
      ws_transfers$fromTeamName,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  
  ws_transfers$toTeamName <- 
    iconv(
      ws_transfers$toTeamName,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  
  ws_transfers$fromOfficialTeamName <- 
    iconv(
      ws_transfers$fromOfficialTeamName,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  
  ws_transfers$toOfficialTeamName <- 
    iconv(
      ws_transfers$toOfficialTeamName,
      from = "UTF-8",
      to = "Windows-1252",
      sub = NA,
      mark = TRUE,
      toRaw = FALSE
    )
  
}

print('Manually Fix Internazionale and PSG')
transfer_df$left[transfer_df$left == 'Inter'] = 'Internazionale'
transfer_df$left = gsub('PSG', 'Paris Saint-Germain', transfer_df$left)
transfer_df$joined[transfer_df$joined == 'Inter'] = 'Internazionale'
transfer_df$joined = gsub('PSG', 'Paris Saint-Germain', transfer_df$joined)


matched_ws_transfers = ws_transfers %>%
  select(-type) %>%
  inner_join(matched_players)

transfer_df = transfer_df %>%
  inner_join(matched_players)


fix_names = function(x){
  unique_teams_first = gsub('1.FC', '', x, fixed = T)
  unique_teams_first = gsub('1. FC', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1. SC', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1.FSV', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1.HFK', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1.JFS', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1.SC', '', unique_teams_first, fixed = T)
  unique_teams_first = gsub('1.', '', unique_teams_first, fixed = T)
  
  unique_teams_first = trimws(unique_teams_first, which = 'both', whitespace = "[ \t\r\n]")
  
  unique_teams_first
}

find_and_remove_youth = function(x){
  
  youth_exists = grepl('u[[:digit:]]+', tolower(x)) | 
    grepl('under [[:digit:]]+', tolower(x)) |
    grepl(' youth', tolower(x), fixed = T) | 
    grepl(' yth', tolower(x), fixed = T) 
  
  x = gsub('u[[:digit:]]+', '', tolower(x))
  x = gsub('under [[:digit:]]+', '', tolower(x))
  x = gsub(' youth', '', tolower(x), fixed = T)
  x = gsub(' yth.', '', tolower(x), fixed = T)
  x = gsub(' yth', '', tolower(x), fixed = T)
  
  list(fixed_team = x, youth_exists = youth_exists)
  
  
}

get_name_subset = function(x){
  if(!is.na(x)){
    x = gsub('[[:punct:]]+','',x)
    x = strsplit(x, split = ' ')
    x_lengths = sapply(x, nchar)
    if(any(x_lengths > 3)){
      x = x[[1]][x_lengths>3]
    }else{
      x = x[[1]][which.max(x_lengths)]
    }
    
    x = paste(x, collapse = ' ')
  }
  
  x
}

get_string_distances = function(x, str_source){
  x = tolower(x)
  str_source = tolower(str_source)
  
  # Fix the search string
  x = fix_names(x)
  x_youth = find_and_remove_youth(x)
  if(x_youth[[2]]){
    x = paste(x, 'u19')
  }
  x = get_name_subset(x)
  
  # Retain the youth matching teams from source
  source_youth = find_and_remove_youth(str_source)[[2]]
  str_source = str_source[source_youth == x_youth[[2]]]
  str_source = sapply(str_source, get_name_subset)
  
  dst = 0 
  
  dst = stringdistmatrix(x, str_source)
  # dst = gm_mean(dst, zero.propagate = T)
  
  dst = as.numeric(dst)
  names(dst) = names(str_source)
  dst
}

get_ws_ids_from_tm_name <- function(x, str_source){
  dst = get_string_distances(x, str_source)
  dst = dst[!is.na(dst)]
  if(any(dst == 0)){
    dst = dst[dst==0]
    ws_id = as.numeric(unique(names(dst)))[1]
  }else{
    ws_id = NA
  }
  ws_id
}

get_team_name_distances = function(x){
  # ws_joined_names = c(strsplit(x[['toTeamName_subset']], split = ' ')[[1]],
  #                     strsplit(x[['toOfficialTeamName_subset']], split = ' ')[[1]])
  # ws_left_names = c(strsplit(x[['fromTeamName_subset']], split = ' ')[[1]],
  #                     strsplit(x[['fromOfficialTeamName_subset']], split = ' ')[[1]])
  
  ws_joined_names = tolower(c(x[['toTeamName_subset']],
                      x[['toOfficialTeamName_subset']]))
  ws_left_names = tolower(c(x[['fromTeamName_subset']],
                    x[['fromOfficialTeamName_subset']]))
  
  left_contain_dist = NA
  joined_contain_dist = NA
  left_dist = 0 
  joined_dist = 0
  if(!is.na(x[['left_subset']])){
    # if(any(grepl(x[['left_subset']], ws_left_names, ignore.case = T, fixed = T))){
    #   left_contain_dist = 0
    # }
    left_dist = stringdistmatrix(x[['left_subset']], tolower(ws_left_names))
    left_dist = c(as.numeric(left_dist), left_contain_dist)
    left_dist = gm_mean(left_dist, zero.propagate = T)
  }
  
  if(!is.na(x[['joined_subset']])){
    # if(any(grepl(x[['joined_subset']], ws_joined_names, ignore.case = T, fixed = T))){
    #   joined_contain_dist = 0
    # }
    joined_dist = stringdistmatrix(x[['joined_subset']], tolower(ws_joined_names))
    joined_dist = c(as.numeric(joined_dist), joined_contain_dist)
    joined_dist = gm_mean(joined_dist, zero.propagate = T)
  }
  
  list(left_dist, joined_dist)
}

print('Joining TransferMarkt transfers and WyScout transfers of Matched Players and Filtering In 1-day interval transfers')
transfer_df2 = transfer_df %>%
  left_join(matched_ws_transfers)

# # Filter based on dates
transfer_df2 = transfer_df2 %>%
  filter(date >= startDate - as.difftime(1, units = 'days') &
           date <= startDate + as.difftime(1, units = 'days'))


print('Fixing Club Names for Future String Distance Calculation')
transfer_df2$left2 <- fix_names(transfer_df2$left)
transfer_df2$joined2 <- fix_names(transfer_df2$joined)

print('Removing youth and Uxx flags from team names for matching first. This info is appended later. This procedure is applied to both source and target clubs and TM and WS datasets.')
transfer_df2$left2 = find_and_remove_youth(transfer_df2$left2)[[1]]
transfer_df2$joined2 = find_and_remove_youth(transfer_df2$joined2)[[1]]
transfer_df2$left_youth = find_and_remove_youth(transfer_df2$left2)[[2]]
transfer_df2$joined_youth = find_and_remove_youth(transfer_df2$joined2)[[2]]
transfer_df2$left_youth_ws = find_and_remove_youth(transfer_df2$fromOfficialTeamName)[[2]]
transfer_df2$joined_youth_ws = find_and_remove_youth(transfer_df2$toOfficialTeamName)[[2]]

print('Subsetting club names for string distance matrix calculation. (Source and Target clubs in TM & WS datasets)')
transfer_df2$left_subset <- sapply(transfer_df2$left2, get_name_subset)
transfer_df2$joined_subset <- sapply(transfer_df2$joined2, get_name_subset)
transfer_df2$fromTeamName_subset <- sapply(transfer_df2$fromTeamName, get_name_subset)
transfer_df2$toTeamName_subset <- sapply(transfer_df2$toTeamName, get_name_subset)
transfer_df2$fromOfficialTeamName_subset <- sapply(transfer_df2$fromOfficialTeamName, get_name_subset)
transfer_df2$toOfficialTeamName_subset <- sapply(transfer_df2$toOfficialTeamName, get_name_subset)

print('Matching Youth Teams')
transfer_df3 = transfer_df2 %>%
  filter(left_youth == left_youth_ws & joined_youth == joined_youth_ws)

print('Computing distances between source teams and target teams between two datasets')
transfer_df3$left_dist = NA
transfer_df3$joined_dist = NA
for(i in 1:nrow(transfer_df3)){
  distances = get_team_name_distances(transfer_df3[i, ])
  transfer_df3$left_dist[i] = distances[[1]]
  transfer_df3$joined_dist[i] = distances[[2]]
}

print('Appending maximum distance of 100 to source and target club names')
transfer_df3$left_dist[is.nan(transfer_df3$left_dist)] = 100
transfer_df3$joined_dist[is.nan(transfer_df3$joined_dist)] = 100

print('Computing the final total distance')
transfer_df3$total_distance = transfer_df3$left_dist + transfer_df3$joined_dist

print('Retaining minimum distance transfer matches that are less than 200 total distance')
matched_multiples = transfer_df3 %>%
  group_by(date, left, joined, tm_player_id) %>%
  filter(total_distance == min(total_distance, na.rm = T) & total_distance < 200) %>%
  data.frame()

# unmatched_multiples = transfer_df3 %>%
#   filter(!transferId %in% matched_multiples$transferId)

print('Finding unmatched transfers and combining them with matched ones')
transfer_df2 = transfer_df2 %>%
  anti_join(matched_multiples[c('date', 'left', 'joined', 'tm_player_id')])
transfer_df2 = rbind(transfer_df2, matched_multiples[colnames(transfer_df2)])
transfer_df2 = unique(transfer_df2)

# Transfers from TM that are NOT IN WS
print('Retaining TM transfers that are not in WS')
transfer_df = transfer_df %>%
  anti_join(transfer_df2[c('date','left', 'joined', 'tm_player_id')]) %>%
  distinct() %>%
  data.frame()


print('Calculating most frequent team ids for all team names and performing team name matching')
most_freq_team_ids_left = transfer_df2 %>%
  group_by(left, fromTeamId) %>%
  summarise(total_n = n()) %>%
  rename('tm_team' = 'left',
         'ws_id' = 'fromTeamId') %>%
  data.frame()

most_freq_team_ids_joined = transfer_df2 %>%
  group_by(joined, toTeamId) %>%
  summarise(total_n = n()) %>%
  rename('tm_team' = 'joined',
         'ws_id' = 'toTeamId') %>%
  data.frame()

most_freq_team_ids = rbind(most_freq_team_ids_left, most_freq_team_ids_joined)
most_freq_team_ids = most_freq_team_ids%>%
  inner_join(teams_df[c('teamId')], by = c('ws_id' = 'teamId'))

print('Find the most frequent WS id of teams in TM')
most_freq_team_ids_tm_side = most_freq_team_ids %>%
  group_by(tm_team, ws_id) %>%
  summarise(total_n = sum(total_n, na.rm = T)) %>%
  ungroup() %>%
  group_by(tm_team) %>%
  mutate(max_n = max(total_n, na.rm = T))


most_freq_team_ids_ws_side = most_freq_team_ids %>%
  group_by(tm_team, ws_id) %>%
  summarise(total_n = sum(total_n, na.rm = T)) %>%
  ungroup() %>%
  group_by(ws_id) %>%
  mutate(max_n = max(total_n, na.rm = T)) %>%
  arrange(ws_id, desc(total_n))

without_club = data.frame(tm_team = 
                            c('Without Club', 'Unknown', 'Career break', 'Ban'), 
                          ws_id = 0)


print('Find the most frequent TM name for each WS id')
most_freq_team_ids_ws_side = most_freq_team_ids_ws_side %>%
  filter(ws_id > 0) %>%
  filter(total_n == max_n) %>%
  ungroup() %>%
  group_by(tm_team) %>%
  arrange(desc(total_n)) %>%
  slice(1)

print('Assign 0 to unknown and missing teams')
most_freq_team_ids_ws_side = plyr::rbind.fill(most_freq_team_ids_ws_side, without_club)

# most_freq_team_ids_tm_side = most_freq_team_ids_tm_side %>%
#   filter(!tm_team %in% most_freq_team_ids_ws_side$tm_team)

print('Secondary sweep over teams that are not matched initially to find the next closest match')
most_freq_team_ids = most_freq_team_ids %>%
  anti_join(most_freq_team_ids_ws_side[c('tm_team')]) 

most_freq_team_ids_tm_side_secondary = most_freq_team_ids %>%
  filter(ws_id >0) %>%
  group_by(tm_team, ws_id) %>%
  summarise(total_n = sum(total_n, na.rm = T)) %>%
  ungroup() %>%
  group_by(tm_team) %>%
  mutate(max_n = max(total_n, na.rm = T))  %>%
  filter(total_n == max_n)

print('Remove duplicate team ids')
duplicates = most_freq_team_ids_tm_side_secondary %>%
  group_by(tm_team) %>%
  summarise(count = n()) %>%
  filter(count > 1)

most_freq_team_ids_tm_side_secondary = most_freq_team_ids_tm_side_secondary %>%
  filter(!tm_team %in% duplicates$tm_team)

most_freq_team_ids_ws_side$match_certainty = 'High'
most_freq_team_ids_tm_side_secondary$match_certainty = 'Low'

print('Build initial team matches data frame and assign confidence to matches')
team_mapping = plyr::rbind.fill(most_freq_team_ids_ws_side[c('tm_team', 'ws_id', 'match_certainty')],
                     most_freq_team_ids_tm_side_secondary[c('tm_team', 'ws_id', 'match_certainty')])



colnames(team_mapping) = c('transfermarkt_teams',
                           'wyscout_team_ids', 'string_match_certainty')

team_mapping = team_mapping[complete.cases(team_mapping),]

team_mapping = team_mapping %>%
  filter(!transfermarkt_teams%in% without_club$tm_team)

without_club$string_match_certainty = 'High'
colnames(without_club) = colnames(team_mapping)
team_mapping = rbind(team_mapping, without_club)

print('Writing the initial team mapping to DB via overwrite.')
dbWriteTable(con, 'transfermarkt_to_wyscout_team_ids', team_mapping, overwrite = T, row.names = F, rownames = F)


print('Combine the TM transfers with team id mapping')
transfer_df$transferId = NA
transfer_df = transfer_df %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('left' = 'transfermarkt_teams')) %>%
  rename('fromTeamId' = 'wyscout_team_ids') %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('joined' = 'transfermarkt_teams')) %>%
  rename('toTeamId' = 'wyscout_team_ids')

# After matching teams, trim the redundant transfers further
transfer_df$year = year(transfer_df$date)
ws_transfers$year = year(ws_transfers$startDate)

transfer_bu = transfer_df

print('Find the transfers in wyscout for each player from same team to same team and remove duplicate transfers')
transfer_df = transfer_df %>%
  select(-transferId) %>%
  anti_join(ws_transfers[c('year', 'playerId', 'fromTeamId')]) %>%
  anti_join(ws_transfers[c('year', 'playerId', 'toTeamId')]) %>%
  filter(!is.na(fee)) %>%
  filter(fee > 0)

transfer_df = transfer_df %>%
  left_join(ws_transfers[c('year', 'playerId', 'fromTeamId', 'toTeamId', 'transferId')]) %>%
  select(-year)

ws_transfers = ws_transfers %>%
  select(-year) %>%
  data.frame()

transfer_df2 = transfer_df2 %>%
  select(-c('fromTeamId', 'toTeamId'))

transfer_df2 = transfer_df2 %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('left' = 'transfermarkt_teams')) %>%
  rename('fromTeamId' = 'wyscout_team_ids') %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('joined' = 'transfermarkt_teams')) %>%
  rename('toTeamId' = 'wyscout_team_ids')

transfer_df = rbind(transfer_df, transfer_df2[colnames(transfer_df)])
# transfer_df =  transfer_df2[colnames(transfer_df)]

print('Find transfers with not null fee and no transfer match')
transfers_with_fees_with_no_id = which(!is.na(transfer_df$fee) & is.na(transfer_df$toTeamId))

transfers_missing_teams = transfer_df[transfers_with_fees_with_no_id,]
missing_ids = unique(c(transfers_missing_teams$left[is.na(transfers_missing_teams$fromTeamId)], transfers_missing_teams$joined[is.na(transfers_missing_teams$toTeamId)]))

source1 = teams_df$name
names(source1) = teams_df$teamId

source2 = teams_df$officialName
names(source2) = teams_df$teamId

src = c(source1, source2)

print(paste('Final attempt at identifying the clubs and rematching transfers. At this point there are', length(missing_ids), 'Clubs (therefore their transfers) unmatched.'))
print('These clubs are:')
print(missing_ids)

ws_ids = sapply(missing_ids, get_ws_ids_from_tm_name, str_source = src)

print('Manually match AC Milan Youth')
ws_ids['AC Milan Youth'] = 65150

team_mapping_additional = data.frame(transfermarkt_teams = names(ws_ids),
                                     wyscout_team_ids = as.numeric(ws_ids),
                                     string_match_certainty = 'Medium')

team_mapping_additional = team_mapping_additional[complete.cases(team_mapping_additional),]

print('Finalize Team Matching')
dbWriteTable(con, 'transfermarkt_to_wyscout_team_ids', team_mapping_additional, append = T, row.names = F, rownames = F)
team_mapping = dbReadTable(con, 'transfermarkt_to_wyscout_team_ids')

# Re-merge to get a complete set of 
print('Re-merge transfer datasets to get the most complete match and remove duplicates again.')
team_mapping = team_mapping %>%
  group_by(transfermarkt_teams) %>%
  slice(1) %>%
  ungroup()

transfer_df = transfer_df %>%
  select(-c('fromTeamId', 'toTeamId'))

transfer_df = transfer_df %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('left' = 'transfermarkt_teams')) %>%
  rename('fromTeamId' = 'wyscout_team_ids') %>%
  left_join(team_mapping[c('transfermarkt_teams', 'wyscout_team_ids')], by = c('joined' = 'transfermarkt_teams')) %>%
  rename('toTeamId' = 'wyscout_team_ids')

transfer_df$toTeamId = as.numeric(transfer_df$toTeamId)
transfer_df$fromTeamId = as.numeric(transfer_df$fromTeamId)

# Find outstanding WS Transfers
ws_transfers_outstanding = ws_transfers %>%
  filter(!transferId %in% transfer_df$transferId) %>%
  mutate(season = paste(lubridate::year(startDate), lubridate::year(startDate)+1, sep = '/'),
         mv = NA,
         fee = ifelse(currency == 'USD', value*0.82, ifelse(currency == 'GBP', value*1.10, value)), 
         tm_player_id = NA) %>%
  select(season, startDate, fromTeamName, toTeamName, mv, fee, tm_player_id, type, playerId, transferId, fromTeamId, toTeamId) %>%
  rename('date' = 'startDate',
         'left' = 'fromTeamName',
         'joined' = 'toTeamName')

all_transfers = rbind(transfer_df, ws_transfers_outstanding)
all_transfers$fromTeamId[all_transfers$left=='Unknown'] = 0
all_transfers$toTeamId[all_transfers$joined=='Unknown'] = 0

all_transfers = all_transfers[!is.na(all_transfers$fromTeamId) | !is.na(all_transfers$toTeamId),]

if(Sys.info()['sysname'] == 'Windows'){
all_transfers$left <- 
  iconv(
    all_transfers$left,
    from = "UTF-8",
    to = "Windows-1252",
    sub = NA,
    mark = TRUE,
    toRaw = FALSE
  )

all_transfers$joined <- 
  iconv(
    all_transfers$joined,
    from = "UTF-8",
    to = "Windows-1252",
    sub = NA,
    mark = TRUE,
    toRaw = FALSE
  )
}


# Find duplicates
all_transfers$type[tolower(all_transfers$type)=='back from loan'] = 'End of loan'

duplicate_transfers = all_transfers %>%
  group_by(date, left, joined) %>%
  summarise(n = n())

to_remove = duplicate_transfers %>%
  filter(n > 3 | (is.na(left) & is.na(joined)))

all_transfers = all_transfers %>%
  group_by(date, playerId, type) %>%
  slice(1)

all_transfers = all_transfers %>%
  mutate(year = year(date),
         month = month(date),
         week = week(date),
         day = day(date))

get_transfers_to_examine = function(all_trfrs = all_transfers){
  to_examine = all_trfrs %>%
    group_by(year, playerId, month) %>%
    summarise(n = n()) %>%
    filter(n>1)
  
  to_examine = all_trfrs %>%
    right_join(to_examine) %>%
    arrange(playerId, year, month)
  
  to_examine
}

to_examine = get_transfers_to_examine()

all_transfers$type = tolower(all_transfers$type)
all_transfers_backup = all_transfers


# Find duplicates in transfers
## Logic
### Group by year, playerid, month and find the ones that occur more than once
#### Check fromTeamId and toTeamId to see if there are duplicates
##### If there are duplicates, retain the most populated row

# all_transfers = all_transfers %>%
#   mutate(fee = ifelse(fee==0, NA, fee))

all_transfers$num_complete = rowSums(is.na(all_transfers[setdiff(colnames(all_transfers),c('tm_player_id', 'transferId'))]))

all_transfers_team_dupes = all_transfers %>%
  group_by(year, playerId, month, fromTeamId, toTeamId) %>%
  mutate(n = n(),
         max_num_complete = max(num_complete, na.rm = T)) %>%
  filter(n > 1)  %>%
  filter(num_complete == max_num_complete)

all_transfers_team_dupes = all_transfers_team_dupes %>%
  group_by(year, playerId, month, fromTeamId, toTeamId) %>%
  slice(1)

all_transfers = all_transfers %>%
  anti_join(all_transfers_team_dupes[c('year', 'playerId', 'month', 'fromTeamId', 'toTeamId')])

all_transfers = rbind(all_transfers, all_transfers_team_dupes[colnames(all_transfers)])
  
# Find NAs that don't match and remove
to_examine = get_transfers_to_examine()
all_transfers = all_transfers %>%
  filter((is.na(left) == is.na(fromTeamId)) & (is.na(joined) == is.na(toTeamId)) | !is.na(fee))

# Find duplicate toTeamIds and remove 

all_transfers_to_team_dupes = all_transfers %>%
  group_by(year, playerId, month, toTeamId) %>%
  mutate(n = n(),
         max_num_complete = max(num_complete, na.rm = T)) %>%
  filter(n > 1) %>%
  filter(num_complete == max_num_complete)

all_transfers_to_team_dupes = all_transfers_to_team_dupes %>%
  group_by(year, playerId, month, toTeamId) %>%
  arrange(fee, mv) %>%
  slice(1)

all_transfers = all_transfers %>%
  anti_join(all_transfers_to_team_dupes[c('year', 'playerId', 'month', 'toTeamId')])
all_transfers = rbind(all_transfers, all_transfers_to_team_dupes[colnames(all_transfers)])

# Find duplicates fromTeamIds and remove

all_transfers_from_team_dupes = all_transfers %>%
  group_by(year, playerId, month, fromTeamId) %>%
  mutate(n = n(),
         max_num_complete = max(num_complete, na.rm = T)) %>%
  filter(n > 1) %>%
  filter(num_complete == max_num_complete)

all_transfers_from_team_dupes = all_transfers_from_team_dupes %>%
  group_by(year, playerId, month, fromTeamId) %>%
  arrange(fee, mv) %>%
  slice(1)

all_transfers = all_transfers %>%
  anti_join(all_transfers_from_team_dupes[c('year', 'playerId', 'month', 'fromTeamId')])
all_transfers = rbind(all_transfers, all_transfers_from_team_dupes[colnames(all_transfers)])

# Find duplicate matches to players and remove

all_transfers = all_transfers %>%
  filter(!is.na(type) & (is.na(left) == is.na(fromTeamId)) & (is.na(joined) == is.na(toTeamId)))
# 
# all_transfers_players_dupe = all_transfers %>%
#   group_by(date, fromTeamId, toTeamId, type, tm_player_id) %>%
#   mutate(n = n(),
#          max_num_complete = max(num_complete, na.rm = T)) %>%
#   filter(n > 1)

# Remove redundant cols and write to db
all_transfers = all_transfers %>%
  select(-c('year', 'month', 'week', 'day', 'num_complete')) %>%
  data.frame()

all_transfers$type[all_transfers$type == 'loan fee'] = 'loan'
all_transfers$type[all_transfers$type == '0'] = 'unknown'
all_transfers$type[all_transfers$type=='unknown' & !is.na(all_transfers$fee)] = 'transfer'
# all_transfers$type[all_transfers$type=='free agent' & 
#                      !all_transfers$joined %in% c('Without Club', 'Unknown', NA) & 
#                      !all_transfers$left %in% c('Without Club', 'Unknown', NA)] = 'transfer'

print('Manually Match additional transfers with weird characters')
all_transfers$toTeamId[all_transfers$playerId==70296]=6713
all_transfers$joined[all_transfers$playerId==70296]='Djurgården'

all_transfers$toTeamId[all_transfers$playerId==256760]=13518
all_transfers$joined[all_transfers$playerId==256760]='Pogon Szczecin'

all_transfers = all_transfers[!is.na(all_transfers$date),]

print('Fix season names')
all_transfers[['season']] = compute_transfer_season(all_transfers[['date']])

print('Write matched transfers to DB')
dbWriteTable(con, 'tmp_all_transfers', all_transfers, overwrite = T, row.names = F, rownames = F)
