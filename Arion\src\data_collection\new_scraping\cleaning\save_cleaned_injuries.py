import traceback
from sqlalchemy.engine import create_engine
from settings import postgres_prod_str
from src.data_collection.new_scraping.cleaning.validation import InjuriesPrep
from src.helper_funcs import fast_read_sql
from src.helper_funcs import fast_write_sql


def main():
    engine = create_engine(postgres_prod_str)
    SCHEMA = "meta_scraping"

    prep = InjuriesPrep()
    df = fast_read_sql(
        f"SELECT * FROM {SCHEMA}.raw_transfermarkt_injuries", engine
    )
    connection = engine.raw_connection()
    cursor = connection.cursor()
    try:
        cursor.execute("DELETE FROM transfermarkt.transfermarkt_injuries;")
        fast_write_sql(
            prep.clean_df(df),
            "transfermarkt_injuries",
            cnx=engine,
            if_exists="append",
            grant=True,
            schema="transfermarkt",
            cursor=cursor,
            connection=connection,
            transaction=True,
        )
        cursor.execute(f"DELETE FROM {SCHEMA}.raw_transfermarkt_injuries")
        connection.commit()
        cursor.close()

    except Exception as e:
        connection.rollback()
        cursor.close()
        print(traceback.format_exc())
        raise Exception()


if __name__ == "__main__":
    main()
