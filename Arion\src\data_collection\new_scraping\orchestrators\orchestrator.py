import sqlalchemy
from src.data_collection.new_scraping.scraping.father_scraper import <PERSON>raper

# Orchestrator of the whole process
# He will handle the incoming data from the scrapper,
# Validating and preparing data
# Writing to the migration table
# Tracks progress of the already collected and to be collected players
SCHEMA = "meta_scraping"

class Orchestrator:
    def __init__(
        self, prog_table: str, engine: sqlalchemy.engine, scraper: Scraper
    ):

        # SQL Alchemy
        self.engine = engine

        # Table
        self.prog_table = prog_table

        # Scraper
        self.scraper = scraper()
        self.dbschema = SCHEMA

    # SQL task populate the progress table with the data from the initial table

    # Get the IDs of the data you want to scrape for example: Teams, Players etc.
    def get_ids_from_progress(self, limit, id):
        pass

    # Scrape the data
    def get_df(self):
        # Scrape the players data
        pass

    # Check progress - Save the new data to migration table and
    def save_scraped_data(self):
        pass
        # Save in the migration table

    # Remove the already scraped IDs from the progress table
    def update_progress(self, ids):  # Can be a SQL task
        pass

    def check_progress(self):
        with self.engine.begin() as con:
            return con.execute(
                "SELECT EXISTS(select * from"
                f" {SCHEMA}.{self.prog_table})"
            ).first()[0]
