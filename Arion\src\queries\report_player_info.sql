SELECT ap."firstName", ap."lastName", ap."height", 
                        ap."birthDate", ap."birthArea_name", ap."weight", 
                        ap."playerId", ap."foot", 
                        lt."name" as team_name, lt."competition_area_name" as league_area, 
                        lt."competition_divisionLevel" as "divisionLevel",
                        tmd."agent", tmd."contract_expiry", tmd."current_value", 
                        tmd.highest_value, tmd.highest_value_date, 
                        tmd.date_joined_current_team, tmd.player_value_last_update,
                        tmd.player_url, 
						tmi.all_injuries, 
						tmi.currently_injured,
						tmi.latest_injury, 
						tmi.num_injuries,
						tmi.most_games_missed,
						tmi.longest_duration,
						tmi.total_games_missed,
						tmi.total_days_missed,
						tmi.average_recovery_time
FROM 
(SELECT 
* 
FROM 
all_players
WHERE "playerId" IN _IDS
 ) ap
LEFT JOIN (
SELECT
    *
FROM
    (
        SELECT
            ROW_NUMBER() OVER(
                PARTITION BY st."teamId"
                ORDER BY
                    ss."seasonstartDate" DESC
            ) AS rn,
            *
        FROM
            seasons_teams st,
            seasons ss
        WHERE
            st."seasonId" = ss."seasonId"
            AND ss.competition_format = 'Domestic league'
    ) tt
WHERE
    tt.rn = 1) lt
ON ap."currentTeamId" = lt."teamId"
LEFT JOIN transfermarkt_data tmd
ON ap."playerId" = tmd."playerId"
LEFT JOIN
(
SELECT
    STRING_AGG(DISTINCT "injury", ', ') AS "all_injuries",
    "playerId",
    SUM (
        CASE
            WHEN injured_until is NULL THEN 1
            ELSE 0
        END
    ) AS "currently_injured",
    MAX("injured_until") AS "latest_injury",
    COUNT("playerId") AS "num_injuries",
    MAX("games_missed") AS "most_games_missed",
    MAX("duration") AS "longest_duration",
    SUM("games_missed") AS "total_games_missed",
    SUM("duration") AS "total_days_missed",
    AVG("duration") AS "average_recovery_time"
FROM
    transfermarkt_injuries
GROUP BY
	"playerId"
) tmi
ON ap."playerId" = tmi."playerId"



