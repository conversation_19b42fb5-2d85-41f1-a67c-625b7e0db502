import pandas as pd

from src.helper_funcs import fast_write_sql, cloud_log_struct
from src.data_collection.wyscout.v2.Filler import Filler


class RecordsFiller(Filler):
    """Create missing records where we cannot retrieve the item so that sql relation dont break"""

    def __init__(self):
        super().__init__()

    @staticmethod
    def replace_by_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        df[df.select_dtypes("object").columns] = "unknown"
        df[df.select_dtypes("bool").columns] = False
        df[df.select_dtypes("number").columns] = -1
        df[df.select_dtypes("number").columns] = df[
            df.select_dtypes("number").columns
        ].astype("Int64")
        return df

    @staticmethod
    def fill_by_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        df[df.select_dtypes("object").columns] = df[
            df.select_dtypes("object").columns
        ].fillna("unknown")
        df[df.select_dtypes("bool").columns] = df[
            df.select_dtypes("bool").columns
        ].fillna(False)
        df[df.select_dtypes("number").columns] = (
            df[df.select_dtypes("number").columns].fillna(-1).astype("Int64")
        )
        return df

    def generate_unknown_record_from_sample_df(self, sample_df):
        sample_row = sample_df[:1].copy()
        return self.replace_by_dtypes(sample_row)

    def create_unknown_records(self):
        for table in self.dependencies_map:
            table_name = table["table"]
            # check if the unknown record is already created and if yes, skip the rest:
            ch = pd.read_sql(
                f"""SELECT * from {self.schema}.{table_name} where "{table['id']}" = -1 """,
                self.cnx_prod,
            )
            if len(ch) > 0:
                continue
            temp = pd.read_sql(
                f"select * from {self.schema}.{table_name}", self.cnx_prod
            )
            df = self.generate_unknown_record_from_sample_df(temp)
            fast_write_sql(
                df,
                table_name,
                self.cnx_prod,
                schema="wyscout",
                if_exists="append",
            )
            cloud_log_struct(
                self.logger,
                {"action": "create_unknown_records", "object_type": table_name},
            )

    def fill_gaps(self):
        for table in self.gaps_map:
            df = pd.DataFrame(self.gaps_map[table])
            sample = pd.read_sql(
                f'select * from {self.schema}."{table}" ',
                self.cnx_prod,
            )
            sample = sample[:1]
            fill_df = self.fill_by_dtypes(
                pd.concat([sample, df]).convert_dtypes()
            )[1:]
            fast_write_sql(
                fill_df,
                table,
                self.cnx_prod,
                schema=self.schema,
                if_exists="append",
            )
            cloud_log_struct(
                self.logger,
                {
                    "action": "fill_parents",
                    "parent": table,
                    "ids_count": len(df),
                },
            )
