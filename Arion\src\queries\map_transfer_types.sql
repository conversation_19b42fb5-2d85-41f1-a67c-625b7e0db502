ALTER TABLE
    transfermarkt.tm_transfers
ADD
    COLUMN IF NOT EXISTS new_type TEXT;

UPDATE
    transfermarkt.tm_transfers
SET
    new_type = CASE
        WHEN (
            "type" = 'loan transfer'
            OR "type" = 'loan fee'
            OR "type" = 'loan'
        ) THEN 'loan'
        WHEN "type" = 'end of loan' THEN 'back from loan'
        WHEN "type" = 'transfer' THEN 'transfer'
        WHEN "type" = 'draft' THEN 'draft'
        WHEN (
            "type" = 'draft'
            OR "type" = 'free transfer'
        ) THEN 'free transfer'
        WHEN (
            "joined_name" = 'without club'
            OR "joined_name" = 'career break'
        ) THEN 'free agent'
        WHEN "joined_name" = 'retired' THEN 'retired'
        WHEN (
            "type" IS NULL
            AND "joined_name" NOT IN (
                'unknown',
                'career break',
                'died',
                'retired',
                'without club'
            )
        ) THEN 'other'
        ELSE 'unknown'
    END;