import os
import pickle
import pandas as pd
from tenacity import retry, wait_random, stop_after_attempt
from sqlalchemy import create_engine

from settings import postgres_prod_str, postgres_dev_str, PROXIES
from src.data_collection.scraping.scraping.scrape_tm_player_profiles import (
    PlayerScraper,
    loop_players,
)
from src.data_collection.scraping.scraping.scrape_transfer_markt import (
    get_soup_from_url,
)
from src.helper_funcs import read_config, get_time_stamp


class PlayerInjuryScraper(PlayerScraper):
    def __init__(self, config, cnx_prod, cnx_dev):
        super().__init__(config, cnx_prod, cnx_dev)
        self.cnx_dev = cnx_dev
        self.cnx_prod = cnx_prod
        self.df = pd.DataFrame(
            columns=[
                "player_id",
                "player_url",
                "season",
                "injury",
                "injured_from",
                "injured_until",
                "duration",
                "games_missed",
            ]
        )
        self.data_dict = {x: [] for x in self.df.columns}
        # export diff file at every run
        self.export_file = f'{config["scraped_player_injuries"]}/player_injuries{get_time_stamp()}.csv'
        # use the prepped file to check which are already scraped
        self.progress_tracker_path = config["progress_trackers"][
            "player_injuries"
        ]

    def get_priority_ids(self, refresh):
        players = pd.read_sql(
            f"""SELECT "tm_player_url" as player_url
                            FROM  tm_to_ws_ids""",
            self.cnx_prod,
        )
        self.priority_players = pd.Series(
            [
                url.replace("/profil/", "/verletzungen/")
                for url in players["player_url"]
            ]
        )

    @retry(
        wait=wait_random(min=10, max=30),
        stop=stop_after_attempt(5),
    )
    def scrape_player(self, player_url):
        soup = get_soup_from_url(player_url, self.session, proxies=PROXIES)
        self.progress_tracker.append(player_url)
        if soup is None or not soup.find("h1", attrs={"itemprop": "name"}):
            print(f"no soup {player_url}")
            return
        # don't want no retired playas:
        if soup.find(title="Retired"):
            return
        temp_id = player_url.split("/")[-1]
        # getting all but id and url, as this is not in tm injury tables
        info_items = [
            x for x in self.data_dict if x not in ["player_id", "player_url"]
        ]
        # getting all rows in the table
        table = soup.find("tbody")
        #  dude has no injuries so we move on
        if table is None:
            return
        table_rows = table.find_all("tr")
        if len(table_rows) > 0:
            for row in table_rows:
                cells = row.find_all("td")
                # row/table must be broken, skipping this crap
                if len(info_items) != len(cells):
                    continue
                else:
                    # get all cells, should be 6
                    cells = row.find_all("td")
                    for inj_var, inj_value in zip(info_items, cells):
                        self.data_dict[inj_var].append(inj_value.text)
                    # need to append player id to each injury row
                    self.data_dict["player_id"].append(temp_id)
                    self.data_dict["player_url"].append(player_url)

    def prep_transfers_df(self):
        pass

    def write_raw_transfers(self):
        pass


# def profile_to_injury_url(urls):
#     urls = pd.Series([url.replace('/profil/', '/verletzungen/') for url in urls])
#     return urls

# select only players that we have matched and already have in the db
# def get_ids(cnx, config):
#     players = pd.read_sql(f'''SELECT "tm_player_url" as player_rul
#                             FROM  tm_to_ws_ids''',
#                             cnx)
#     return players['player_url']


def main():
    try:
        refresh = os.environ["REFRESH"]
    except:
        refresh = False
    cnx_prod = create_engine(postgres_prod_str)
    cnx_dev = create_engine(postgres_dev_str)
    config = read_config("config.yml")
    scraping_config = config["scraping"]["directories"]
    db_config = config["db_tables"]
    # players = get_ids(cnx_prod, db_config)
    # players_injuries_urls = profile_to_injury_url(players)
    loop_players(
        PlayerInjuryScraper, scraping_config, cnx_prod, cnx_dev, refresh
    )


if __name__ == "__main__":
    main()
