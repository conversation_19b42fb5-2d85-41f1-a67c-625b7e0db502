from datetime import datetime, timedelta
import time

from airflow import DAG
from airflow.operators.postgres_operator import PostgresOperator
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dummy_operator import DummyOperator

# from dag_settings import workdir_sorare
workdir_sorare = '/home/<USER>/Projects/working_branches/sorare'

# src/sorare/calculate_tweet_thresholds.py

dag_params = {
    "dag_id": "calculate_tweet_anomaly_thresholds",

    "start_date": datetime(2022, 2, 2),
    "schedule_interval": None,
    "params": {"workdir": workdir_sorare},
    "max_active_runs": 1,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    calculate_tweet_thresholds_backtest = BashOperator(
        task_id="calculate_tweet_thresholds_backtest",
        bash_command="""
                        export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                        cd {{ params.workdir }}
                        python3 src/modelling/calculate_tweet_thresholds_backtest.py """,
    )

    calculate_tweet_thresholds_backtest