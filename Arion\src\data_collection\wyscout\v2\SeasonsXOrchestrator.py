from typing import Type


from src.data_collection.wyscout.v2.Orchestrator import Orchestrator
from src.data_collection.wyscout.v2.Updates<PERSON>hecker import Updates<PERSON><PERSON>cker
from src.data_collection.wyscout.v2.Updater import Updater


class SeasonsXOrchestrator(Orchestrator):
    def __init__(
        self,
        batch_size: int,
        updates_checker: Type[UpdatesChecker],
        updater: Type[Updater],
        skip_empty_games: bool = True,
        phantom_objects: list = None,
    ):
        from_scratch = False  # seasons are alsways updated from scratch, i.e. we dont check the changed
        # object endpoint
        super().__init__(
            batch_size=batch_size,
            updates_checker=updates_checker,
            updater=updater,
            from_scratch=from_scratch,
            skip_empty_games=skip_empty_games,
            phantom_objects=phantom_objects,
        )

    async def write_to_db(
        self, updater_resp, prepped_ids_in_batch, connection, cursor
    ):
        if not self.updater.only_active:
            for df_name, df in updater_resp.items():
                self.handle_write_single_table(
                    df, prepped_ids_in_batch, df_name, connection, cursor
                )
        else:
            # for seasons_x tables the check for phantoms happens right before we write,
            # reasong being that we cannot possibly know that we have a match/team/player
            # with missing refernce data before we have run the whole batch, unfortunately,
            # this needs to happen for each batch we run:
            if self.phantom_objects is not None:
                for phantom_object in self.phantom_objects:
                    phantom_id = self.get_id_name_from_object_name(
                        phantom_object
                    )
                    ids = updater_resp[phantom_id].astype(float).tolist()
                    await self.handle_phantoms(phantom_object, ids)
            self.handle_write_single_table(
                updater_resp,
                prepped_ids_in_batch,
                self.updates_checker.object_type,
                connection,
                cursor,
            )
