import pandas as pd
import sqlalchemy

from src.helper_funcs import get_cloud_logger


class SQLKeysManager:
    def __init__(
        self,
        table_name: str,
        schema: str,
        cnx: sqlalchemy.engine.base.Engine,
        cursor=None,
    ):
        self.table_name = table_name
        self.schema = schema
        self.cnx = cnx
        self.fkeys_list = None
        self.dropped_keys = False
        self.logger = get_cloud_logger()
        self.cursor = cursor if cursor is not None else cnx

    def get_fkeys(self):
        query = f"""
                SELECT
                tc.constraint_type,
                tc.table_schema, 
                tc.constraint_name, 
                tc.table_name, 
                kcu.column_name, 
                ccu.table_schema AS foreign_table_schema,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND ccu.table_name='{self.table_name}'
            AND tc.table_schema = '{self.schema}';
         """
        self.fkeys_list = list(
            pd.read_sql(query, self.cnx).to_dict(orient="index").values()
        )

    def drop_fkeys(self):
        for key in self.fkeys_list:
            self.cursor.execute(
                f"""
            ALTER TABLE {key['table_schema']}."{key['table_name']}" 
            DROP CONSTRAINT "{key['constraint_name']}";
            """
            )
        self.dropped_keys = True
        self.logger.log_text(f"Foreign keys dropped for table {self.table_name}")

    def recreate_fkeys(self):
        if not self.dropped_keys:
            raise ValueError(
                f"Keys for table {self.table_name} have not been dropped"
            )
        for key in self.fkeys_list:
            self.cursor.execute(
                f"""
            ALTER TABLE {key['table_schema']}."{key['table_name']}" 
            ADD  CONSTRAINT "{key['constraint_name']}" 
            FOREIGN KEY ("{key['column_name']}") 
            REFERENCES {key['foreign_table_schema']}."{key['foreign_table_name']}"  ("{key['foreign_column_name']}");
            """
            )
        self.logger.log_text(f"Foreign keys recreated for table {self.table_name}")


