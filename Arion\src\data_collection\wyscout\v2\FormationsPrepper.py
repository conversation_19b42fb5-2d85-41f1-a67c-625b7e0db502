import copy
from typing import Dict
import pandas as pd


class FormationsPrepper:
    def fix_timestamps(self, df, ts):
        df["startSec_old"] = df["startSec"]
        for i, row in df.iterrows():
            if row["matchPeriod"] == "2H":
                df.at[i, "startSec"] = (
                    row["startSec"]
                    + ts["first_half_end"]
                    - ts["gap_first_second"]
                )
            elif row["matchPeriod"] == "E1":
                df.at[i, "startSec"] = (
                    row["startSec"]
                    + ts["second_half_end"]
                    - ts["gap_first_second"]
                    - ts["gap_second_e1"]
                )
            elif row["matchPeriod"] == "E2":
                df.at[i, "startSec"] = (
                    row["startSec"]
                    + ts["e1_end"]
                    - ts["gap_first_second"]
                    - ts["gap_second_e1"]
                    - ts["gap_e1_e2"]
                )

        df["startSec_next"] = df.groupby(["matchId", "teamId"])[
            "startSec"
        ].transform(lambda x: x.shift(-1))
        df["startSec_next"] = df["startSec_next"].fillna(
            ts["total"]
            - ts["gap_first_second"]
            - ts["gap_second_e1"]
            - ts["gap_e1_e2"]
        )
        df["matchPeriod_next"] = (
            df.groupby(["matchId", "teamId"])["matchPeriod"]
            .transform(lambda x: x.shift(-1))
            .fillna(ts["last_period"])
        )

        df["duration"] = (df["startSec_next"] - df["startSec"]) / 60

        return df

    @staticmethod
    def get_agg_positions(df: pd.DataFrame) -> pd.DataFrame:
        pos = (
            df.groupby(["playerId", "position", "matchId"])["duration"]
            .sum()
            .reset_index()
        )
        pos["percent"] = pos.groupby(["playerId", "matchId"])[
            "duration"
        ].transform(lambda x: round(x / x.sum() * 100))
        pos = pos.drop(columns=["duration"])
        return pos

    def prep(self, formations: dict, matchId: int) -> Dict[str, pd.DataFrame]:
        """Compute formations and player-match-positions for the game
        Args:
            formations (dict): formations response
            matchId (int): if of match since this is not provided in formations
            ts(dict): dictionary with timestamps

        Returns:
            pd.DataFrame: prepped df
        """
        forms = copy.deepcopy(formations)
        # get matchId from events:
        # traverse wyscout's garbage api response which is in the format:
        # teamId: half:startsecof formation:formationtype: information
        form_list = []
        for k, v in forms.items():
            for vv in v.values():
                for vvv in vv.values():
                    for vvvv in vvv.values():
                        # add team id, matchid to this garbage
                        vvvv["teamId"] = int(k)
                        vvvv["matchId"] = matchId

                        # add formation id so we can merge back when we flatten:
                        vvvv["players"] = list(
                            map(
                                lambda x: dict(
                                    list(x.values())[0], **{"id": vvvv["id"]}
                                ),
                                vvvv["players"],
                            )
                        )
                        form_list.append(vvvv)
        df = pd.DataFrame(form_list)
        # df = self.fix_timestamps(df, ts) #NOTE using wyscout timestamps for now
        df["duration"] = (df["endSec"] - df["startSec"]) / 60
        players = df.pop("players")
        players = pd.concat(list(map(lambda x: pd.DataFrame(x), players)))
        df = pd.merge(df, players, on="id")

        # deduping because wyscoyt suck:
        df = df.drop_duplicates(subset=["playerId", "matchId", "id"])

        pos = self.get_agg_positions(df)
        return {"formations": df, "player_match_positions": pos}
