import pandas as pd
import numpy as np
import re

from src.data_collection.new_scraping.mixins import (
    TransferMartkMoneyPrepMixin,
    TransferMartkDateMixin,
    TmLeagueTierMixin,
)


def prepare_data(data_frame: pd.DataFrame) -> pd.DataFrame:
    # Validate the players data
    return data_frame


class Prep:
    def __init__(self):
        pass

    def clean_df(self, data_frame: pd.DataFrame):
        pass


class ExamplePrep(Prep):
    def __init__(self, data_frame: pd.DataFrame):
        super().__init__(data_frame)

    def clean_df(self):
        return self.data_frame


class PlayerPrep(
    Prep, TransferMartkMoneyPrepMixin, TransferMartkDateMixin, TmLeagueTierMixin
):
    def clean_df(self, data_frame: pd.DataFrame):

        # First I am cleaning the data with the common methods from the Mixin classes
        data_frame = self.clean_league(
            self.clean_dates(self.clean_money(data_frame))
        )

        # Specific cleaning now
        data_frame["height"] = data_frame["height"].apply(
            lambda x: x.split("\xa0")[0] if (np.all(pd.notnull(x))) else x
        )

        data_frame = data_frame.drop_duplicates(subset=['playerId'])

        return data_frame


class TransfersPrep(Prep, TransferMartkDateMixin, TransferMartkMoneyPrepMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        def specific_cleaning(x):
            if x == "?":
                return "unknown"
            elif x == "-":
                return np.nan
            elif x in ["Loan fee:", "Loan fee"]:
                return "loan fee"
            elif x == "Draft":
                return "draft"
            elif x == "End of loan":
                return "end of loan"
            else:
                return x

        def joined_name_cleaning(x):
            if x == "Without Club":
                return "without club"
            elif x == "Career break":
                return "career break"
            else:
                return x

        data_frame = self.clean_dates(self.clean_money(data_frame))

        # Specific cleaning now
        data_frame["type"] = data_frame["type"].apply(specific_cleaning)
        data_frame["joined_name"] = data_frame["joined_name"].apply(
            joined_name_cleaning
        )
        # adding placeholder col so we match cols 
        # so that we can append to existing table instead of replacing:
        data_frame["new_type"] = np.nan  
        return data_frame


class InjuriesPrep(Prep, TransferMartkDateMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        def specific_cleaning_missed(x):
            if x in ("-", "?"):
                return np.nan
            try:
                return int(x)
            except:
                return np.nan

        def specific_cleaning_duration(x):
            if isinstance(x, float):
                return x
            if "?" in x:
                return np.nan
            if "days" not in x:
                return np.nan
            try:
                return int(x.split(" ")[0])
            except:
                return np.nan

        data_frame = self.clean_dates(data_frame)
        data_frame["games_missed"] = (
            data_frame["games_missed"]
            .apply(specific_cleaning_missed)
            .astype("Int64")
        )
        data_frame["duration"] = (
            data_frame["duration"]
            .apply(specific_cleaning_duration)
            .astype("Int64")
        )
        return data_frame


class StaffPrep(Prep, TransferMartkDateMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        def specific_date_appointed_cleaning(x):
            match = re.findall(r"\(.+\)", str(x))
            if match:
                return match[0][1:-1]
            else:
                new_match = re.findall(r"(\d+)\/(\d+)", str(x))
                if new_match:
                    year = new_match[0][0]
                    if int(year) <= 70:
                        return f"Jan 1, 20{year}"
                    else:
                        return f"Jan 1, 19{year}"
                return np.nan
        
        def specific_date_charge_cleaning(x):
            match = re.findall(r"\(.+\)", str(x))
            if match:
                return match[0][1:-1]
            else:
                new_match = re.findall(r"(\d+)\/(\d+)", str(x))
                if new_match:
                    year = new_match[0][0]
                    if int(year) <= 70:
                        return f"Dec 31, 20{year}"
                    else:
                        return f"Dec 31, 19{year}"
                return np.nan

        def match_cleaning(x):
            if x == "-":
                return np.nan
            else:
                return x

        data_frame["appointed"] = data_frame["appointed"].apply(
            specific_date_appointed_cleaning
        )
        data_frame["in_charge_until"] = data_frame["in_charge_until"].apply(
            specific_date_charge_cleaning
        )
        data_frame = self.clean_dates(data_frame)

        data_frame["matches"] = data_frame["matches"].apply(match_cleaning)
        return data_frame


class TeamsPrep(Prep, TmLeagueTierMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        data_frame = self.clean_league(data_frame)
        return data_frame


class AgentsPrep(Prep, TransferMartkMoneyPrepMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        data_frame = self.clean_money(data_frame)
        return data_frame
class HistoricalContract(Prep, TransferMartkDateMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        data_frame = self.clean_dates(data_frame)
        return data_frame
class HistoricalValuesPrep(Prep, TransferMartkMoneyPrepMixin, TransferMartkDateMixin):
    def clean_df(self, data_frame: pd.DataFrame):
        data_frame = self.clean_dates(self.clean_money(data_frame))
        return data_frame