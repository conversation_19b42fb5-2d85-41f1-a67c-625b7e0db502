from multiprocessing import cpu_count, Pool
import time
import aiohttp
import asyncio
from bs4 import BeautifulSoup
from numpy.lib.function_base import append
import pandas as pd
import numpy as np
from settings import PROXIES
from src.data_collection.new_scraping.scraping.father_scraper import (
    Scraper,
    fetch,
    get_response,
)
import requests

pd.set_option("display.max_columns", 300)


class TmAgentsScraper(Scraper):
    async def loop_through_urls(self) -> pd.DataFrame:
        url = "https://www.transfermarkt.com/berater/spielerberateruebersicht/berater/28/ajax/yw1"
        text = get_response(url)
        soup = BeautifulSoup(text, "html.parser")

        countries_urls = []
        for i in range(1, 9):
            text = get_response(f"{url}/page/{i}")
            soup = BeautifulSoup(text, "html.parser")
            countries_table = (
                soup.find("div", attrs={"id": "yw2"}).find("table").find("tbody")
            )
            countries_elements = countries_table.findAll(
                "td", attrs={"class": "hauptlink"}
            )
            if i == 1:
                countries_elements.pop(0)
            for element in countries_elements:
                countries_urls.append(
                    f"https://www.transfermarkt.com/berater/beraterfirmenuebersicht/berater?landId={element.find('a')['href'].split('/')[-1]}"
                )

        html_tasks = []
        async with aiohttp.ClientSession() as session:
            html_tasks.extend(fetch(session, url) for url in countries_urls)
            htmls = await asyncio.gather(*html_tasks)
        return self.loop_through_htmls(htmls)

    def loop_through_htmls(self, htmls) -> pd.DataFrame:
        cpu = cpu_count()

        pool = Pool(processes=cpu)
        res = pool.map(self.scrape_agent_info, htmls)
        pool.close()
        pool.join()

        agent_df = []

        for data in res:
            if data:
                [agent_df.append(d) for d in data]
        return pd.DataFrame(agent_df)

    def scrape_agent_info(self, tuple):
        text, url = tuple
        result = []
        soup = BeautifulSoup(text, "html.parser")

        if soup is None:
            print(f"no soup {url}")
            time.sleep(5)
            return result

        try:
            num_pages = int(
                soup.find(
                    "li", attrs={"class": "tm-pagination__list-item--icon-last-page"}
                )
                .find("a")["href"]
                .split("=")[-1]
            )
        except:
            num_pages = 1

        agencies = [
            f"https://transfermarkt.com/{url}&page={i}" for i in range(1, num_pages + 1)
        ]
        # No agencies, so we won't collect them

        if not agencies:
            return result
        ll = [
            (
                requests.get(
                    url,
                    headers={
                        "User-Agent": (
                            "Mozilla/5.0 (Windows NT 5.1)"
                            " AppleWebKit/537.36 (KHTML, like Gecko) "
                            "Chrome/49.0.2623.112 Safari/537.36"
                        )
                    },
                    proxies={"https": PROXIES},
                ).content,
                url,
            )
            for url in agencies
        ]
        for agency in ll:
            data = self.scrape_country_agents(agency)
            if data:
                [result.append(d) for d in data if data]
        return result

    def scrape_country_agents(self, tuple):
        text, url = tuple
        soup = BeautifulSoup(text, "html.parser")
        ll_dd = []

        if soup is None:
            print(f"no soup {url}")
            time.sleep(5)
            return []
        if not soup.find("div", attrs={"id": "yw1"}):
            print(f"no table with agencies for country with url: {url}")
            time.sleep(1)
            return []

        dd = {
            "agency": np.nan,
            "agency_id": np.nan,
            "players": np.nan,
            "players_in_PL": np.nan,
            "rumours": np.nan,
            "total_market_value": np.nan,
            "average_market_value": np.nan,
        }
        try:
            agents_trs = (
                soup.find("div", attrs={"id": "yw1"}).find("tbody").findAll("tr")
            )
        except:
            print(f"No agencies for this url {url}")
            return []

        for tr in agents_trs:
            try:
                agency_el = (
                    tr.find("table", attrs={"class": "inline-table"})
                    .find("td", attrs={"class": "hauptlink"})
                    .find("a")
                )
                agency = agency_el.text.strip()
                agency_id = agency_el["href"].split("/")[-1]
            except:
                agency = np.nan
                agency_id = np.nan

            try:
                players_el = tr.findAll("td", attrs={"class": "zentriert"})[1].find("a")
                players = players_el.text.strip()
            except:
                players = np.nan

            try:
                players_pl_el = tr.findAll("td", attrs={"class": "zentriert"})[2].find(
                    "a"
                )
                players_pl = int(players_pl_el.text.strip())
            except:
                players_pl = np.nan

            try:
                rumours_el = tr.findAll("td", attrs={"class": "zentriert"})[3].find("a")
                rumours = int(rumours_el.text.strip())
            except:
                rumours = np.nan

            try:
                total_m_value_el = tr.findAll("td", attrs={"class": "rechts"})[0]
                total_m_value = total_m_value_el.text.strip()
            except:
                total_m_value = np.nan

            try:
                average_m_value_el = tr.findAll("td", attrs={"class": "rechts"})[1]
                average_m_value = average_m_value_el.text.strip()
            except:
                average_m_value = np.nan

            dd["agency"] = agency
            dd["agency_id"] = agency_id
            dd["players"] = players
            dd["players_in_PL"] = players_pl
            dd["rumours"] = rumours
            dd["total_market_value"] = total_m_value
            dd["average_market_value"] = average_m_value
            ll_dd.append(dd.copy())
            dd = {key: np.nan for key in dd}
        return ll_dd


if __name__ == "__main__":
    scraper = TmAgentsScraper()
    print(asyncio.run(scraper.loop_through_urls()))
