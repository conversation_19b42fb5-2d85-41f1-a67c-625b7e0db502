from src.data_collection.new_scraping.orchestrators.orchestrator import (
    Orchestrator,
)
from src.data_collection.new_scraping.scraping.father_scraper import Scraper
import asyncio
from src.helper_funcs import fast_write_sql, fast_read_sql, get_sql_array_str

# Orchestrator of the whole process
# He will handle the incoming data from the scrapper,
# Validating and preparing data
# Writing to the migration table
# Tracks progress of the already collected and to be collected players


class TMStaffOrchestrator(Orchestrator):
    def __init__(self, prog_table, raw_data_table, engine, scraper: Scraper):
        # Tables
        self.raw_table = raw_data_table

        super().__init__(prog_table, engine, scraper)

    # Get the IDs of the data you want to scrape for example: Teams, Players etc.
    def get_urls_from_progress(self, limit, url):
        query = (
            f"SELECT {url} FROM"
            f" {self.dbschema}.{self.prog_table} LIMIT {limit}"
        )
        # Select the players IDs
        self.tt = fast_read_sql(query, self.engine)
        self.urls = self.tt[url].tolist()

    # Scrape the data
    def get_df(self):
        # Scrape the players data
        self.staff_df = asyncio.run(
            self.scraper.loop_through_urls(self.urls)
        )  # players_ids

    # Save to raw table

    # Check progress - Save the new data to migration table and
    def save_scraped_data(self):
        # Save in the raw table
        try:
            fast_write_sql(
                self.staff_df,
                self.raw_table,
                cnx=self.engine,
                if_exists="append",
                schema=self.dbschema,
                transaction=False,
            )
        except Exception as e:
            raise (e)

    #
    # Remove the already scraped IDs from the progress table
    def update_progress(self, prog_id):  # Can be a SQL task
        # Remove the players in progress table based on the players in the migration table
        query = (
            f"DELETE FROM {self.dbschema}.{self.prog_table} WHERE"
            f" {self.dbschema}.{self.prog_table}.{prog_id} in"
            f" {get_sql_array_str(self.urls)}"
        )
        print(query)
        with self.engine.connect() as connection:
            connection.execute(query)

    # When the migration table is full, move all the data to the existing players table
