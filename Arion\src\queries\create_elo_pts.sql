DROP TABLE IF EXISTS elo_pts;
CREATE TABLE elo_pts
AS
SELECT sm."date", sm."competitionId", home."matchId", home_id, away_id, home.home_score, away.away_score, 
	CASE 
		WHEN home.home_score > away.away_score 
		THEN 1 
		WHEN home.home_score < away.away_score 
		THEN 0
		ELSE 0.5
		END
		AS home_pts
FROM 
(
    SELECT "teamId" AS home_id, score AS home_score, "matchId" 
    FROM matches_teams
    WHERE side='home'
    ) home, 
(
    SELECT "teamId" AS away_id, score AS away_score, "matchId"  
    FROM matches_teams
    WHERE side='away'
    ) away, 
    seasons_matches sm
    WHERE home."matchId" = away."matchId" and home."matchId"=sm."matchId"
WITH DATA;
GRANT all ON elo_pts TO elvan, daniel, kliment;