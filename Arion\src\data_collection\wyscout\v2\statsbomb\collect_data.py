import json
import requests
from typing import List
from datetime import datetime
from sqlalchemy import create_engine
import pandas as pd
from settings import postgres_prod_str
from src.data_collection.wyscout.v2.Updater import Updater
from src.helper_funcs import fast_write_sql
from time import sleep

AUTH = ("<EMAIL>", "kW3jiHLz")
cnx = create_engine(postgres_prod_str)


def get_sbomb_request(url, params=None):
    resp = requests.get(url, auth=AUTH, params=params)
    return json.loads(resp.text)


def get_sbomb():
    sb_url = "https://data.statsbombservices.com/api/v2/competitions"
    return get_sbomb_request(sb_url)


def get_comps():
    url = "https://data.statsbomb.com/api/v2/competitions"
    comps = requests.get(url, auth=AUTH)
    df = pd.DataFrame(json.loads(comps.text))
    df.to_sql("competitions", cnx, if_exists="replace", schema="statsbomb")


def get_seasons_matches():
    comp_df = pd.read_sql("select * from statsbomb.competitions", cnx)
    comp_ids = list(comp_df.competition_id.unique())
    season_ids = list(comp_df.season_id.unique())
    cnx.execute("drop table if exists statsbomb.matches;")
    upd = Updater(None)
    for cmid in comp_ids:
        for smid in season_ids:
            url = f"https://data.statsbomb.com/api/v4/competitions/{cmid}/seasons/{smid}/matches"
            matches = requests.get(url, auth=AUTH)
            df = upd.flatten_dict_cols(
                upd.flatten_dict_cols(
                    pd.DataFrame(json.loads(matches.text)), True
                ),
                True,
            )
            fast_write_sql(
                df, "matches", cnx, if_exists="append", schema="statsbomb"
            )


def get_events():
    matches = pd.read_sql("select * from statsbomb.matches", cnx)[
        "match_id"
    ].tolist()
    cnx.execute("drop table if exists statsbomb.events;")

    for mid in matches:
        url = f"https://data.statsbomb.com/api/v5/events/{mid}"
        ev = requests.get(
            url,
            auth=AUTH,
        )
        try:
            df = pd.DataFrame(json.loads(ev.text))
            fast_write_sql(
                df, "events", cnx, if_exists="append", schema="statsbomb"
            )
        except:
            print(mid)


def get_advanced_stats():
    matches = pd.read_sql("select * from statsbomb.matches", cnx)[
        "match_id"
    ].tolist()
    cnx.execute("drop table if exists statsbomb.advanced_stats;")

    for mid in matches:
        url = f"https://data.statsbomb.com/api/v1/matches/{mid}/player-stats"
        ev = requests.get(
            url,
            auth=AUTH,
        )
        try:
            print(datetime.now(), mid)
            df = pd.DataFrame(json.loads(ev.text))
            sleep(1 / 10)
            fast_write_sql(
                df,
                "advanced_stats",
                cnx,
                if_exists="append",
                schema="statsbomb",
            )
        except:
            print(mid)


# Pisa	Empoli	available	scheduled	2020-07-29
def get_sbomb_events(matchId: int = 2272562, params: dict = None) -> dict:
    base_url = " https://data.statsbomb.com/api/v5/events/_ID"
    url = base_url.replace("_ID", str(matchId))
    resp = requests.get(url, auth=AUTH, params=params)
    return json.loads(resp.text)


def get_events_df(events: List[dict]) -> pd.DataFrame:
    df = pd.DataFrame(events)
    df = Updater.flatten_dict_cols(df, True)
    # cant be bothered with recursion:
    df = Updater.flatten_dict_cols(df, True)
    return df
