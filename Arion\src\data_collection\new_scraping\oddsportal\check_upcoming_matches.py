import time
from datetime import datetime
import pandas as pd
from sqlalchemy import create_engine
from settings import postgres_prod_str
from src.helper_funcs import get_sql_array_str

def main():
    engine = create_engine(postgres_prod_str)

    time_now = int(datetime.now().timestamp())

    query = """
    select match_id, match_date from oddsportal.upcoming_matches um 
    order by match_date """
    with engine.connect() as connection:
        df = pd.read_sql(query, connection)

    matches_to_be_moved = []
    for index, row in df.iterrows():
        match_time = int(time.mktime(datetime.strptime(row['match_date'], "%Y-%m-%d %H:%M:%S").timetuple()))
        if time_now > match_time:
            matches_to_be_moved.append(row['match_id'])

    if matches_to_be_moved:
        engine.execute(f"""INSERT INTO oddsportal.past_matches
                        SELECT * FROM oddsportal.upcoming_matches
                        WHERE match_id in {get_sql_array_str(matches_to_be_moved)}""")
        engine.execute(f"""INSERT INTO oddsportal.past_match_odds
                        SELECT * FROM oddsportal.upcoming_match_odds
                        WHERE match_id in {get_sql_array_str(matches_to_be_moved)}""")
        engine.execute(f"""DELETE FROM oddsportal.upcoming_matches
                        WHERE match_id in {get_sql_array_str(matches_to_be_moved)}""")
        engine.execute(f"""DELETE FROM oddsportal.upcoming_match_odds
                        WHERE match_id in {get_sql_array_str(matches_to_be_moved)}""")

if __name__ == "__main__":
    main()