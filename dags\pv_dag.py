from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from dag_settings import workdir_pv as workdir

dag_params = {
    "dag_id": "player_valuation_dag",
    "start_date": datetime(2021, 5, 13),
    "schedule_interval": timedelta(days=14),
    "params": {"workdir": workdir},
    "max_active_runs": 1,
    "catchup": False,
    "default_view": "tree",
    "default_args": {
        "owner": "Airflow",
        "depends_on_past": False,
        "email": ["<EMAIL>", "<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    },
}

with DAG(**dag_params) as dag:

    package_install = BashOperator(
        task_id="package_install",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/download_and_install_package.py""",
    )

    data_wrangling_training = BashOperator(
        task_id="data_wrangling_training",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/training_data_wrangling.py""",
    )

    data_wrangling_inference = BashOperator(
        task_id="data_wrangling_inference",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/inference_data_wrangling.py""",
    )

    model_training = BashOperator(
        task_id="model_training",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/model_training.py""",
    )

    value_inference = BashOperator(
        task_id="value_inference",
        bash_command="""export PYTHONPATH="{{params.workdir}}:$PYTHONPATH"
                            cd {{ params.workdir }}
                            python3 src/models/player_valuation/inference.py""",
    )

    (
        package_install
        >> value_inference
        >> data_wrangling_training
        >> data_wrangling_inference
        >> model_training
    )
