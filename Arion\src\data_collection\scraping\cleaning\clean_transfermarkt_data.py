# existing transfermarkt clean script; TODO adjust reading files part when we move it to a db

import pandas as pd
import numpy as np
from glob import glob
import os
import re
from time import time
from src.helper_funcs import read_config


def conv_str_to_float_col(x):
    pattern = r"\d+[.|,]?\d{0,2}"
    # try:
    if "m" in str(x).lower():
        return float(re.search(pattern, x).group().replace(",", ".")) * 10 ** 6
    elif "th" in str(x).lower() or "k" in str(x).lower():
        return float(re.search(pattern, x).group().replace(",", ".")) * 10 ** 3
    else:
        return x if isinstance(x, float) else float(x.replace(",", "."))

    # except:
    #     print(x)


class FileCombiner:
    def __init__(self, files_path):
        self.files_path = files_path
        self.files_list = None
        self.concat_df = None

    @staticmethod
    def get_all_files(path):
        return glob(os.path.join(path, "*.csv"))

    def get_file_list(self):
        self.files_list = self.get_all_files(self.files_path)

    def concat_files(self):
        self.concat_df = pd.concat(
            [
                pd.read_csv(df, index_col=0)
                for df in self.files_list
                if len(pd.read_csv(df)) > 0
            ]
        )
        self.concat_df = self.concat_df.reset_index(drop=True)


def clean_players(df):
    df = df.copy()
    df["player_birth_date"] = df["player_birth_date"].apply(
        lambda x: str(x).split("(")[0].strip()
    )
    for col in [
        "current_market_value",
        "previous_market_value",
        "player_birth_date",
    ]:
        df[col] = df[col].apply(lambda x: str(x).strip()).replace("-", np.nan)
    df["player_birth_date"] = pd.to_datetime(
        df["player_birth_date"], format="%b %d, %Y", errors="coerce"
    )
    for col in ["current_market_value", "previous_market_value"]:
        df[col] = df[col].apply(conv_str_to_float_col)
    if "team" in df.columns:
        df = df.rename(index=str, columns={"team": "team_id"})
    return df


def clean_teams(df):
    # first row contains full name, so we keep it as a second column to potentially help matching
    df["team_name_full"] = df.groupby("team_id")["team_name"].transform("first")
    # drop dupes
    df = df.drop_duplicates(subset="team_id", keep="last")
    if "league" in df.columns:
        df = df.rename(index=str, columns={"league": "league_id"})
    return df


def clean_leagues(df):
    df = df.rename(index=str, columns={"league_code": "league_id"})
    return df


def prep_table(path, clean_func, export_path, export=True):
    table = FileCombiner(path)
    table.get_file_list()
    table.concat_files()
    table.concat_df = clean_func(table.concat_df)
    if export:
        table.concat_df.to_csv(export_path)
    return table


def main():
    config = read_config("config.yml")["scraping"]["directories"]
    start = time()
    # we using one level higher to clean and then right to the same lvl, e.g.
    #  we load from continents to prep leagues and write to leagues or load from leagues to prep teams
    #  and then write to teams, etc.
    leagues = prep_table(
        config["scraped_continents"],
        clean_leagues,
        f'{config["processed_scraped"]}/leagues_concat.csv',
    )
    teams = prep_table(
        config["scraped_leagues"],
        clean_teams,
        f'{config["processed_scraped"]}/teams_concat.csv',
    )
    players = prep_table(
        config["scraped_teams"],
        clean_players,
        f'{config["processed_scraped"]}/players_concat.csv',
    )
    players_w_team = pd.merge(
        players.concat_df, teams.concat_df, how="left", on="team_id"
    )
    players_w_team_league = pd.merge(
        players_w_team, leagues.concat_df, how="left", on="league_id"
    )
    # deduping as we have guys appear in multiple comps:
    players_w_team_league = players_w_team_league.drop_duplicates(["playerId"])
    players_w_team_league.to_csv(config["cleaned_transfermarkt"])
    print(f"{time()-start} seconds runtime")


if __name__ == "__main__":
    main()
