# Arion #

Arion is a statistical ranking tool for football players. The tool has the following functionalities:

* Custom range of matches and positions
* Custom definitions of player characteristics
* Player comparison across and within leagues
* Contextual player performance based on game difficulty, quality and recency

The tool allows the user to define what they value in a player, how important each player characteristic is, as well as how much they value performance in most recent games to arrive at a custom rating. 

## Methodology ##

Methodology is fairly simple and it can be summarised in 6 steps:

* Take user inputs for the required positions, analysis dimensions and importance for each
* Extract the data for the relevant positions and period
* Compute in-game statistics (frequency, ratio, strength)
* Scale in-game features (i.e. per90 scaling, game difficulty, game quality, game recency)
* __Aggregate to player average features (Average features)__
* Rank the features (ECDF)
* Aggregate ranked features into a player index (Weighted Geometric mean)

Methodology is explained in detail in their relevant Markdown files.

## Data & Tech Stack ##

As of 16/06/2020 we use data from [WyScout!](https://wyscout.com/). We intend to include other data providers in the future.

Tech stack is a combination of the following:

* R - Used for data collection and exploratory analysis
* Python - Core programming language for the system
* Airflow - Automation tool
* PostgreSQL - Database
* Django - User Interface
* Docker - Containerization, __CI/CD__
* Github - Version control

## Roadmap ##

The roadmap can be found here __add link__.
