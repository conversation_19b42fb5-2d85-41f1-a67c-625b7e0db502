from typing import List, Optional, Union
from enum import Enum
from dataclasses import dataclass
import ssl
import smtplib
import traceback

from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

from src.reporting.Reporter import Reporter, ReportContent, ContentType
from settings import GOOGLE_ENSKAI_ALERTS_PASS, OUTLOOK_ENSKAI_ALERTS_PASS
from src.helper_funcs import (
    get_cloud_logger,
    cloud_log_text,
    cloud_log_struct,
)


@dataclass
class MailConfig:
    smtp_server: str
    sender_email: str
    password: str
    port: int = 587


class MailConfigOptions(Enum):
    google_enskai_alerts: MailConfig = MailConfig(
        "smtp.gmail.com", "<EMAIL>", GOOGLE_ENSKAI_ALERTS_PASS
    )
    outlook_enskai_alerts: MailConfig = MailConfig(
        "smtp.office365.com",
        "<EMAIL>",
        OUTLOOK_ENSKAI_ALERTS_PASS,
        587,
    )


class Mailer:
    def __init__(
        self,
        subject: str,
        recipients: List[str],
        contents_list: List[ReportContent],
        mail_config: MailConfig,
    ):
        self.subject = subject
        self.recipients = recipients
        self.contents_list = contents_list
        self.mail_config = mail_config
        self.logger = get_cloud_logger()

        self.message = MIMEMultipart("mixed")
        self.message["Subject"] = self.subject
        self.message["From"] = self.mail_config.sender_email
        self.message["To"] = ", ".join(self.recipients)

    def add_plain_text(self, plain_text: str):
        self.message.attach(MIMEText(plain_text, "plain"))

    def add_body_html(self, body_html: str):
        self.message.attach(MIMEText(body_html, "html"))

    def add_attachment_file(
        self,
        file: Union[str, bytes],
        file_format: str,
        custom_name: Optional[str] = None,
    ):
        if file_format == "csv":
            attachment = MIMEApplication(file)
        else:
            print("excel prepping")
            attachment = MIMEBase("application", "octet-stream")
            attachment.set_payload(file)
            encoders.encode_base64(attachment)

        file_name = (
            custom_name
            if custom_name is not None
            else f"{self.subject}.{file_format}"
        )

        attachment["Content-Disposition"] = f"attachment; filename={file_name}"
        self.message.attach(attachment)

    def add_mail_item(self, item: ReportContent):
        if item.type == ContentType.plain_text:
            self.add_plain_text(item.content)
        elif item.type == ContentType.body_html:
            self.add_body_html(item.content)
        elif item.type == ContentType.attachment_file:
            self.add_attachment_file(item.content, item.file_format)

    def send_mail(self):
        for item in self.contents_list:
            self.add_mail_item(item)
        
        with smtplib.SMTP(
            self.mail_config.smtp_server, self.mail_config.port
        ) as server:
            context=ssl.create_default_context()

            try:
                server.starttls(context=context)
                server.login(
                    self.mail_config.sender_email, self.mail_config.password
                )
                server.sendmail(
                    self.mail_config.sender_email,
                    self.recipients,
                    self.message.as_string(),
                )
                server.close()
                cloud_log_text(self.logger, f"Sent mail for {self.subject}")
            except:
                cloud_log_struct(
                    self.logger,
                    {
                        "action": f"Failed to send mail for {self.subject}",
                        "error": traceback.format_exc(),
                    },
                )


# def main():
#     m = Mailer([], Reporter(), MailConfigOptions.google_enskai_alerts.value)
#     m.send_mail()
