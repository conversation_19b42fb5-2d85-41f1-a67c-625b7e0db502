drop materialized view if exists derived_tables.player_aggregate_stats_view;
create materialized view derived_tables.player_aggregate_stats_view as
(SELECT 
                        ap."playerId", 
                        ap.num_games_last_3years, 
                        ap.total_minutes_last_3years,
                        ap.num_games_last_1year, 
                        ap.total_minutes_last_1year,
                        pp.priority,
                        pp.priority_reason,
                        lec.likely_agent_contract_expiry,
                        COALESCE(esg.speed_groups_text, 'Unknown') AS speed_estimate,
                        pv.values as expected_transfer_value

                    FROM 
                    (SELECT 
                    * 
                    FROM 
                    wyscout.players p
                    LEFT JOIN
                     (
                                select count(l."playerId") as num_games_last_3years, sum(l."minutesTagged") as total_minutes_last_3years, l."playerId"  as plid
								from wyscout.lineups l, 
								wyscout.matches m,
								wyscout.competitions c 
								where m."matchId" = l."matchId" 
								and c."competitionId" = m."competitionId" 
								and c.type = 'club'
								and m.date > ( NOW() - INTERVAL '3 YEARS')::text
								and l."minutesTagged" > 0
								group by l."playerId" 
								) nm
                    ON p."playerId" = nm."plid"
                    LEFT JOIN
                     (
                                select count(l."playerId") as num_games_last_1year, sum(l."minutesTagged") as total_minutes_last_1year, l."playerId"  as plid2
								from wyscout.lineups l, 
								wyscout.matches m,
								wyscout.competitions c 
								where m."matchId" = l."matchId" 
								and c."competitionId" = m."competitionId" 
								and c.type = 'club'
								and m.date > ( NOW() - INTERVAL '1 YEARS')::text
								and l."minutesTagged" > 0
								group by l."playerId" 
								) nm2
                    ON p."playerId" = nm2."plid2"
                    ) ap
                    LEFT JOIN (select distinct "playerId", 1 as priority, priority_reason from derived_tables.priority_players) pp  ON ap."playerId" = pp."playerId"
                    LEFT JOIN (select distinct on ("playerId") * from derived_tables.likely_expiring_agent_contracts) lec ON ap."playerId" = lec."playerId"
                    LEFT JOIN(SELECT distinct "playerId", values FROM 
                    player_valuation.player_value_predictions,
                    (select max(date) as max_date from player_valuation.player_value_predictions) mx
                    WHERE tm_value_used = true
                    and root_transformed = true
                    and date = mx.max_date
                    ) pv
                    ON ap."playerId" = pv."playerId"
                    LEFT JOIN (select distinct on ("playerId") * from derived_tables.estimated_speed_groups order by "playerId", estimation_date desc) esg
                    ON ap."playerId" = esg."playerId");

CREATE INDEX idx_agg_stats_pid
ON derived_tables.player_aggregate_stats_view("playerId");

grant all on derived_tables.player_aggregate_stats_view to elvan, ivan_dimitrov;