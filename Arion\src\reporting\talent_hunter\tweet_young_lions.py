from src.reporting.TweetBot import TweetBot
from src.reporting.talent_hunter.TalentHunter import <PERSON><PERSON>unter


def main():
    hunter = TalentHunter()
    rec = hunter.get_next_record()
    if rec is None:
        return
    content = hunter.generate_content(rec)
    bot = TweetBot()
    try:
        tweet_info = bot.post_tweet(content)
    except Exception as e:
        print(f'failed tweet: {content}')
        raise Exception(e)
    try:
        hunter.save_twitter_post(rec, tweet_info)
    except Exception as e:
        bot.delete_tweet(tweet_info.twitter_id)
        print(e)


if __name__ == "__main__":
    main()
