import glob
import tempfile
from datetime import datetime
import itertools
import math 
import yaml
import json
import numpy as np
import pandas as pd
from scipy import stats
from statsmodels.stats.proportion import binom_test
from statsmodels.distributions.empirical_distribution import ECDF
from google.cloud import storage
from io import String<PERSON>
from pycountry_convert import country_alpha2_to_continent_code, country_alpha3_to_country_alpha2


def read_config(config_file='config.yml'):
    """
    Reads configuration file for rankings from a local reporsitory.

    Args:
        config_file (str, optional): Path to configuration file. Defaults to 'config.yml'.

    Returns:
        Dict: Python dictionary that holds the configuration file.
    """
    with open(config_file, 'r') as yamlfile:
        cfg = yaml.load(yamlfile, Loader=yaml.Loader)
    return cfg

def fast_read_sql(query, cnx):
    """
    Fast reads a given sql query from a SQLAlchemy connection.

    Exploits a temporary CSV to dump the data into. The data is written to a temporary csv file, and then loaded from that temporary file using pandas.

    Args:
        query (String or SQLAlchemy Selectable): Query to be executed on cnx
        cnx (SQLALchemy Engine): Existing SQLAlchemy engine connection to the database with create_engine

    Raises:
        Exception: If query is not a string or sqlalchemy selectable

    Returns:
        DataFrame: Pandas dataframe which holds the query output
    """
    # if query is some sqlalchemy selectable or something, covert to string:
    if not isinstance(query, str):
        try:
            query_compiled = query.compile()
            params = query_compiled.params
            query_s = f'''{str(query_compiled)}'''
            for k,v in params.items():
                if isinstance(v, str):
                    query_s = query_s.replace(':'+str(k), f''' '{v}'  ''')
                else:
                    query_s = query_s.replace(':'+str(k), str(v))
        except Exception as e:
            raise Exception('SQL read is neither string, nor alchemy' 
        'selectable and thus the fast sql read failed')
    else:
         query_s = query
    with tempfile.TemporaryFile() as tmpfile:
        copy_sql = "COPY ({query_s}) TO STDOUT WITH CSV {head}".format(
            query_s=query_s, head="HEADER"
            )
        conn = cnx.raw_connection()
        cur = conn.cursor()
        cur.copy_expert(copy_sql, tmpfile)
        tmpfile.seek(0)
        df = pd.read_csv(tmpfile)
    return df

def load_config(config_file):
    """
    Safe reads configuration file for rankings from a local reporsitory.

    Args:
        config_file (str, optional): Path to configuration file.

    Raises:
        Exception: If the YAML load fails

    Returns:
        Dict: Python dictionary that holds the configuration file.
    """
    with open(config_file, 'r') as stream:
        try:
            return yaml.safe_load(stream)
        except yaml.YAMLError as exc:
            print(exc)


def chunked_iterable(iterable, size):
    """
    Turns an iterable into iterable chunks of given size 

    Args:
        iterable (Iterable): Iterable
        size (int): Chunk size

    Yields:
        Iterable: The chunk of size from iterable
    """

    it = iter(iterable)
    while True:
        chunk = tuple(itertools.islice(it, size))
        if not chunk:
            break
        yield chunk

def empirical_cdf(x, min_v = None, max_v = None):
    """
    Calculates empirical p-values of the data assuming no sampling. Distribution-Free.

    Args:
        x (Numpy Array): Numeric values whose distribution will be computed
        min_v ([type], optional): Minimum assumed value x can take to modulate distribution. Defaults to None.
        max_v ([type], optional): Maximum assumed value x can take to modulate distribution. Defaults to None.

    Returns:
        Numpy Array: Probability values of x in P(X<x)
    """

    x = np.array(x)
    if min_v is not None:
        x = np.append(x, [min_v])

    if max_v is not None:
        x = np.append(x, [max_v])

    ecdf = ECDF(x, side = 'left')
    return ecdf(x)


def binomial_test(x, baseline_p = None, alt = 'smaller'):
    """
    Performs binomial test for proportions.

    This is a one-sided hypothesis test that gives the likelihood of the ratio being greater than baseline_p.
    baseline_p is assumed to be 0.5 unless explicitly provided.

    Args:
        x (Numpy Array): Holds the success frequency and the attempt frequency. Success frequency must be less than or equal to attempt frequency.
        baseline_p (float, optional): Baseline ratio to compare to. Must be in range [0,1]. Defaults to None.
        alt (str, optional): Alternative hypothesis. Accepted values ['two-sided', 'smaller', 'larger']. Defaults to 'smaller'.

    Returns:
        Numpy Array: Binomial proportions test p-values
    """
    
    if baseline_p is None:
        baseline_p = 0.5

    return binom_test(x[0], x[1], prop = baseline_p, alternative = alt)

def calculate_odds(p):
    """
    Takes a probability value and turns it into odds.

    [extended_summary]

    Args:
        p (float): Probability that an event is positive (i.e. The likelihood of player being greater than the rest)

    Raises:
        ValueError: If probability is less than or equal to 0

    Returns:
        float: Odds ratio
    """

    if p > 0:
        odd = p/(1-p)
    else:
        raise ValueError('Probability must be non-zero')

    return odd


def log1p_base(x, base=10):
    """
    Computes logarithm of non-negative values given a logarithmic base. If x == 0, x is assumed to be 1.

    Args:
        x (float): Non-negative number
        base (int, optional): Logarithmic base. Defaults to 10.

    Returns:
        float: Logarithm of given value
    """
    if x == 0:
        x = 1
    return math.log(x, base)


def pvalue_bootstrap_sample(x):
    """
    Calculates p-values of the data assuming bootstrap sampling. Assumes Gaussian distribution approximation.

    Args:
        x (Numpy Array): Numeric values whose distribution will be computed

    Returns:
        Numpy Array: Probability values of x in P(X<x)
    """
    x = (x - x.mean(axis=0)) / (x.std(axis=0) / np.sqrt(x.size))
    return stats.norm.sf(-x)
    

def smooth_prob(p, c = 0.9):
    """
    Smooths the probability values without changing their order
    Useful when probability distribution needs to move further towards a more condensed one (around 0.5)

    Args:
        p (Numpy array): Probability vector to be smoothed
        c (float, optional): Smoothing coefficient. When c = 1, original probability is returned. [0, 1]. Defaults to 0.9.

    Returns:
        Numpy array: Smoothed probability values.
    """

    p_smooth = c * p + (1-c) * ((math.exp(p)/(math.exp(p) + math.exp(1-p))))

    return p_smooth


# def get_derivation_features(config, position, xp_vars=True):
#     """
#     [summary]

#     [extended_summary]

#     Args:
#         config ([type]): [description]
#         position ([type]): [description]
#         xp_vars (bool, optional): [description]. Defaults to True.

#     Returns:
#         [type]: [description]
#     """
#     pos_features = list(config['positions_weights'][position].keys())
#     derivation_features_subset = {feat:
#                                   config['advanced_variables_definitions'][feat] for feat in pos_features}
#     denoms = [derivation_features_subset[x]['denominator'] for x in
#               pos_features]
#     numers = [derivation_features_subset[x]['numerator'] for x in
#               pos_features]
#     xp_vars = [derivation_features_subset[x]['exp_var'] for x in
#                pos_features] if xp_vars else []
#     features_list = denoms+numers+xp_vars
#     flat_feat_list = []
#     for x in features_list:
#         if isinstance(x, list):
#             flat_feat_list = flat_feat_list + [var for var in x]
#         else:
#             flat_feat_list.append(x)
#     features_set = list(set([x for x in flat_feat_list if str(x) != 'nan'
#                               and x is not None]))
#     return features_set


# def get_timedelta(x):
#     return (x-x.min())/np.timedelta64(1, "D")


# def get_progression(var, timedeltas=None):
#     if  timedeltas is not None:
#         x = timedeltas
#     else:
#         x = np.arange(len(var))      
#     y = np.array(var)
#     return np.polyfit(x, y, 1)[0]


# def get_consistency(var, roll_window, detrend=False):
#     raw = var.copy()
#     var = var.dropna().rolling(window=roll_window, center=True).mean().dropna()
#     if not detrend:
#         return np.std(var)
#     else:
#         trend = get_progression(var)
#         detrended = raw - np.arange(len(raw))*trend
#         return np.std(detrended)

def get_time_stamp(ms=False):
    """
    Helper function to turn timestamp into string


    Args:
        ms (bool, optional): Whether to return microsecond. Defaults to False.

    Returns:
        string: Current timestamp in string
    """
    now = datetime.now()
    if not ms:
        return f'''{now.year}{now.month}{now.day
        }_{now.hour}{now.minute}'''
    return   f'''{now.year}{now.month}{now.day
        }_{now.hour}{now.minute}{now.second}{now.microsecond}'''


def concat_batch_files(path, extension='.csv', encoding=None):
    """
    Concatenates batches of same file.

    Args:
        path (string): File path
        extension (str, optional): File extension. Defaults to '.csv'.
        encoding (string, optional): File encoding. Defaults to None.

    Returns:
        DataFrame: Concatenated data frame
    """
    if not path.endswith('/'):
        path = path+'/'
    files = [x for x in  glob.glob(f'{path}*{extension}')]
    df_list = [pd.read_csv(x, encoding=encoding) for x in files]
    df_concat = pd.concat(df_list, sort=True,axis=0).reset_index(drop=True)
    return df_concat

# def get_dupes(df, col):
#     dups = df[col].value_counts()[df[col].value_counts()>1].index
#     duped = df[df[col].isin(dups)]
#     return duped

def calculate_pvalue(x):
    """
    Calculates p-values of the data assuming no sampling. Assumes Gaussian distribution.

    Args:
        x (Numpy Array): Numeric values whose distribution will be computed

    Returns:
        Numpy Array: Probability values of x in P(X<x)
    """
    x = (x - x.mean(axis=0)) / (x.std(axis=0))
    return stats.norm.sf(-x)

def trim_array(x, lower_censor = 0.05, upper_censor = 0.05):
    """
    Helper function to trim extreme probability values.


    Args:
        x (Numpy array): Array of probabilities.
        lower_censor (float, optional): Lower threshold of extreme probabilities. Defaults to 0.05.
        upper_censor (float, optional): Upper threshold of extreme probabilities. Defaults to 0.05.

    Returns:
        Numpy array: Trimmed probability array
    """
    pvals = calculate_pvalue(x)
    x = x[(pvals >= lower_censor) & (pvals <= 1-upper_censor)]
    return x

def min_max_scale(x, min_value = 0, max_value = 1, trim_data = False, lower_censor = None, upper_censor = None):
    """

    Scales the array within a min_value-max_value range.

    If upper and lower censor values are provided along with trim_data = True, the probability values are trimmed.
    Otherwise, the min-max scaling is applied directly according to formula below.

    m\mapsto \frac{m-r_{\text{min}}}{r_{\text{max}}-r_{\text{min}}}\times (t_{\text{max}}-t_{\text{min}}) + t_{\text{min}}


    Args:
        x (Array-like): Array of values to be scaled between min_value-max_value.
        min_value (int, optional): Minimum desired value for the array. Defaults to 0.
        max_value (int, optional): Maximum desired value for the array. Defaults to 1.
        trim_data (bool, optional): Whether to trim data. Defaults to False.
        lower_censor ([type], optional): Lower censor value to trim probability values. Defaults to None.
        upper_censor ([type], optional): Upper censor value to trim probability values. Defaults to None.

    Returns:
        Array-like: Array of scaled values
    """
    

    if trim_data:
        if lower_censor is None:
            if upper_censor is None:
                xx = trim_array(x)
            else:
                xx = trim_array(x, upper_censor = upper_censor)
        elif upper_censor is None:
            xx = trim_array(x, lower_censor = lower_censor)
        else:
            assert lower_censor + upper_censor < 1
            xx = trim_array(x, lower_censor = lower_censor, upper_censor = upper_censor)
    else:
        xx = x
    
    min_value_obs = np.min(xx)
    max_value_obs = np.max(xx)     

    scaled_x = min_value + (max_value - min_value) * (x - min_value_obs) / (max_value_obs - min_value_obs)
    return scaled_x


def ratings_via_weighted_geometric_mean(df, var_weights, vars_to_include, offset = 1):
    """
    Calculates player ratings via geometric mean.

    Args:
        df (DataFrame): Data frame of feature ratings (min-max scaled feature values.)
        var_weights (Array-like): An array of feature importance weights. 
        vars_to_include (List of strings): The variable names to include in rating
        offset (int, optional): Offset for the logarithm to deal with 0 values. Higher the value, lower the actual feature importance. Defaults to 1.

    Returns:
        DataFrame: Modified data frame with a new `player_ratings` column.
    """
    df_tmp = df.copy()
    for feature in vars_to_include:
        df_tmp[feature] =  np.log(df_tmp[feature] + offset)*var_weights[feature]
    df['player_rating'] = np.exp(df_tmp.sum(axis=1) / np.sum(list(var_weights.values())))
    return df

def datetime_to_int(dt):
    """
    Changes datetime to UNIX Timestamp.

    Args:
        dt (Datetime): Date in python Datetime format

    Returns:
        int: Unix time equivalent of dt
    """
    return int(dt.strftime("%Y%m%d%H%M%S"))


def read_var_defs_from_db(config, cnx_prod):
    """
    Reads advanced variable definitions from the database.

    Advanced variable definitions are now held in a semi-structured table as JSON objects. This function accesses the database to retreive pre defined variables.

    Args:
        config (Dict): Skeleton configuration dictionary to hold variable definitions among other fields.
        cnx_prod (SQLALchemy connection): Connection to database

    Returns:
        Dict: config dictionary with `advanced_variables_definitions` field.
    """
    var_df = pd.read_sql('select * from tmp_var_definitions', cnx_prod)
    var_df.definition = var_df.definition.map(json.loads)
    var_df.index = var_df.variable_name
    var_dict = var_df.to_dict()
    config['advanced_variables_definitions'] = var_dict['definition']

    return config

def fix_trailing_char(str_array, chars_to_remove = '_'):
    """
    Fix trailing characters in an array of strings

    Args:
        str_array (Array-like, str): Array of strings from which the trailing characters will be removed
        chars_to_remove (str, optional): Characters to remove. Defaults to '_'.

    Returns:
        Array-like, str: Clean string array
    """
    chars_to_remove = ''.join(chars_to_remove)
    str_array = [str_val.rstrip(chars_to_remove) for str_val in str_array]
    return str_array


def download_blob(bucket_name, source_blob_name, destination_file_name):
    """Downloads a blob from the bucket."""
    # bucket_name = "your-bucket-name"
    # source_blob_name = "storage-object-name"
    # destination_file_name = "local/path/to/file"

    storage_client = storage.Client(project='footballanalytics')

    bucket = storage_client.bucket(bucket_name)

    # Construct a client side representation of a blob.
    # Note `Bucket.blob` differs from `Bucket.get_blob` as it doesn't retrieve
    # any content from Google Cloud Storage. As we don't need additional data,
    # using `Bucket.blob` is preferred here.
    blob = bucket.blob(source_blob_name)
    blob.download_to_filename(destination_file_name)

    print(
        "Blob {} downloaded to {}.".format(
            source_blob_name, destination_file_name
        )
    )

def upload_blob(bucket_name, source_file_name, destination_blob_name):
    """Uploads a file to the bucket."""
    # The ID of your GCS bucket
    # bucket_name = "your-bucket-name"
    # The path to your file to upload
    # source_file_name = "local/path/to/file"
    # The ID of your GCS object
    # destination_blob_name = "storage-object-name"

    storage_client = storage.Client(project='footballanalytics')
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)

    blob.upload_from_filename(source_file_name)

    print(
        "File {} uploaded to {}.".format(
            source_file_name, destination_blob_name
        )
    )

def fast_write_sql(df, table_name, engine, schema='public', if_exists='replace', sep='\x01',  encoding='utf-8', dtype=None):
    # Check if table exists:
    existance = engine.execute(f'''SELECT EXISTS(
    SELECT * 
    FROM information_schema.tables 
    WHERE 
      table_schema = '{schema}' AND 
      table_name = '{table_name}'); ''').first()[0]
    # if table doesnt exists or we overwrite, write first row:
    if (existance and if_exists == 'replace') or not existance:
         df[:0].to_sql(table_name, engine, if_exists=if_exists,
                        index=False, schema=schema, dtype=dtype)

    # Prep:
    output = StringIO()
    df.to_csv(output, header=False, encoding=encoding, index=False, sep=sep)
    output.seek(0)
    # Insert:
    connection = engine.raw_connection()
    cursor = connection.cursor()
    schema_tablename = f'{schema}.{table_name}'
    cursor.copy_from(output, schema_tablename, sep=sep, null='')
    connection.commit()
    cursor.close()


def get_final_file_from_gcloud(folder_name):
    storage_client = storage.Client(project='footballanalytics')
    file_names = []
    creation_date = []
    # folder_name = 'package_files/'
    for item in storage_client.list_blobs('player-valuation', prefix=folder_name):
        file_names.append(item.name)
        creation_date.append(item.time_created)

    file_metadata = pd.DataFrame({'file_names':file_names,
        'file_date':creation_date})
    file_metadata = file_metadata[file_metadata['file_names'] != folder_name]
    file_metadata['file_names'] = file_metadata['file_names'].str.replace(folder_name, '')
    file_metadata = file_metadata.sort_values('file_date', ascending=False).reset_index()
    source_blob_name = file_metadata['file_names'][0]
    return source_blob_name

def get_continent(col):
    try:
        cn_a2_code =  country_alpha3_to_country_alpha2(col)
    except:
        cn_a2_code = 'Unknown' 
    try:
        cn_continent = country_alpha2_to_continent_code(cn_a2_code)
    except:
        cn_continent = 'Unknown' 
    return cn_continent

def parse_json(myjson):
    """
    Parses json string into a dictionary.
    \f
    Args:
        myjson (JSON String): JSON string that mirrors the sample config.

    Raises:
        HTTPException: If not json

    Returns:
        Dict: Parsed dictionary from the json input
    """

    try:
        json_object = json.loads(myjson)
        
    except ValueError as e:
        raise Exception('Config must either be JSON array or Python Dictionary')

    return json_object