import json
from typing import Union, List

from sqlalchemy import types
import pandas as pd
import numpy as np

from src.data_collection.wyscout.v2.Updater import Updater


class TransfersUpdater(Updater):
    def __init__(self, table_name):
        super().__init__(table_name)
        self.base_url = "https://apirest.wyscout.com/v3/players/_ID/transfers"
        self.object_type = "transfers"
        self.id_name = "transferId"
        self.if_exists = "append"
        self.dtype = {
            "announceDate": types.Date,
            "startDate": types.Date,
            "endDate": types.Date,
            "fromTeamId": types.Integer,
            "toTeamId": types.Integer,
        }
        self.prepped_ids_list = []

    def prep(self, results):
        df = pd.DataFrame(results)
        df = df.replace("", np.nan).replace("0000-00-00", np.nan)
        for col, col_type in self.dtype.items():
            if col_type == types.Integer:
                df[col] = df[col].replace("", np.nan).astype("Int64")
        return df

    def extract_payload_from_resp(
        self, resp: str, code: int
    ) -> Union[dict, List[dict]]:
        """This is required because of bullshit wyscout response structure so we dont have
        to have 5 different process_response where 99% of code is repeated
        """
        payload = json.loads(resp)
        # if there is no transfers array, we just return None that will get filtered out:
        return payload.get("transfer")
